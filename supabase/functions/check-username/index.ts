import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts'; // Paylaşılan CORS ayarları için

console.log('check-username function started.');

// Gelişmiş rate limiting için IP tabanlı veri yapısı
const ipTracker: Record<string, {
  count: number,               // İstek sayısı
  timestamp: number,           // Son istek zamanı
  violations: number,          // Kural ihlali sayısı
  blockedUntil: number | null  // Engelleme bitiş zamanı
}> = {};

// Rate limiting yapılandırması
const RATE_LIMIT = 8;          // Zaman penceresi içinde izin verilen maksimum istek
const RATE_WINDOW = 60 * 1000; // Temel zaman penceresi (60 saniye)
const MIN_USERNAME_LENGTH = 4; // Minimum kullanıcı adı uzunluğu
const CLEANUP_INTERVAL = 30 * 60 * 1000; // IP kayıtlarını temizleme aralığı (30 dakika)

// Sürekli ihlal edenlere artan bekleme süresi - 2^violations dakika
const getBlockDuration = (violations: number): number => {
  return Math.min(Math.pow(2, violations) * 60 * 1000, 24 * 60 * 60 * 1000); // Maximum 24 saat
};

// Belirli aralıklarla eski kayıtları temizle (memory leak önlemi)
setInterval(() => {
  const now = Date.now();
  for (const ip in ipTracker) {
    // 30 dakikadan eski kayıtları temizle (bloke olmayanlar)
    if (ipTracker[ip].blockedUntil === null && (now - ipTracker[ip].timestamp) > CLEANUP_INTERVAL) {
      delete ipTracker[ip];
    }
    // Bloke süresi dolan kayıtları temizle
    else if (ipTracker[ip].blockedUntil !== null && ipTracker[ip].blockedUntil < now) {
      delete ipTracker[ip];
    }
  }
}, CLEANUP_INTERVAL);

serve(async (req) => {
  // CORS için preflight kontrol
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // IP bazlı rate limiting kontrolü
    const clientIP = req.headers.get('x-forwarded-for') || 'unknown';
    const now = Date.now();
    
    // IP henüz takip edilmiyorsa yeni kayıt oluştur
    if (!ipTracker[clientIP]) {
      ipTracker[clientIP] = { 
        count: 0, 
        timestamp: now, 
        violations: 0, 
        blockedUntil: null 
      };
    }
    
    // Engelleme kontrolü
    if (ipTracker[clientIP].blockedUntil !== null && ipTracker[clientIP].blockedUntil > now) {
      const remainingTime = Math.ceil((ipTracker[clientIP].blockedUntil - now) / 1000);
      return new Response(
        JSON.stringify({ 
          error: `Too many requests. Please try again later.`,
          retryAfter: remainingTime
        }),
        { 
          status: 429, 
          headers: { 
            ...corsHeaders, 
            'Content-Type': 'application/json',
            'Retry-After': remainingTime.toString()
          } 
        }
      );
    }
    
    // Zaman penceresi kontrolü
    if ((now - ipTracker[clientIP].timestamp) > RATE_WINDOW) {
      // Zaman penceresi dışında - sıfırla
      ipTracker[clientIP].count = 1;
      ipTracker[clientIP].timestamp = now;
    } else {
      // Zaman penceresi içinde - artır
      ipTracker[clientIP].count++;
      
      // Rate limit aşıldıysa engelle
      if (ipTracker[clientIP].count > RATE_LIMIT) {
        // İhlal sayısını artır
        ipTracker[clientIP].violations++;
        
        // Engelleme süresini hesapla (artan şekilde)
        const blockDuration = getBlockDuration(ipTracker[clientIP].violations);
        ipTracker[clientIP].blockedUntil = now + blockDuration;
        
        // Engelleme yanıtı
        const retryAfter = Math.ceil(blockDuration / 1000);
        return new Response(
          JSON.stringify({ 
            error: `Rate limit exceeded. Too many requests.`,
            retryAfter: retryAfter
          }),
          { 
            status: 429, 
            headers: { 
              ...corsHeaders, 
              'Content-Type': 'application/json',
              'Retry-After': retryAfter.toString()
            } 
          }
        );
      }
    }
    
    // İstek gövdesini JSON olarak almayı dene
    let username: string | undefined;
    try {
      const body = await req.json();
      username = body.username;
    } catch (jsonError) {
      console.error('Error parsing request body:', jsonError);
      return new Response(JSON.stringify({ error: 'Invalid request body. JSON expected.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }
    
    console.log('Received username check request for:', username);

    // Gelen username'i doğrula - minimum karakter kontrolü artırıldı
    if (!username || typeof username !== 'string' || username.length < MIN_USERNAME_LENGTH) {
      return new Response(JSON.stringify({ 
        error: `Geçerli bir kullanıcı adı gerekli (en az ${MIN_USERNAME_LENGTH} karakter).`
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Kullanıcı adının başındaki @ işaretini kaldır (varsa)
    const cleanedUsername = username.startsWith('@') ? username.substring(1) : username;
    // Veritabanına kaydedilecek format (@ ile başlayan)
    const dbUsername = `@${cleanedUsername}`;

    // Supabase istemcisini Service Role Key ile başlat
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { auth: { persistSession: false } }
    );

    console.log(`Checking database for username: ${dbUsername}`);

    // Optimizasyon: Veritabanında sadece kullanıcı adının varlığını kontrol et
    const { data: profileData, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('username')
      .eq('username', dbUsername)
      .maybeSingle(); 
      
    if (profileError) {
        console.error('Database error checking username:', profileError);
        return new Response(JSON.stringify({ error: 'Database error checking username.' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
        });
    }

    const exists = profileData !== null;
    console.log(`Username ${dbUsername} exists: ${exists}`);

    // Başarılı sonucu döndür
    return new Response(
      JSON.stringify({ exists }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (err) {
    // Genel/beklenmedik hatalar için
    console.error('Error in check-username function:', err);
    return new Response(JSON.stringify({ error: err.message || 'Internal Server Error' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
}); 