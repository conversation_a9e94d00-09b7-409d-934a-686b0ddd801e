# Projeyi Çalıştırma

## Geliştirme (Önerilen Akış)
İki terminalle çalışın:

1) Terminal A (Worker)
```
cd worker
wrangler dev
```

2) Terminal B (Frontend)
```
cd app
npm run dev
```

- Vite dev server, `/api` isteklerini 8787’deki Worker’a proxy’ler; SPA + API aynı origin gibi çalışır.
- Eğer `ECONNREFUSED 127.0.0.1:8787` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Worker tarafında `wrangler dev` çalışmıyordur.

## Neden Cloudflare Vite plugin dev’de kapalı?
- `@cloudflare/vite-plugin` dev modda app klasöründe `wrangler.(toml|json)` arar.
- Bizde wrangler config `worker/` içinde olduğundan, app içinde arayıp hata veriyordu.
- Bu yüzden dev’de plugin’i kapattık. <PERSON>şlev ka<PERSON>ı yok; `/api` proxy üzerinden Worker’a bağlanıyoruz.

İsterseniz plugin’i dev’de de kullanabiliriz; o zaman plugin’e `worker/wrangler.toml` yolunu açıkça vermek gerekir. Talep ederseniz ben ayarlayabilirim.

## Alternatif (Opsiyonel): Tek terminal ile geliştirme
- İsterseniz Vite proxy’yi local 8787 yerine prod Worker’a yönlendiririz.
- Böylece sadece `npm run dev` yeter; API çağrıları prod’daki Worker’a gider.
- Canlı veriye çağrı yapılacağı için geliştirme sırasında dikkat edilmelidir.

## Production Yayın
1) SPA build
```
cd app
npm run build
```
2) Worker deploy
```
cd worker
wrangler deploy
```
3) Production URL
- https://ikrakitabe.keremkozyigit.workers.dev

## Çevre Değişkenleri (Frontend)
Sadece Supabase bilgileri gerekli (Worker URL / API key yok):
```
VITE_SUPABASE_URL=...supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOi...

# Opsiyonel
VITE_DEBUG=true
```

---


İki terminalle çalışın:
1. Terminal A
cd worker
wrangler dev

2. Terminal B
cd app
npm run dev
Bu şekilde Vite dev server, /api çağrılarını 8787’deki Worker’a proxy’ler ve her şey same-origin gibi çalışır.