# İkra Kitabe - <PERSON><PERSON><PERSON>u

## Temel Mimari <PERSON>

- **Domain-Driven Design (DDD)**: Kodun iş domainleri etrafında organize edilmesi
- **Atomic Design**: Atom, molekül ve organizmalardan oluşan komponent hiyerarşisi
- **Tip Güvenliği**: Tüm kod tabanında güçlü TypeScript tiplendirmesi
- **State Yönetimi**: Zustand ile domain bazlı state yönetimi (auth, reader, settings, library) ve React hooks ile component-level state yönetimi
- **Responsive Tasarım**: Tüm ekran boyutlarına uyumlu UI
- **Güvenlik**: Supabase Auth ile kimlik doğrulama ve Supabase Database'de Row Level Security (RLS) ile veritabanı yetkilendirmesi
- **Performans Optimizasyonu**: Sayfa yükleme deneyimini iyileştiren teknikler ve gereksiz API çağrılarını önleme

## Proje <PERSON> bölüm, `app/src` dizininin dosya ve klasör yapısını gösterir.

```plaintext
app/src
├── App.css
├── App.tsx
├── assets
│   └── react.svg
├── domains
│   ├── auth                # Kimlik doğrulama, kullanıcı profilleri
│   │   ├── hooks
│   │   │   ├── useAuth.ts
│   │   │   ├── useAuthForm.ts
│   │   │   └── useUsernameCheck.ts # Ortak kullanıcı adı kontrol hook'u
│   │   ├── models
│   │   │   └── types.ts
│   │   ├── pages
│   │   │   ├── Components
│   │   │   │   ├── AuthSheet.tsx
│   │   │   │   ├── LoginForm.tsx
│   │   │   │   ├── ProfileSheet.tsx # Profil görüntüleme/düzenleme
│   │   │   │   ├── SignupForm.tsx
│   │   │   │   └── UsernameSheet.tsx  # Google giriş sonrası kullanıcı adı girişi
│   │   │   ├── ResetPasswordPage.tsx
│   │   │   └── UsernameSetupPage.tsx # Profil tamamlama sayfası
│   │   ├── services
│   │   │   └── authService.ts
│   │   └── store
│   │       └── authStore.ts
│   ├── library             # Kitap ve kategori yönetimi
│   │   ├── hooks
│   │   │   ├── useBookService.ts
│   │   │   └── useCategoryService.ts
│   │   ├── models
│   │   │   └── types.ts
│   │   ├── pages
│   │   └── store
│   │       └── librarystore.ts
│   ├── reader              # İçerik görüntüleme, okuma deneyimi
│   │   ├── hooks
│   │   │   ├── index.ts
│   │   │   ├── quran
│   │   │   │   └── useSurahs.ts # fetchData kullanıyor
│   │   │   ├── risale
│   │   │   │   └── useRisaleSection.ts # fetchData kullanıyor
│   │   │   └── useReaderInteractivity.ts
│   │   ├── models
│   │   │   └── types.ts
│   │   ├── pages
│   │   │   ├── quran
│   │   │   │   ├── ChaptersPage.tsx
│   │   │   │   └── ReadPage.tsx
│   │   │   └── risale
│   │   │       ├── ChapterSelection.tsx
│   │   │       └── ReadPage.tsx
│   │   ├── services
│   │   │   └── readersettingsservice.ts
│   │   └── store
│   │       └── readerstore.ts
│   ├── reader-interactions # Şerh, vurgu, yer imleri (feature-based organization)
│   │   ├── annotations     # Şerh (Note) sistemi
│   │   ├── highlights      # Vurgulama sistemi
│   │   ├── bookmarks       # Yer imi sistemi
│   │   ├── text-selection  # Metin seçimi altyapısı
│   │   └── shared          # Ortak component'ler
│   └── settings
│       ├── models
│       │   └── types.ts
│       ├── services
│       │   └── settingsservice.ts
│       └── store
│           └── settingsstore.ts
├── shared
│   ├── components          # Paylaşılan UI bileşenleri
│   │   ├── Icons
│   │   │   └── GoogleIcon.tsx
│   │   └── Atoms, Molecules, Organisms
│   ├── context
│   │   └── ThemeContext.tsx
│   ├── hooks               # Paylaşılan hook'lar
│   │   ├── useIsMobile.ts
│   │   └── autooverlay.ts  # Otomatik arka plan karıştırma
│   ├── theme
│   │   └── definitions.ts
│   └── utils
│       ├── dataFetcher.ts  # Merkezi veri getirme (statik JSON)
│       └── supabaseClient.ts # Supabase istemcisi
├── styles
│   └── index.css
├── docs                    # Detaylı sistem dokümantasyonları
│   ├── ANNOTATION_SYSTEM.md
│   ├── BOOKMARK_SYSTEM.md
│   └── HIGHLIGHTING_SYSTEM.md
└── vite-env.d.ts
```

## Domain İçerikleri

### Auth Domain

- **Sorumluluk:** Kullanıcı kimlik doğrulama, oturum yönetimi ve kullanıcı profili işlemlerini yönetir.
- **Teknoloji:** Supabase (Auth, Database, PostgreSQL Functions/Triggers).
- **Supabase Entegrasyonu:**
    - **İstemci:** `[supabaseClient.ts](mdc:app/src/shared/utils/supabaseClient.ts)`.
    - **Servis:** `[authService.ts](mdc:app/src/domains/auth/services/authService.ts)` Supabase Auth API'lerini ve `public.profiles` tablosu işlemlerini yönetir.
        - `signup` metodu, `options: { data: { username: '...', display_name: '...' } }` ile ilk profil bilgilerini (`username`, `display_name`) `raw_user_meta_data`'ya aktarır. Veritabanı trigger'ı ile anahtar isimlerinin eşleşmesi kritiktir.
    - **Google OAuth:** Desteklenir. Google ile ilk girişte `username` `NULL` kalır, kullanıcı profil tamamlama sayfasına (`[UsernameSheet.tsx](mdc:app/src/domains/auth/pages/Components/UsernameSheet.tsx)`) yönlendirilir.
    - **Veritabanı:**
        - `auth.users`: Temel kullanıcı bilgileri ve `raw_user_meta_data` (JSONB).
        - `public.profiles`: Uygulamaya özgü profil bilgileri (`id`, `username` UNIQUE, `display_name`, `avatar_url`, `updated_at`). `id`, `auth.users.id`'ye bağlıdır.
    - **Otomatik Profil Oluşturma (Trigger):**
        - `public.handle_new_user` PostgreSQL trigger'ı, `auth.users`'a yeni kayıt sonrası `NEW.id` ve `NEW.raw_user_meta_data`'dan aldığı verilerle `public.profiles`'a kayıt ekler.
        - Fonksiyon `SECURITY DEFINER` olmalıdır.
    - **RLS:** `public.profiles` tablosunda, kullanıcıların kendi verilerini okuma/güncelleme politikaları aktiftir.
    - **Username Kontrolü:**
        - `check-username` Edge Function'ı ile (`[authService.ts](mdc:app/src/domains/auth/services/authService.ts)` üzerinden).
        - Güvenlik iyileştirmeleri: Rate limiting, minimum uzunluk (4 karakter), kötüye kullanım cezaları (2^ihlal_sayısı dakika).
        - IP bazlı izleme ve 24 saate kadar engelleme sistemi.
        - `useUsernameCheck.ts` hook'u ile ortak kullanıcı adı kontrol işlevi.
- **State Yönetimi (`[authStore.ts](mdc:app/src/domains/auth/store/authStore.ts)`):
    - Kullanıcı, profil, kimlik doğrulama durumu (`isAuthenticated`), `isUsernameSetupRequired` bayrağını yönetir.
    - `isUsernameSetupRequired`: Normal kayıtta genellikle `false` (metadata'dan `username` alınır); Google girişinde `true` olur.
    - `signup` action'ı, kayıt sonrası iyimser (optimistic) profil güncellemesi yapar, sonra asenkron olarak veritabanından kesin profili çeker.
    - Optimizasyon: localStorage'da token varlığına bakarak hızlı ilk durum kontrolü, gereksiz API çağrılarını önleme
- **UI Etkileşimi:**
    - `[useAuth.ts](mdc:app/src/domains/auth/hooks/useAuth.ts)` hook'u ile state/action erişimi.
    - `useLayoutEffect` ile sayfa yüklenirken ilk auth kontrolünün render'dan önce yapılması
    - `[LoginButton.tsx](mdc:app/src/shared/components/atoms/Button/LoginButton.tsx)`: Sayfa yenilenirken "Giriş Yap" yazısının anlık görünmesini önleyen yükleme durumu yönetimi
    - `[AuthSheet.tsx](mdc:app/src/domains/auth/pages/Components/AuthSheet.tsx)` ve içindeki formlar.
    - `[ProfileSheet.tsx](mdc:app/src/domains/auth/pages/Components/ProfileSheet.tsx)`: Düzenleme ve görüntüleme modları, kalem ikonu ile alan başına düzenleme

### Library Domain

- **Sorumluluk:** Kitap ve kategori verilerini yönetme
- **Veri Kaynağı:** Statik JSON dosyaları (`/public/data/`)
- **İşlevler:**
  - Kitap listeleme ve kategorilere göre filtreleme
  - `useBookService` ve `useCategoryService` hook'ları ile veri erişimi

### Reader Domain

- **Sorumluluk:** İçerik görüntüleme ve okuma deneyimi
- **İçerik Tipleri:** Kuran, Risale-i Nur ve Tafsir
- **İşlevler:**
  - Okuma pozisyonu ve ayarları yönetme
  - Farklı içerik görüntüleme modları
  - `CombinedVerseData` ile ayet verilerini UI'a aktarma
- **Veri Yükleme:**
  - Statik JSON verileri `fetchData` ile erişim
  - Chunk bazında ayet/çeviri verileri yükleme

### Reader-Interactions Domain

**Sorumluluk:** Metin üzerinde şerh, vurgu ve yer imi işlemleri

**📚 Detaylı Dokümantasyon:**
- [Annotation Sistemi](app/docs/ANNOTATION_SYSTEM.md) - Şerh sistemi detayları
- [Bookmark Sistemi](app/docs/BOOKMARK_SYSTEM.md) - Koleksiyon bazlı yer imleri
- [Highlighting Sistemi](app/docs/HIGHLIGHTING_SYSTEM.md) - Vurgulama ve görselleştirme

**🏗️ Ana Bileşenler:**
- **TextSelectionHandler**: Metin seçimi ve annotation koordinasyonu (multi-sentence support)
- **TextActionMenu**: Seçilen metin için eylem menüsü (Şerh, Vurgula, Yer İmi, Şerh Bul, Temizle)
- **TextHighlighter**: HTML-aware highlighting + Arabic text support
- **ColorPicker**: 8 renk + background/text styles + temizle butonu
- **AnnotationSheet**: Annotation oluşturma/düzenleme formu (tab-based)
- **BookmarkBottomSheet**: Koleksiyon bazlı bookmark oluşturma
- **AnnotationSearchSheet**: Annotation arama ve filtreleme ("Şerh Bul")

**🗄️ Veritabanı Yapısı:**
- **text_annotations**: Ana annotation tablosu
  - Temel: `id`, `user_id`, `book_id`, `section_id`, `annotation_type`
  - Pozisyon: `selection_start/end`, `selected_text`, `sentence_id` (JSONB)
  - Recovery: `prefix_text`, `suffix_text`, `word_proximity`, `text_hash`, `sentence_hash`
  - İçerik: `annotation_content`, `color`, `highlight_style`
  - Sosyal: `is_public`, `tags`, `metadata`
  - Bookmark: `collection_id` (bookmark_collections referansı)
- **bookmark_collections**: Bookmark koleksiyonları
  - Temel: `id`, `user_id`, `name`, `description`
  - Görsel: `color`, `icon`
  - Sistem: `is_default`

**🎨 Annotation Türleri:**
1. **Note (Şerh)**: Detaylı açıklamalar (mavi noktalı alt çizgi + 📝 icon)
2. **Highlight (Vurgulama)**: Görsel vurgulama (8 renk, background/text stil + 🎨 icon)
3. **Bookmark (Yer İmi)**: Koleksiyon bazlı işaretleme (vurgulanmaz + 📖 icon)

**🔍 Pozisyon Sistemi:**
- **Çoklu Pozisyon Stratejisi**: 5 katmanlı kurtarma algoritması
- **Flat Structure**: Performans için düz veritabanı yapısı
- **Multi-Sentence Support**: Çoklu cümle seçimi (max 20)
- **Recovery Algorithm**: Metin değişikliklerine dayanıklı eşleştirme

**🎨 Görselleştirme (TextHighlighter v2):**
- **HTML-Aware Processing**: Bold, italic, span tag'leri korunur
- **Arabic Text Support**: Font-size, dotted underlines, tooltips preserved
- **Multi-Style Support**: Background + text color combinations
- **Z-Index Algoritması**: Küçük aralık = üstte, yeni = üstte
- **Segmentation**: Character-level segment building
- **Data Attributes**: `data-annotation-id` for deletion operations

**🔧 Hook'lar:**
- **useAnnotationManager**: Ana annotation CRUD + smart operations
- **useCollectionManager**: Bookmark koleksiyon yönetimi
- **useTextSelectionHandler**: Metin seçimi koordinasyonu (multi-sentence)
- **useReaderInteractionStyles**: Theme-compatible styling

**🚀 Performans:**
- **Lazy Loading**: Görünür annotation'ları yükleme
- **Memoization**: Ağır hesaplamaları önbelleğe alma
- **Debounced Search**: Arama sorgularını geciktirme
- **Optimistic Updates**: Hızlı UI güncellemeleri

**🔒 Güvenlik:**
- **Row Level Security (RLS)**: Kullanıcı bazlı veri erişimi
- **Input Validation**: XSS koruması ve sanitization
- **Rate Limiting**: API çağrılarında sınırlama

**📱 Kullanıcı Deneyimi:**
- **Responsive Design**: Mobil/desktop uyumlu arayüz
- **Accessibility**: Keyboard navigation ve screen reader desteği
- **Error Handling**: Kullanıcı dostu hata mesajları
- **Loading States**: Yükleme durumu göstergeleri

### Settings Domain

- **Sorumluluk:** Uygulama ayarları ve kullanıcı tercihleri
- **İşlevler:** Tema, dil ve diğer konfigürasyonları yönetme
- **Depolama:** LocalStorage'da kullanıcı tercihlerini saklama

## Veri Akışı

1. **Veri Kaynakları**:
   - **Supabase:** Kimlik bilgileri, profiller, şerhler
   - **Statik JSON:** Kuran, Risale metinleri, kitap meta verileri
   - **LocalStorage:** Kullanıcı tercihleri, okuma pozisyonları

2. **Veri Erişimi**:
   - **Supabase İşlemleri:** `authService.ts` üzerinden erişim
   - **Statik Veri:** `fetchData` fonksiyonu ile merkezi erişim
   - **Chunk Erişimi:** Özel hook'lar ile verimli veri yükleme

3. **State Yönetimi**:
   - **Zustand Mağazaları:** Domain bazlı state yönetimi
   - **Custom Hook'lar:** İş mantığı ve veri erişimi
   - **Bileşen State'i:** Lokal UI durumu

## Kimlik Doğrulama Akışı

1. **Başlangıç Kontrolü:**
   - Uygulama başlatıldığında mevcut oturum kontrolü
   - `useLayoutEffect` ile render'dan önce hızlı localStorage kontrolü
2. **Giriş/Kayıt:** Form veya Google OAuth ile kimlik doğrulama
3. **Google Entegrasyonu:** İlk giriş yapanlar için kullanıcı adı sorgusu
4. **Profil Verisi:** `profiles` tablosunda saklanan kullanıcı bilgileri
5. **Korumalı İşlevler:** Yetkilendirme gerektiren işlemlerde kimlik kontrolü
6. **UI Geçişleri:** Yükleme durumunda düzgün UI geçişleri, titreşim ve anlık yanıp sönmeleri önleme

## Komponent Mimarisi

### Atomic Design Metodolojisi
- **Atoms**: Temel UI öğeleri (Button, Input, Icon)
- **Molecules**: Atom grupları (SearchBar, BookCard)
- **Organisms**: Karmaşık UI bölümleri (Header, ExpandableSection)
- **Sayfalar**: Tam sayfa deneyimleri.

## İmport Stratejisi

Kod tabanında iki import yaklaşımı kullanılmaktadır:

1. **Göreceli Yollar**:
   ```typescript
   import { Something } from '../../models/types';
   ```

2. **Alias Paths** (tsconfig.json ve vite.config.ts'de tanımlı):
   ```typescript
   import { supabase } from '@shared/utils/supabaseClient';
   import { useAuthStore } from '@domains/auth/store/authStore';
   ```

Okunabilirlik için **alias path**'lerin kullanımı teşvik edilir.

## Kod Standartları

### İsimlendirme Kuralları
- **Dosyalar**: Bileşenler için PascalCase, servisler/hook'lar için camelCase
- **Bileşenler**: PascalCase
- **Fonksiyonlar/Hook'lar**: camelCase
- **Arayüzler/Tipler**: PascalCase (`I` ön eki kullanılabilir: `IBook`)
- **Veritabanı**: snake_case (Supabase/PostgreSQL konvansiyonu)
- **CSS**: kebab-case veya TailwindCSS class'ları

### Bileşen Yapısı
- TypeScript arayüzleri ile işlevsel bileşenler
- Props destructuring
- Hook'lar başta
- Yardımcı fonksiyonlar ve JSX return ifadesi

## Performans Optimizasyonu

- Memoizasyon (`useMemo`, `useCallback`)
- Büyük listeler için sanallaştırma
- Merkezi veri önbellekleme
- Gereksiz re-render'ları önleme
- Erken state belirleme (localStorage kontrolü ile gereksiz API çağrılarını önleme)
- `useLayoutEffect` ile kritik UI durumları için erken kontrol

## Hata Yönetimi

- **ErrorBoundary**: React bileşenlerinde oluşan hataları yakalayan ve kullanıcı dostu bir hata mesajı gösteren sınıf bileşeni
- **NetworkStatusMonitor**: Ağ bağlantısı durumunu izleyen ve bağlantı kesildiğinde kullanıcıya bildirim gösteren bileşen
- **EnhancedErrorState**: Farklı hata türlerine göre özelleştirilmiş mesajlar ve eylemler sunan gelişmiş bir hata durumu bileşeni
- **useErrorHandler**: Hataları yakalayan, kategorize eden ve uygun şekilde işleyen hook
- **useSafeAsync**: Asenkron işlemleri güvenli bir şekilde yöneten, hata yönetimi ve yeniden deneme mekanizmaları içeren hook
- **R2FetchError**: Özelleştirilmiş hata sınıfı ile daha ayrıntılı hata bilgileri
- **Hata Türleri**: Ağ bağlantısı, içerik bulunamama, sunucu, ayrıştırma ve genel hatalar için özelleştirilmiş yönetim

## Duyarlı Tasarım

- Mobil öncelikli yaklaşım
- Flexbox/Grid layout'lar
- Duyarlı metin/aralıklar
- TailwindCSS ile medya sorguları

## Geliştirme İş Akışı

1. Domain ve model tanımlama
2. Veritabanı şeması ve RLS politikaları oluşturma
3. Servis katmanı ve OAuth yapılandırması
4. State yönetimi (store) ekleme
5. UI bileşenlerini Atomic Design ile geliştirme
6. Test ve optimizasyon

## Güvenlik Önlemleri

1. **Edge Functions Güvenliği**:
   - Rate limiting (IP bazlı)
   - Artan ceza süreleri (üstel artış: 2^ihlal_sayısı dakika)
   - Input validasyonu (minimum uzunluk, karakter kontrolü)
   - 24 saate kadar engelleme mekanizması

2. **Auth Güvenliği**:
   - Row Level Security (RLS) politikaları
   - Token yönetimi ve yenileme stratejileri
   - Çıkış işleminde kapsamlı temizlik

## Kullanıcı Deneyimi İyileştirmeleri

1. **Auth Durumu Geçişleri**:
   - Sayfa yenilendiğinde "Giriş Yap" yazısının anlık görünmesini önleme
   - Yükleme durumunda spinner gösterme
   - Küçük gecikmelerle UI geçişlerini pürüzsüzleştirme

2. **Profil Düzenleme**:
   - Görüntüleme ve düzenleme modları
   - Her alan için kalem ikonu ile düzenleme
   - İptal ve Kaydet butonları
   - Temiz ve kullanıcı dostu arayüz