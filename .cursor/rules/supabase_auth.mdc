---
description:
globs:
alwaysApply: false
---
# Supabase Authentication and Profile Management

This document outlines the structure and key components for handling user authentication and profiles using Supabase in this project.

## Core Supabase Client

The main Supabase client is initialized in `[supabaseClient.ts](mdc:app/src/shared/utils/supabaseClient.ts)`. It uses environment variables (`VITE_SUPABASE_URL`, `VITE_SUPABASE_ANON_KEY`) for configuration.

## Auth Domain (`@domains/auth`)

All authentication and profile-related logic is encapsulated within the `auth` domain.

- **Service (`[authService.ts](mdc:app/src/domains/auth/services/authService.ts)`):** Handles all interactions with Supabase Auth (login, signup, logout) and the `public.profiles` database table (fetching/updating user profiles).
- **Store (`[authStore.ts](mdc:app/src/domains/auth/store/authStore.ts)`):** A Zustand store managing the global authentication state, including the authenticated user (`user` object from Supabase Auth) and their profile data (`profile` object fetched from the `profiles` table).
- **Hooks:**
    - `[useAuth.ts](mdc:app/src/domains/auth/hooks/useAuth.ts)`: Provides easy access to the auth state (user, profile, isLoading, isAuthenticated) and actions (login, logout, etc.) for UI components. It also listens for `onAuthStateChange` to keep the state updated and fetches the user profile.
    - `[useAuthForm.ts](mdc:app/src/domains/auth/hooks/useAuthForm.ts)`: Contains hooks (`useLoginForm`, `useSignupForm`) for managing form state, validation, and submission logic for the login and signup forms.
- **Models (`[types.ts](mdc:app/src/domains/auth/models/types.ts)`):** Defines TypeScript interfaces for authentication data (e.g., `IUserProfile`, `ISignupData`).
- **UI Components (`[AuthSheet.tsx](mdc:app/src/domains/auth/pages/Components/AuthSheet.tsx)`, `[LoginForm.tsx](mdc:app/src/domains/auth/pages/Components/LoginForm.tsx)`, `[SignupForm.tsx](mdc:app/src/domains/auth/pages/Components/SignupForm.tsx)`):** Reusable components for the authentication user interface, typically shown in a sheet/modal.
    - The `[LoginButton.tsx](mdc:app/src/shared/components/atoms/Button/LoginButton.tsx)` in `@shared/components` uses the `useAuth` hook to display either "Giriş Yap" or the user's display name/username.

## Profile Management (`public.profiles` Table)

- User profile data (unique `username`, `display_name`, `avatar_url`, etc.) is stored in a separate `public.profiles` table in the Supabase database, linked to `auth.users` via the user's `id`.
- **Automatic Profile Creation:** A PostgreSQL trigger (`handle_new_user`) automatically creates a row in `profiles` when a new user signs up. It populates the profile using data passed in the `options.data` field during `supabase.auth.signUp` (handled by `authService.signup`).
- **Security (RLS):** Row Level Security policies are enforced on the `profiles` table to ensure users can only insert/update their own profile, while allowing public read access.

## Application Integration

- The main application component (`[App.tsx](mdc:app/src/App.tsx)`) uses the `useAuthStore` (via `useAuth` hook implicitly) to check the authentication status on initial load.
- The `[AppNavbar.tsx](mdc:app/src/shared/components/molecules/Navigation/AppNavbar.tsx)` component includes the `LoginButton` and logic to open the `AuthSheet`.
