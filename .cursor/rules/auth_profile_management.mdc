---
description:
globs:
alwaysApply: false
---
# Authentication and User Profile Management Guide

This project uses Supabase for user authentication and manages extended user profile information in a separate database table.

## Core Components:

1.  **Supabase Authentication (`auth.users` table):**
    *   Handles basic user registration (email/password, OAuth like Google) and session management.
    *   Stores core user data and `raw_user_meta_data` which can include initial profile details passed during signup.

2.  **`public.profiles` Table:**
    *   A custom table in the `public` schema used to store application-specific user profile data such as `username`, `display_name`, and `avatar_url`.
    *   Linked to `auth.users` via the user's `id` (UUID).

3.  **Database Trigger (`handle_new_user`):**
    *   An SQL function (`public.handle_new_user`) is triggered `AFTER INSERT ON auth.users`.
    *   This function is responsible for creating a corresponding row in `public.profiles` for each new user.
    *   It populates `public.profiles` using data from `NEW.id`, `NEW.email`, and fields extracted from `NEW.raw_user_meta_data` (like `username`, `display_name`).
    *   **Crucial:** This trigger must have `SECURITY DEFINER` privileges to access `auth.users` data and write to `public.profiles`.

4.  **Frontend Service (`[app/src/domains/auth/services/authService.ts](mdc:app/src/domains/auth/services/authService.ts)`):**
    *   Contains functions to interact with Supabase for authentication (e.g., `login`, `signup`, `signInWithGoogle`) and profile operations (`getUserProfile`, `updateProfile`).
    *   The `signup` method in this service is key for passing initial `username` and `display_name` to the `raw_user_meta_data` via `options: { data: { ... } }`.

5.  **Frontend State Management (`[app/src/domains/auth/store/authStore.ts](mdc:app/src/domains/auth/store/authStore.ts)`):**
    *   A Zustand store that manages the application's authentication state (`isAuthenticated`, `user`, `profile`).
    *   Includes logic to determine if a user needs to complete their profile (e.g., set a username), primarily through the `isUsernameSetupRequired` flag.
    *   Actions like `signup`, `login`, and `checkAuth` orchestrate calls to `authService.ts` and update the store state. Timing of profile fetching here is critical to avoid race conditions.

6.  **Profile Completion Page (`/profil-tamamla`):**
    *   A dedicated page for users who sign up via methods that don't initially provide a username (like Google OAuth) or if their initial profile setup through the trigger didn't fully complete the `username`.
    *   The visibility of this page is controlled by `authStore.isUsernameSetupRequired`.

7.  **Row Level Security (RLS) on `public.profiles`:**
    *   RLS is typically enabled on the `profiles` table.
    *   Ensure appropriate policies are in place to allow users to `SELECT` (read) their own profile and `UPDATE` their own profile. Missing or incorrect RLS policies can lead to errors (e.g., HTTP 406) when accessing profile data.
    *   Example policies:
        ```sql
        CREATE POLICY "Users can read their own profile."
        ON public.profiles FOR SELECT USING (auth.uid() = id);

        CREATE POLICY "Users can update their own profile."
        ON public.profiles FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);
        ```
