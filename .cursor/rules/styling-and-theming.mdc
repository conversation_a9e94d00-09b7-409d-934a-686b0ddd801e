---
description:
globs:
alwaysApply: false
---
# Styling and Theming

This project utilizes Tailwind CSS for utility-first styling, supplemented by global styles and a custom theme context.

## Tailwind CSS

*   **Configuration:** Tailwind is configured in [tailwind.config.js](mdc:app/tailwind.config.js). This file defines custom settings, including:
    *   Theme extensions (colors, fonts like `font-arabic`).
    *   Plugins (e.g., `tailwindcss-animate`).
*   **Usage:** Tailwind utility classes are applied directly in JSX elements within components for rapid UI development.
*   **Purging:** Unused styles are automatically purged in production builds for optimized CSS output.

## Global Styles

*   **File:** Global CSS rules are defined in [src/styles/index.css](mdc:app/src/styles/index.css).
*   **Content:** This file includes:
    *   Tailwind's base, components, and utilities directives (`@tailwind`).
    *   Font imports (`@import url(...)`).
    *   Base HTML element styles (e.g., `html`, `body`).
    *   Definition of CSS variables for theming (e.g., `--background`, `--foreground`, `--primary`, `--text-color`, `--text-inverse`). These variables are used by Tailwind utility classes (like `bg-background`, `text-foreground`) and can be dynamically updated by the theme context.

## Theme Context

*   **Provider:** [src/shared/context/ThemeContext.tsx](mdc:app/src/shared/context/ThemeContext.tsx) provides the theme state (e.g., 'light', 'dark', 'sepia') and functions to update it across the application.
*   **Integration:** It likely wraps the root component ([src/App.tsx](mdc:app/src/App.tsx)) to make the theme accessible globally.
*   **Dynamic Styling:** The context updates CSS variables defined in `index.css` based on the selected theme, causing Tailwind utility classes and custom styles using these variables to adapt accordingly.
*   **Theme Definitions:** The specific color values for each theme (light, dark, sepia) are likely defined in a separate file, potentially [src/shared/theme/definitions.ts](mdc:app/src/shared/theme/definitions.ts).

## Auto Overlay Hook

*   **Functionality:** The hook [src/shared/hooks/autooverlay.ts](mdc:app/src/shared/hooks/autooverlay.ts) calculates appropriate overlay colors (e.g., for text backgrounds or highlights) based on the current theme's background color. This ensures sufficient contrast and readability regardless of the active theme.
*   **Usage:** Components needing adaptive overlay colors utilize this hook to get theme-aware color values.
