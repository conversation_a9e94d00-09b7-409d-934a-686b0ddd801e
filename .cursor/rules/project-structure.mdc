---
description:
globs:
alwaysApply: false
---
# <PERSON><PERSON> Kitabe Project Structure

İkra Kitabe is a React-based web application for reading Islamic content, particularly the Quran and Risale-i Nur collection.

## Main Directories

- `app/src/domains` - Contains domain-specific code organized by business context
- `app/src/shared` - Contains reusable components, hooks, and utilities
- `app/src/assets` - Contains static assets like images and icons
- `app/src/services` - Contains shared services used across domains
- `app/src/styles` - Contains global styles and CSS utilities

## Key Domains

- **Library Domain**: Manages books and categories in [app/src/domains/library](mdc:app/src/domains/library)
- **Reader Domain**: Handles content viewing and reading experience in [app/src/domains/reader](mdc:app/src/domains/reader)
- **Settings Domain**: Manages application settings in [app/src/domains/settings](mdc:app/src/domains/settings)

## Entry Points

- The main application entry point is [app/src/main.tsx](mdc:app/src/main.tsx)
- The main app component is [app/src/App.tsx](mdc:app/src/App.tsx)

## Shared Components 

The project uses Atomic Design methodology with components organized as:
- Atoms: Basic UI elements in [app/src/shared/components/atoms](mdc:app/src/shared/components/atoms)
- Molecules: Combinations of atoms in [app/src/shared/components/molecules](mdc:app/src/shared/components/molecules)
- Organisms: Complex UI sections in [app/src/shared/components/organisms](mdc:app/src/shared/components/organisms)
