---
description: Error handling architecture and best practices
globs: 
alwaysApply: false
---
# Error Handling

İkra Kitabe implements a comprehensive error handling system to provide users with a better experience when encountering issues, especially during content loading.

## Core Components

### Error Boundary

[app/src/shared/components/organisms/ErrorBoundary/ErrorBoundary.tsx](mdc:app/src/shared/components/organisms/ErrorBoundary/ErrorBoundary.tsx)

- Class component that catches errors in React component tree
- Prevents entire app from crashing when a component fails
- Displays user-friendly error messages
- Supports custom fallback components
- Provides retry functionality

### Network Status Monitor

[app/src/shared/components/organisms/NetworkStatusMonitor/NetworkStatusMonitor.tsx](mdc:app/src/shared/components/organisms/NetworkStatusMonitor/NetworkStatusMonitor.tsx)

- Monitors network connectivity status
- Shows notifications when connection is lost
- Automatically detects when connection is restored
- Customizable offline message

### Enhanced Error State

[app/src/shared/components/molecules/Feedback/EnhancedErrorState.tsx](mdc:app/src/shared/components/molecules/Feedback/EnhancedErrorState.tsx)

- Displays user-friendly error messages based on error type
- Supports different error types: network, content, auth, permission, general
- Provides retry functionality
- Supports custom actions and messages
- Shows technical details in development mode

## Error Handling Hooks

### useErrorHandler

[app/src/shared/hooks/useErrorHandler.ts](mdc:app/src/shared/hooks/useErrorHandler.ts)

- Categorizes errors by type
- Provides consistent error handling across the application
- Includes utility functions for error detection
- Maintains error state with context information

### useSafeAsync

[app/src/shared/hooks/useSafeAsync.ts](mdc:app/src/shared/hooks/useSafeAsync.ts)

- Wraps async operations with error handling
- Provides loading, error, and data states
- Includes automatic retry mechanism
- Handles component unmounting gracefully
- Supports success and error callbacks

## Custom Error Types

### R2FetchError

[app/src/shared/utils/r2DataFetcher.ts](mdc:app/src/shared/utils/r2DataFetcher.ts)

- Custom error class for R2 data fetching
- Includes error type, status code, and original error
- Categorizes errors as network, not found, server, parse, or unknown
- Provides detailed error messages for better debugging

## Implementation in Pages

- All routes wrapped with ErrorBoundary in App.tsx
- Content loading errors handled with EnhancedErrorState
- Network connectivity monitored throughout the application
- Specific error handling for Quran and Risale content

## Best Practices

1. **Use Error Boundaries** for component-level error catching
2. **Categorize errors** by type for better user feedback
3. **Provide retry mechanisms** when appropriate
4. **Show user-friendly messages** based on error context
5. **Monitor network connectivity** and notify users of issues
6. **Include technical details** only in development mode
7. **Handle async operations safely** with proper loading and error states
