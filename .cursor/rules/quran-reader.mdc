---
description:
globs:
alwaysApply: false
---
# Quran Reader Module

The Quran reader is a core functionality of <PERSON>kra Kitabe, allowing users to read and interact with Quranic content in various formats.

## Main Components

- **ReadPage**: Main page component for displaying Quran content [app/src/domains/reader/pages/quran/ReadPage.tsx](mdc:app/src/domains/reader/pages/quran/ReadPage.tsx)
- **ChaptersPage**: Page for browsing and selecting surahs [app/src/domains/reader/pages/quran/ChaptersPage.tsx](mdc:app/src/domains/reader/pages/quran/ChaptersPage.tsx)

## Content Display Components

- **VerseContent**: Renders individual verses with different view modes [app/src/domains/reader/pages/quran/Components/VerseContent.tsx](mdc:app/src/domains/reader/pages/quran/Components/VerseContent.tsx)
- **VerseList**: Manages the list of verses [app/src/domains/reader/pages/quran/Components/VerseList.tsx](mdc:app/src/domains/reader/pages/quran/Components/VerseList.tsx)
- **SurahHeader**: Displays the surah title and information [app/src/domains/reader/pages/quran/Components/SurahHeader.tsx](mdc:app/src/domains/reader/pages/quran/Components/SurahHeader.tsx)

## Navigation Components

- **NavigationSheet**: Modal for navigating between surahs and verses [app/src/domains/reader/pages/quran/Components/NavigationSheet.tsx](mdc:app/src/domains/reader/pages/quran/Components/NavigationSheet.tsx)
- **BreadcrumbNavigation**: Shows current location in the content [app/src/domains/reader/pages/quran/Components/BreadcrumbNavigation.tsx](mdc:app/src/domains/reader/pages/quran/Components/BreadcrumbNavigation.tsx)

## Display Mode Components

- **ContentModeSelector**: Controls the content display mode [app/src/domains/reader/pages/quran/Components/ContentModeSelector.tsx](mdc:app/src/domains/reader/pages/quran/Components/ContentModeSelector.tsx)
- **MealSelector**: Controls which translations to display [app/src/domains/reader/pages/quran/Components/MealSelector.tsx](mdc:app/src/domains/reader/pages/quran/Components/MealSelector.tsx)

## Custom Hooks

- **useContentMode**: Manages content display modes [app/src/domains/reader/hooks/quran/usecontentmode.ts](mdc:app/src/domains/reader/hooks/quran/usecontentmode.ts)
- **useNavigationSheet**: Manages navigation UI state [app/src/domains/reader/hooks/quran/usenavigationsheet.ts](mdc:app/src/domains/reader/hooks/quran/usenavigationsheet.ts)
- **useVerseScroll**: Handles scrolling to specific verses [app/src/domains/reader/hooks/quran/useversescroll.ts](mdc:app/src/domains/reader/hooks/quran/useversescroll.ts)
- **useVerseHighlight**: Manages verse highlighting [app/src/domains/reader/hooks/quran/useversehighlight.ts](mdc:app/src/domains/reader/hooks/quran/useversehighlight.ts)

## Content Modes

The reader supports four content display modes:
1. Arabic Only (mode '1')
2. Arabic with Translation (mode '2') 
3. Word-by-word Translation (mode '3')
4. Translation Only (mode '4')
