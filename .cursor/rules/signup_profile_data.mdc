---
description:
globs:
alwaysApply: false
---
# New User Signup and Profile Data Flow

This rule details how initial profile data (like `username` and `display_name`) is passed during user signup and processed by the database trigger.

## Process:

1.  **Frontend Signup (`[app/src/domains/auth/services/authService.ts](mdc:app/src/domains/auth/services/authService.ts)`):
    *   The `signup` function in `authService.ts` calls `supabase.auth.signUp()`.
    *   Crucially, it passes initial profile data (e.g., `username`, `display_name` from the registration form) within the `options: { data: { ... } }` object of the `signUp` call.
    *   Example from `authService.ts`:
        ```typescript
        // options: {
        //   data: {
        //     username: formattedUsername, 
        //     display_name: displayName || '' // Note: key is 'display_name' (snake_case)
        //   }
        // }
        ```

2.  **Supabase (`auth.users.raw_user_meta_data`):
    *   Supabase stores the content of the `options.data` object from the `signUp` call into the `raw_user_meta_data` JSONB column of the `auth.users` table for the new user.

3.  **Database Trigger (`handle_new_user`):
    *   The `public.handle_new_user` SQL function is triggered after a new row is inserted into `auth.users`.
    *   This function reads values from `NEW.raw_user_meta_data` to populate the corresponding new row in the `public.profiles` table.
    *   Example of how it accesses data:
        ```sql
        -- Inside handle_new_user function:
        -- profile_username := NEW.raw_user_meta_data->>'username';
        -- profile_display_name := NEW.raw_user_meta_data->>'display_name'; 
        ```

## Key for Successful Data Transfer:

*   **Matching Keys:** The keys used in the `options.data` object in the frontend `authService.ts` (`signup` function) **must exactly match** the keys a_string_var = """Hello World!""" 
a_second_one = '''How's life?'''
another = "Yo!"the `handle_new_user` database trigger expects when extracting data from `NEW.raw_user_meta_data`.
    *   For instance, if the frontend sends `options.data: { displayName: 'Test User' }` (camelCase) but the trigger expects `NEW.raw_user_meta_data->>'display_name'` (snake_case), the `display_name` in the `profiles` table will be `NULL`.
    *   This was a common source of issues, and it was resolved by ensuring `authService.ts` sends `display_name` (snake_case).

This flow is critical for ensuring that data entered during registration is correctly reflected in the user's profile from the outset.
