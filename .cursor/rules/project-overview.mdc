---
description: 
globs: 
alwaysApply: false
---
# Project Overview

This project is a web application built with **Vite**, **React**, and **TypeScript**.

## Key Technologies

*   **Framework:** [React](mdc:https:/reactjs.org)
*   **Build Tool:** [Vite](mdc:https:/vitejs.dev) ([vite.config.ts](mdc:app/vite.config.ts))
*   **Language:** [TypeScript](mdc:https:/www.typescriptlang.org) ([tsconfig.json](mdc:app/tsconfig.json))
*   **Styling:** [Tailwind CSS](mdc:https:/tailwindcss.com) ([tailwind.config.js](mdc:app/tailwind.config.js)) & Global CSS ([src/styles/index.css](mdc:app/src/styles/index.css))
*   **Routing:** [React Router DOM](mdc:https:/reactrouter.com) (Configuration in [src/App.tsx](mdc:app/src/App.tsx))
*   **State Management:** [Zustand](mdc:https:/github.com/pmndrs/zustand) (Stores typically located in `src/domains/{domainName}/store/`)
*   **Linting:** ESLint ([eslint.config.js](mdc:app/eslint.config.js))

## Architectural Patterns

*   **Domain-Driven Design (DDD):** The codebase is organized into distinct business domains within the `src/domains/` directory (e.g., `library`, `reader`, `settings`). Each domain contains its specific data, models, services, UI pages/components, hooks, and state stores. See also [ARCHITECTURE.md](mdc:app/ARCHITECTURE.md).
*   **Atomic Design:** Shared UI components located in `src/shared/components/` are structured following Atomic Design principles:
    *   `atoms`: Basic building blocks (e.g., Button, Input).
    *   `molecules`: Combinations of atoms (e.g., SearchBar, BookCard).
    *   `organisms`: More complex UI sections composed of molecules and atoms (e.g., PageLayout, AppNavbar).

## Entry Point & Core Files

*   **Main Entry:** [src/main.tsx](mdc:app/src/main.tsx) initializes the React application.
*   **Root Component:** [src/App.tsx](mdc:app/src/App.tsx) sets up global providers (like ThemeProvider), defines routes, and renders the main application layout.
*   **Shared Resources:** Reusable logic and UI elements are found in `src/shared/`.
*   **Static Data:** Data like Quran text or book metadata seems to be stored as `.ts` files within domain-specific `data` folders (e.g., `src/domains/reader/data/quran/`) and loaded dynamically using Vite's `import.meta.glob`.

This structure promotes separation of concerns and modularity.
