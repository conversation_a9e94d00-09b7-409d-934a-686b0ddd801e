---
description:
globs:
alwaysApply: false
---
# Coding Standards

This document outlines the coding standards and patterns used in the İkra Kitabe project.

## TypeScript Usage

- All files use TypeScript with strict typing
- Interfaces and types are defined in domain-specific `models/types.ts` files
- Type exports follow standard patterns:
  ```typescript
  export interface IEntity {
    id: number;
    slug?: string;
  }
  export type ContentMode = '1' | '2' | '3' | '4';
  ```

## Component Structure

- React Functional Components with hooks
- Props interfaces defined at the top of files
- Components are memoized when necessary for performance
- Explicit return type annotations (React.FC<Props>)

Example pattern:
```typescript
interface ComponentProps {
  // props definition
}

export const Component: React.FC<ComponentProps> = ({ 
  // destructured props
}) => {
  // component logic
  return (
    // JSX
  );
};
```

## Custom Hooks

- Custom hooks use the 'use' prefix
- Hook dependencies are properly declared in dependency arrays
- Return values are typed explicitly
- Hooks are focused on a single responsibility

## Styling Approach

- TailwindCSS for utility-based styling
- CSS variables for theming (--text-color, --bg-color)
- Custom color utilities (autoHue, autoOverlay)
- Responsive design with mobile-first approach

## State Management

- Zustand for global state
- React useState/useRef for component-local state
- Props drilling minimized with context where appropriate

## File Organization

- Domain-driven design with clear separation of concerns
- Atomic Design for component hierarchy
- Related files grouped in dedicated folders
