---
description:
globs:
alwaysApply: false
---
# İkra Kitabe Architecture

## Core Principles

- **Domain-Driven Design (DDD)**: Code organized around business domains
- **Atomic Design**: Component hierarchy of atoms, molecules, and organisms
- **TypeScript**: Strong typing throughout the codebase
- **State Management**: Zustand for domain-based state (settings, reader UI, user session)
- **Supabase**: Authentication, database with Row Level Security (RLS)

## Project Structure

```
app/src/
├── domains/                     # Domain-specific code
│   ├── auth/                    # Authentication & user profiles
│   ├── library/                 # Book library & categories
│   ├── reader/                  # Content viewing experience
│   └── reader-interactions/     # Annotations system
│
├── shared/                      # Shared utilities
│   ├── components/              # Reusable UI components
│   ├── context/                 # Global contexts (Theme)
│   ├── hooks/                   # Shared hooks
│   └── services/                # API services
```

## Domain Responsibilities

### Auth Domain
- User authentication (Supabase Auth)
- User profiles (profiles table)
- Google OAuth integration
- Password reset flow
- Session management

### Library Domain
- Book and category management
- Listing and filtering books
- Static data from JSON files

### Reader Domain
- Content rendering (<PERSON>, <PERSON><PERSON><PERSON>)
- Navigation controls
- Reading position tracking
- Content modes (different layouts)

### Reader-Interactions Domain
- Annotations & highlights
- Bookmarks
- Text selection handling

## Data Sources

- **Supabase**: Auth and PostgreSQL database
- **Static JSON**: Content files in `/public/data/`
- **LocalStorage**: User preferences and reading positions

## Data Flow

1. **Data Access**:
   - `authService.ts` for Supabase operations
   - `dataFetcher.ts` for static content
   - Custom hooks for chunk-based loading

2. **State Management**:
   - Domain stores (`authStore.ts`, `readerstore.ts`, etc.)
   - Custom hooks for business logic
   - Component state for local UI

## Authentication Flow

1. Initial load checks existing session
2. Login/signup via form or Google OAuth
3. New users (Google) prompted for username if needed
4. Profile data stored in `profiles` table
5. Auth store updates global state
6. Protected features check auth status

## Component Architecture

- **Atoms**: Basic UI elements (Button, Input)
- **Molecules**: Groups of atoms (SearchBar, BookCard, ErrorState, EnhancedErrorState)
- **Organisms**: Complex UI sections (Header, ExpandableSection, ErrorBoundary, NetworkStatusMonitor)
- **Pages**: Complete experiences

## Error Handling Architecture

- **ErrorBoundary**: Class component that catches errors in React component tree
- **NetworkStatusMonitor**: Monitors network connectivity and shows notifications
- **EnhancedErrorState**: Displays user-friendly error messages based on error type
- **Error Types**: Network, content, authentication, permission, and general errors
- **Error Hooks**: useErrorHandler and useSafeAsync for consistent error management
- **R2FetchError**: Custom error class with detailed error information

## Import Strategy

```typescript
// Alias paths (preferred)
import { supabase } from '@shared/utils/supabaseClient';
import { useAuthStore } from '@domains/auth/store/authStore';

// Relative paths
import { Something } from '../../models/types';
```
