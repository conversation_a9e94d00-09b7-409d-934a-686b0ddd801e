---
description: 
globs: 
alwaysApply: false
---
# Quran Reader Structure

The main page for reading the Quran is [ReadPage.tsx](mdc:app/src/domains/reader/pages/quran/ReadPage.tsx).

- It fetches verse data using the [useVerseLoader.ts](mdc:app/src/domains/reader/hooks/quran/useverseloader.ts) hook, which combines Arabic text, word-by-word data (Mode 3), and translations from [useTranslationLoader.ts](mdc:app/src/domains/reader/hooks/quran/useTranslationLoader.ts).
- It gets Surah information using [usesurahs.ts](mdc:app/src/domains/reader/hooks/quran/usesurahs.ts).
- It manages the display mode (Arabic only, with translations, etc.) using [usecontentmode.ts](mdc:app/src/domains/reader/hooks/quran/usecontentmode.ts).
- It uses the Zustand store [readerstore.ts](mdc:app/src/domains/reader/store/readerstore.ts) for user settings (like selected translators) and UI state (like footnote expansion).
- It renders the list of verses using [VerseList.tsx](mdc:app/src/domains/reader/pages/quran/Components/VerseList.tsx).
- Navigation between Surahs and Verses is handled by [NavigationSheet.tsx](mdc:app/src/domains/reader/pages/quran/Components/NavigationSheet.tsx).
- Core data types like `CombinedVerseData` are defined in [types.ts](mdc:app/src/domains/reader/models/types.ts).
