---
description: 
globs: 
alwaysApply: false
---
# Quran Content Display Modes

The display of Quran verses is handled by a set of components based on the selected `ContentMode`:

- **[VerseContent.tsx](mdc:app/src/domains/reader/pages/quran/Components/VerseContent.tsx):** This component acts as a router. It receives the `viewMode` prop and conditionally renders one of the specific mode components below.
- **[Mode1Content.tsx](mdc:app/src/domains/reader/pages/quran/Components/Mode1Content.tsx):** Displays only the Arabic text with verse numbers (using `font-arabic` and the `toArabicNumeral` helper from [formatters.ts](mdc:app/src/shared/utils/formatters.ts)).
- **[Mode2Content.tsx](mdc:app/src/domains/reader/pages/quran/Components/Mode2Content.tsx):** Displays Arabic text (`font-arabic`) along with translations. It uses [TranslationDisplay.tsx](mdc:app/src/domains/reader/pages/quran/Components/TranslationDisplay.tsx) for the translation part. Verse number style is similar to Mode 3.
- **[Mode3Content.tsx](mdc:app/src/domains/reader/pages/quran/Components/Mode3Content.tsx):** Displays word-by-word analysis (Arabic word + meaning) and translations below (using [TranslationDisplay.tsx](mdc:app/src/domains/reader/pages/quran/Components/TranslationDisplay.tsx)).
- **[Mode4Content.tsx](mdc:app/src/domains/reader/pages/quran/Components/Mode4Content.tsx):** Displays only translations, using [TranslationDisplay.tsx](mdc:app/src/domains/reader/pages/quran/Components/TranslationDisplay.tsx).

- **[TranslationDisplay.tsx](mdc:app/src/domains/reader/pages/quran/Components/TranslationDisplay.tsx):** A reusable component responsible for rendering selected translations and handling footnote expansion logic (using the Zustand store [readerstore.ts](mdc:app/src/domains/reader/store/readerstore.ts)).
