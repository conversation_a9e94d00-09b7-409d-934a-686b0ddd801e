---
description:
globs:
alwaysApply: false
---
# State Management

İkra <PERSON>abe uses Zustand for global state management, with state split by domain concerns.

## Store Structure

Each domain has its own dedicated store:

- **Library Store**: [app/src/domains/library/store/librarystore.ts](mdc:app/src/domains/library/store/librarystore.ts)
  - Manages books and categories data
  - Handles search functionality
  - Tracks loading states for data fetching

- **Reader Store**: [app/src/domains/reader/store/readerstore.ts](mdc:app/src/domains/reader/store/readerstore.ts)
  - Manages reading preferences and settings
  - Tracks currently selected content
  - Handles bookmarks and reading progress

- **Settings Store**: [app/src/domains/settings/store/settingsstore.ts](mdc:app/src/domains/settings/store/settingsstore.ts)
  - Manages application-wide settings
  - Handles theme preferences
  - Manages user interface preferences

## Local Component State

In addition to Zustand stores, React's useState and useReducer are used for component-specific state:

- UI interaction states (dropdowns, modals)
- Form input values
- Temporary display preferences

## Custom Hooks

Custom hooks abstract common state management patterns:

- Content mode management: [app/src/domains/reader/hooks/quran/usecontentmode.ts](mdc:app/src/domains/reader/hooks/quran/usecontentmode.ts)
- Navigation state: [app/src/domains/reader/hooks/quran/usenavigationsheet.ts](mdc:app/src/domains/reader/hooks/quran/usenavigationsheet.ts)

## Data Loading and Persistence

- Data is loaded from static files in the data directory
- User preferences are persisted in localStorage

## Error Handling

- **useErrorHandler**: Custom hook for error categorization and handling
- **useSafeAsync**: Hook for safe async operations with retry mechanism
- **Error Types**: Categorized as network, content, auth, permission, and general
- **Error States**: Managed in component state or domain stores
- **Error UI Components**: EnhancedErrorState for user-friendly error messages
- **Global Error Boundary**: Catches unhandled errors in React component tree
- **Network Status Monitoring**: Detects and notifies users of connectivity issues
