---
description:
globs:
alwaysApply: false
---
# İkra Kitabe Annotation System (Şerh Sistemi)

This document provides a comprehensive overview of the annotation system implemented in the İkra Kitabe application.

## Domain Structure

The annotation system follows a domain-driven design approach, primarily organized under:

- `reader-interactions`: Core annotation functionality 
- `reader`: Reading interface where annotations are displayed
- `auth`: Authentication system that integrates with annotations

## Database Schema

### Annotations Table

```sql
create table public.annotations (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  book_id text not null,
  section_id text not null,
  paragraph_index integer not null,
  text_content text not null,
  annotation_content text,
  selection_start integer not null,
  selection_end integer not null,
  created_at timestamp with time zone default now() not null,
  updated_at timestamp with time zone default now() not null,
  type text default 'annotation'::text not null,
  metadata jsonb default '{}'::jsonb not null,
  
  constraint annotations_type_check check (type in ('annotation', 'highlight', 'bookmark'))
);

-- Create index for faster queries
create index annotations_user_book_section_idx on public.annotations (user_id, book_id, section_id);
```

The annotations table stores:
- User-created annotations, highlights, and bookmarks
- References to specific books and sections
- Precise text positions via paragraph index and character positions
- Custom metadata (including color, visibility settings)

## Data Models

### AnnotationType

[app/src/domains/reader-interactions/models/types.ts](mdc:app/src/domains/reader-interactions/models/types.ts)

```typescript
export type AnnotationType = 'annotation' | 'highlight' | 'bookmark';
```

### Annotation Interface

[app/src/domains/reader-interactions/models/types.ts](mdc:app/src/domains/reader-interactions/models/types.ts)

```typescript
export interface Annotation {
  id: string;
  user_id: string;
  book_id: string;
  section_id: string;
  paragraph_index: number;
  text_content: string;
  annotation_content: string | null;
  selection_start: number;
  selection_end: number;
  created_at: string;
  updated_at: string;
  type: AnnotationType;
  metadata: {
    color?: string;
    isPublic?: boolean;
  };
}
```

## Key Components

- **AnnotationMarker**: [app/src/domains/reader-interactions/components/AnnotationMarker.tsx](mdc:app/src/domains/reader-interactions/components/AnnotationMarker.tsx)
  - Renders visual markers for annotations in text
  - Displays appropriate styling based on annotation type

- **AnnotationForm**: [app/src/domains/reader-interactions/components/AnnotationForm.tsx](mdc:app/src/domains/reader-interactions/components/AnnotationForm.tsx)
  - Creates new annotations with type, color, and visibility settings
  - Integrates with authentication via `useAuth` hook

- **TextActionMenu**: [app/src/domains/reader/components/TextActionMenu.tsx](mdc:app/src/domains/reader/components/TextActionMenu.tsx)
  - Context menu for text selection with annotation options

## Core Hooks

- **useAnnotation**: [app/src/domains/reader-interactions/hooks/useAnnotation.ts](mdc:app/src/domains/reader-interactions/hooks/useAnnotation.ts)
  - Manages CRUD operations for annotations
  - Handles authentication checks and errors

- **useTextSelection**: [app/src/domains/reader/hooks/useTextSelection.ts](mdc:app/src/domains/reader/hooks/useTextSelection.ts)
  - Detects text selection and calculates positions

## User Flow

1. User selects text → TextActionMenu appears
2. User chooses annotation type → AnnotationForm opens
3. If not authenticated → Login/signup prompt appears
4. User submits → Annotation saved to Supabase
5. AnnotationMarker displays on text

## Authentication Integration

### AuthContext

[app/src/domains/auth/context/AuthContext.tsx](mdc:app/src/domains/auth/context/AuthContext.tsx)

Provides:
- Global authentication state
- Modal control for login/signup
- Integration with annotation components

### useAuthModals Hook

[app/src/domains/auth/hooks/useAuthModals.ts](mdc:app/src/domains/auth/hooks/useAuthModals.ts)

Manages authentication UI:
- Modal visibility state
- Tab switching between login/signup
- Callback functions for opening/closing modals

## Integration Flow

1. User selects text in reader interface
2. `useTextSelection` hook detects selection and calculates position
3. `TextActionMenu` appears with annotation options
4. User chooses annotation type
5. `AnnotationForm` opens for input
6. If user is not authenticated, authentication flow is triggered via `useAuth`
7. On submission, `useAnnotation.addAnnotation` saves to Supabase
8. `AnnotationMarker` components render to display the annotation

## Error Handling

The system provides graceful error handling, especially for authentication:
- Unauthenticated users are prompted to login/signup
- Form validation prevents invalid submissions
- Network errors display appropriate feedback

## Styling and UI

Annotations utilize:
- Tailwind CSS for styling
- Context-aware color schemes (dark/light mode compatibility)
- Accessible UI elements
- Mobile-responsive positioning
