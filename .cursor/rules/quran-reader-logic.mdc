---
description:
globs:
alwaysApply: false
---
# Quran Reader Logic

The Quran reading functionality resides primarily within the `reader` domain (`src/domains/reader/`).

## Core Components & Page

*   **Main Page:** [src/domains/reader/pages/quran/ReadPage.tsx](mdc:app/src/domains/reader/pages/quran/ReadPage.tsx) is the central component for the Quran reader. It orchestrates various custom hooks to fetch data, manage state (like current verse, content mode), handle navigation, and render the UI layout.
*   **Verse Rendering:** The display of individual verses is handled by mode-specific components:
    *   [Mode1Content.tsx](mdc:app/src/domains/reader/pages/quran/Components/Mode1Content.tsx) (Arabic only)
    *   [Mode2Content.tsx](mdc:app/src/domains/reader/pages/quran/Components/Mode2Content.tsx) (Arabic + Translation)
    *   [Mode3Content.tsx](mdc:app/src/domains/reader/pages/quran/Components/Mode3Content.tsx) (Word-by-word + Translation)
    *   [Mode4Content.tsx](mdc:app/src/domains/reader/pages/quran/Components/Mode4Content.tsx) (Translation only)
*   **Parent Content Renderer:** These mode components are conditionally rendered by [src/domains/reader/pages/quran/Components/VerseContent.tsx](mdc:app/src/domains/reader/pages/quran/Components/VerseContent.tsx) based on the `viewMode` prop.
*   **Translation/Footnotes Display:** The shared logic for displaying selected translations and handling footnotes is encapsulated in [src/domains/reader/pages/quran/Components/TranslationDisplay.tsx](mdc:app/src/domains/reader/pages/quran/Components/TranslationDisplay.tsx), used by Modes 2, 3, and 4.

## Data Loading

*   **Verse Data:** [src/domains/reader/hooks/quran/useVerseLoader.ts](mdc:app/src/domains/reader/hooks/quran/useVerseLoader.ts) is responsible for loading the primary verse data (Arabic text, word analysis) for a specific Surah. It uses Vite's dynamic import (`import.meta.glob`) to load data from `src/domains/reader/data/quran/verses/`.
*   **Translation Data:** [src/domains/reader/hooks/quran/useTranslationLoader.ts](mdc:app/src/domains/reader/hooks/quran/useTranslationLoader.ts) is called by `useVerseLoader`. It dynamically loads translation data from `src/domains/reader/data/quran/translations/`.
*   **Conditional Loading:** Importantly, `useTranslationLoader` now accepts the current `contentMode` and **only loads translation data if the mode is not '1'** (Arabic only), optimizing performance.

## Other Key Hooks

*   **`useContentMode`:** Manages the current display mode (1-4).
*   **`useSurahs`:** Provides the list of all Surahs.
*   **`useNavigationSheet`:** Handles the logic for the Surah/Verse selection sheet.
*   **`useVerseScroll`:** Provides functionality to scroll to a specific verse.
*   **`useVisibleVerse`:** Detects the currently visible verse during scrolling.
