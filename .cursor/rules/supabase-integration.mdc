---
description: 
globs: 
alwaysApply: false
---
# <PERSON><PERSON> Kitabe Supabase Integration

## Database Structure

The application uses Supabase PostgreSQL with these schemas:
- **public**: Application tables
- **auth**: Authentication (Supabase-managed)
- **storage**: File storage (Supabase-managed)

### Key Tables

#### Annotations

```sql
create table public.annotations (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users not null,
  book_id text not null,
  section_id text not null,
  paragraph_index integer not null,
  text_content text not null,
  annotation_content text,
  selection_start integer not null,
  selection_end integer not null,
  created_at timestamp with time zone default now() not null,
  updated_at timestamp with time zone default now() not null,
  type text default 'annotation'::text not null,
  metadata jsonb default '{}'::jsonb not null,
  
  constraint annotations_type_check check (type in ('annotation', 'highlight', 'bookmark'))
);

-- RLS policies
alter table public.annotations enable row level security;
create policy "Users can read their own annotations" on public.annotations for select using (auth.uid() = user_id);
create policy "Users can insert their own annotations" on public.annotations for insert with check (auth.uid() = user_id);
```

#### User Profiles

```sql
create table public.profiles (
  id uuid not null,
  updated_at timestamp with time zone null,
  username text null,
  display_name text null,
  avatar_url text null,
  constraint profiles_duplicate_pkey primary key (id),
  constraint profiles_duplicate_username_key unique (username),
  constraint profiles_duplicate_id_fkey foreign KEY (id) references auth.users (id),
  constraint username_length check (username IS NULL OR char_length(username) >= 3)
);

-- RLS policies for profiles
alter table public.profiles enable row level security;

-- Users can read their own profile
create policy "Users can read their own profile"
  on public.profiles for select
  using (auth.uid() = id);

-- Users can update own profile
create policy "Users can update own profile"
  on public.profiles for update
  using (auth.uid() = id);
```

## Supabase Client

```typescript
// Singleton client setup
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
});
```

## Data Access Pattern

```typescript
// Example from useAnnotation hook
const fetchAnnotations = async (bookId: string, sectionId: string) => {
  try {
    const { data, error } = await supabase
      .from('annotations')
      .select('*')
      .eq('book_id', bookId)
      .eq('section_id', sectionId)
      .order('paragraph_index', { ascending: true });
      
    if (error) throw error;
    return data || [];
  } catch (error) {
    // Error handling
  }
};
```

## Realtime Subscriptions

```typescript
// Subscribe to annotation changes
const subscription = supabase
  .channel(`annotations:${bookId}:${sectionId}`)
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'annotations',
      filter: `book_id=eq.${bookId}&section_id=eq.${sectionId}`
    },
    (payload) => {
      // Handle realtime updates
    }
  )
  .subscribe();
```

## Security

- **RLS Policies**: Control data access by user
- **JWT Auth**: Secure token-based authentication
- **Input Validation**: Before database operations
