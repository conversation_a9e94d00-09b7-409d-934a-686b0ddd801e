---
description: 
globs: 
alwaysApply: false
---
# İkra Kitabe Authentication System

## Architecture

- **Data Layer**: Supabase Auth
- **State Management**: Auth store (Zustand)
- **Context Layer**: AuthContext
- **UI Layer**: Auth components

## Auth Context

```typescript
// AuthContext provides:
interface AuthContextType {
  openLoginModal: () => void;
  openSignupModal: () => void;
  closeAuthModal: () => void;
  isLoginModalOpen: boolean;
  isSignupModalOpen: boolean;
}
```

## Key Components

- **AuthProvider**: [app/src/domains/auth/context/AuthContext.tsx](mdc:app/src/domains/auth/context/AuthContext.tsx)
  - Global provider that renders auth modals
  - Exposes useAuth hook for components

- **useAuthModals**: [app/src/domains/auth/hooks/useAuthModals.ts](mdc:app/src/domains/auth/hooks/useAuthModals.ts)
  - Manages login/signup modal state

- **AuthSheet**: Sliding sheet component with login/signup forms

- **LoginButton**: [app/src/shared/components/atoms/Button/LoginButton.tsx](mdc:app/src/shared/components/atoms/Button/LoginButton.tsx)
  - Triggers login modal

## Auth Flow

1. **Initial Check**: App startup checks for session
2. **Trigger Points**:
   - Direct navigation to auth pages
   - LoginButton in AppNavbar
   - Protected feature access (annotations)
3. **Authentication**: Credentials submitted to Supabase
4. **Success**: Modal closes, state updates with user info

## Integration with Annotations

- Annotation creation requires authentication
- Unauthenticated users see login/signup prompts
- Auth errors in AnnotationForm handle gracefully

## Supabase Authentication Schema

Supabase provides built-in authentication tables under the `auth` schema:

```sql
-- Main user identity table
auth.users (
  id uuid primary key,
  email text unique,
  encrypted_password text,
  email_confirmed_at timestamp with time zone,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  ...
)

-- User identity schema (simplified)
auth.identities (
  id uuid primary key,
  user_id uuid references auth.users not null,
  identity_data jsonb not null,
  provider text not null,
  ...
)
```

## Auth Store

[app/src/domains/auth/store/authStore.ts](mdc:app/src/domains/auth/store/authStore.ts)

The auth store uses Zustand for state management with the following key features:

```typescript
interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (password: string) => Promise<void>;
}
```

## Auth Components

### AuthSheet

[app/src/domains/auth/pages/Components/AuthSheet.tsx](mdc:app/src/domains/auth/pages/Components/AuthSheet.tsx)

A sliding sheet component that contains both login and signup forms:
- Adapts to mobile and desktop layouts
- Handles tab switching between login/signup
- Connects to auth store for authentication actions

### LoginButton

[app/src/shared/components/atoms/Button/LoginButton.tsx](mdc:app/src/shared/components/atoms/Button/LoginButton.tsx)

A reusable button component that:
- Opens the login modal using the useAuth hook
- Accepts an optional custom onClick handler
- Uses consistent styling with other application buttons

## Authentication Flow

1. **Initial Auth Check**: On app startup, `checkAuth()` verifies existing sessions
2. **Login/Signup Trigger**: Either through:
   - Direct navigation to auth pages
   - Clicking the LoginButton in AppNavbar
   - Attempting protected actions (e.g., creating annotations)
3. **Modal Display**: AuthSheet slides up with login/signup forms
4. **Authentication**: Credentials submitted to Supabase Auth
5. **Session Creation**: On successful auth, a session is created
6. **State Update**: Auth store updates global state with user info
7. **Modal Closure**: On success, auth modal automatically closes

## Security Considerations

- Session tokens stored securely in localStorage
- Automatic token refresh mechanism
- Backend RLS policies control data access
- Password reset flow with secure tokens

## Responsive Design

The authentication UI is fully responsive:
- Sheet-based design works on mobile and desktop
- Form layout adjusts based on screen size
- Touch-friendly inputs for mobile users
