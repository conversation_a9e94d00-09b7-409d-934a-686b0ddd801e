---
description:
globs:
alwaysApply: false
---
# Project Terminology

This document outlines key terminology and naming conventions used in the İkra Kitabe project.

## Domain-Specific Terms

### Quran Reader

- **Surah**: Chapter of the Quran (114 total)
- **Verse**: Individual verse within a Surah, also called "Ayet" in Turkish
- **Meal**: Translation of the Quran (Turkish term)
- **Juz**: Section of the Quran (30 total parts)
- **ContentMode**: Display mode for Quran content (previously "ViewMode")
  - Mode 1: Arabic only
  - Mode 2: Arabic with translation
  - Mode 3: Word-by-word translation
  - Mode 4: Translation only

### R<PERSON>le Reader

- **Risale-i Nur**: Collection of Islamic texts by <PERSON>
- **Kitap**: Book (Turkish term)
- **Bölüm**: Section or chapter (Turkish term)
- **Paragraf**: Paragraph, basic unit of content

### UI Components

- **NavigationSheet**: Modal dialog for navigation
- **ActionSheet**: Mobile-oriented modal for actions
- **ContentCard**: Card component for displaying content items

## File Naming Conventions

- **Component Files**: PascalCase (e.g., `ContentModeSelector.tsx`)
- **Hook Files**: camelCase with 'use' prefix (e.g., `useContentMode.ts`)
- **Service Files**: camelCase (e.g., `bookService.ts`)
- **Store Files**: camelCase (e.g., `librarystore.ts`)
- **Type Files**: camelCase (e.g., `types.ts`)

## Component Props Naming

- Props interfaces are named with component name + 'Props' suffix (e.g., `ContentModeSelectorProps`)
- Event handler props use 'on' prefix (e.g., `onSelectVerse`, `onClose`)
- Toggle functions use 'toggle' prefix (e.g., `toggleFootnotes`)
- Boolean state props often use 'is' prefix (e.g., `isContentModeOpen`, `isLoading`)
