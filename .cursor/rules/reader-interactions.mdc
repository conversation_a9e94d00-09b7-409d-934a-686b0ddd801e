---
description:
globs:
alwaysApply: false
---
# İkra Kitabe Reader Interactions Domain

The reader-interactions domain manages user interactions with content such as annotations, highlights, and bookmarks.

## Key Components

- **AnnotationMarker**: [components/AnnotationMarker.tsx](mdc:app/src/domains/reader-interactions/components/AnnotationMarker.tsx)
  - Visualizes annotations within text
  - Different styling for annotation types

- **AnnotationForm**: [components/AnnotationForm.tsx](mdc:app/src/domains/reader-interactions/components/AnnotationForm.tsx)
  - Creates/edits annotations
  - Integrates with auth system

- **TextActionMenu**: [reader/components/TextActionMenu.tsx](mdc:app/src/domains/reader/components/TextActionMenu.tsx)
  - Context menu for text selection
  - Options for annotate, highlight, bookmark

## Core Hooks

- **useAnnotation**: [hooks/useAnnotation.ts](mdc:app/src/domains/reader-interactions/hooks/useAnnotation.ts)
  - CRUD operations for annotations
  - Authentication checks

- **useTextSelection**: [reader/hooks/useTextSelection.ts](mdc:app/src/domains/reader/hooks/useTextSelection.ts)
  - Selection detection and position calculation

## User Flow

1. **Select Text**: User selects text → TextActionMenu appears
2. **Choose Action**: User selects action type
3. **Create**: AnnotationForm opens with selected text
4. **Authentication**: Login prompt if needed
5. **Save**: Annotation saved to database
6. **Display**: AnnotationMarker renders in text

## Integration Points

- **Auth System**: Uses useAuth hook for authentication flows
- **Supabase**: Stores/retrieves annotations with RLS policies
- **Reader**: Integrates with ParagraphContainer component
