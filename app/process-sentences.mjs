#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// File path
const inputFile = 'src/domains/reader/data/risalei_nur/27/content/27_1.ts';
const outputFile = 'src/domains/reader/data/risalei_nur/27/content/27_1_processed.ts';

// Read the file
console.log(`Reading file: ${inputFile}`);
const fileContent = fs.readFileSync(inputFile, 'utf8');

// Parse the content
const contentMatch = fileContent.match(/export const content = ({[\s\S]*});/);
if (!contentMatch || !contentMatch[1]) {
  console.error('Could not parse the content object');
  process.exit(1);
}

// Parse the content object
const contentObject = JSON.parse(contentMatch[1]);

// Process the sentences array
if (contentObject.sentences) {
  // Split the content at <br> tags
  const parts = contentObject.sentences.split(/<br>/g);
  
  // Convert to an array of strings
  contentObject.sentences = parts.map(part => part.trim());
  
  console.log(`Converted content with <br> tags to ${contentObject.sentences.length} separate strings`);
}

// Create the output content
const outputContent = `export const content = ${JSON.stringify(contentObject, null, 2)};`;

// Write the processed content to the output file
fs.writeFileSync(outputFile, outputContent, 'utf8');
console.log(`Processed content written to: ${outputFile}`);

// Log a summary of the changes
console.log('Process completed successfully!'); 