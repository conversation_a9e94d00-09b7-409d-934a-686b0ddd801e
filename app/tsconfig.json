{
  "compilerOptions": {
    // Basic Options from root tsconfig
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "jsx": "react-jsx",
    "skipLibCheck": true,
    "types": ["vite/client", "node"],

    // Module Resolution
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,

    // Strictness & Linting
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    // App directory settings
    "baseUrl": ".", // Base directory for path aliases is app
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["./src/shared/*"],
      "@library/*": ["./src/domains/library/*"],
      "@reader/*": ["./src/domains/reader/*"],
      "@settings/*": ["./src/domains/settings/*"],
      "@domains/*": ["./src/domains/*"]
    }
  },
  "include": [
    "src", // Include source files within app
    "vite.config.ts" // Include Vite config within app
   ],
  "exclude": [
    "node_modules", // Exclude node_modules within app
    "dist" // Exclude build output within app
   ]
} 