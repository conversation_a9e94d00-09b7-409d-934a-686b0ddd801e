#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// File paths - using absolute path for better reliability
const inputFile = path.resolve(process.cwd(), 'src/domains/reader/data/risalei_nur/27/content/27_1.ts');
const outputFile = path.resolve(process.cwd(), 'src/domains/reader/data/risalei_nur/27/content/27_1_processed.ts');

// Read the file
console.log(`Reading file: ${inputFile}`);
try {
  const fileContent = fs.readFileSync(inputFile, 'utf8');
  console.log(`File read successfully. File size: ${fileContent.length} characters`);
  
  // Extract just the content string directly from the file without parsing as JSON
  const mainContentMatch = fileContent.match(/"sentences": "([^"\\]*(\\.[^"\\]*)*)"/);
  
  if (!mainContentMatch || !mainContentMatch[1]) {
    console.error('Could not find sentences content');
    process.exit(1);
  }
  
  // Get the sentences content as a string
  const sentencesContent = mainContentMatch[1];
  console.log(`Found sentences content (length: ${sentencesContent.length} characters)`);
  
  // Split by <br> tags
  const sentences = sentencesContent.split(/<br\s*\/?>/i);
  console.log(`Split into ${sentences.length} sentences`);
  
  // Create processed content with array of sentences
  const newFileContent = fileContent.replace(
    /"sentences": "([^"\\]*(\\.[^"\\]*)*)"/, 
    `"sentences": ${JSON.stringify(sentences, null, 4)}`
  );
  
  // Write the processed content
  fs.writeFileSync(outputFile, newFileContent, 'utf8');
  console.log(`Processed content written to: ${outputFile}`);
  console.log('Process completed successfully!');
  
} catch (error) {
  console.error('Error processing file:', error);
} 