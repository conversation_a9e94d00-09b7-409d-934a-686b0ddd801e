#!/usr/bin/env python3

import json
import re
import os

# File paths
input_file = 'src/domains/reader/data/risalei_nur/27/content/27_1.ts'
output_file = 'src/domains/reader/data/risalei_nur/27/content/27_1_processed.ts'

# Read the file
print(f"Reading file: {input_file}")
with open(input_file, 'r', encoding='utf-8') as f:
    file_content = f.read()

# Parse the content using regex
content_match = re.search(r'export const content = ({[\s\S]*});', file_content)
if not content_match:
    print('Could not parse the content object')
    exit(1)

# Parse the JSON content
content_json_str = content_match.group(1)
content_object = json.loads(content_json_str)

# Process the sentences array
if 'sentences' in content_object:
    # Split the content at <br> tags while preserving other HTML tags
    # Using a regex pattern that looks for <br> or <br/> or <br /> tags
    parts = re.split(r'<br\s*/?>', content_object['sentences'], flags=re.IGNORECASE)
    
    # Convert to an array of strings while trimming whitespace
    content_object['sentences'] = [part.strip() for part in parts]
    
    print(f"Converted content with <br> tags to {len(content_object['sentences'])} separate strings while preserving other HTML tags")

# Create the output content
output_content = f"export const content = {json.dumps(content_object, indent=2, ensure_ascii=False)};"

# Write the processed content to the output file
with open(output_file, 'w', encoding='utf-8') as f:
    f.write(output_content)
print(f"Processed content written to: {output_file}")

# Log a summary of the changes
print('Process completed successfully!') 