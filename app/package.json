{"name": "i<PERSON>-kitabe", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-strict": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@aws-sdk/client-s3": "^3.800.0", "@aws-sdk/s3-request-presigner": "^3.800.0", "@babel/core": "^7.28.0", "@floating-ui/react": "^0.27.13", "@supabase/supabase-js": "^2.49.4", "@types/lodash": "^4.17.16", "html-react-parser": "^5.2.2", "immer": "^10.1.1", "lodash": "^4.17.21", "lucide-react": "^0.341.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.3", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "zustand": "^5.0.7"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.12.4", "@eslint/js": "^9.21.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^22.18.1", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-window": "^1.8.8", "@types/testing-library__react": "^10.0.1", "@vitejs/plugin-react": "^5.0.2", "autoprefixer": "^10.4.17", "cors": "^2.8.5", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "express": "^5.1.0", "globals": "^15.15.0", "http-proxy-middleware": "^3.0.5", "jest": "^29.7.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "~5.3.3", "typescript-eslint": "^8.24.1", "vite": "^6.3.6", "vitest": "^3.1.1"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "eslint.config.js", "keywords": [], "author": "", "license": "ISC"}