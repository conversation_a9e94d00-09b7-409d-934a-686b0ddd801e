import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
// import { cloudflare } from '@cloudflare/vite-plugin'
import { fileURLToPath, URL } from 'url'

// https://vite.dev/config/
export default defineConfig(({ command }) => ({
  plugins: [
    react(),
    // NOTE: Disable Cloudflare Vite plugin in dev to avoid requiring a wrangler config in app/
    // If you want to use it, pass the worker wrangler.toml path explicitly.
    // ...(command === 'serve' ? [cloudflare({ wrangler: { /* configPath: '../worker/wrangler.toml' */ } })] : []),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@domains': fileURLToPath(new URL('./src/domains', import.meta.url)),
      '@shared': fileURLToPath(new URL('./src/shared', import.meta.url)),
      '@pages': fileURLToPath(new URL('./src/pages', import.meta.url)),
      '@library': fileURLToPath(new URL('./src/domains/library', import.meta.url)),
      '@reader': fileURLToPath(new URL('./src/domains/reader', import.meta.url)),
      '@settings': fileURLToPath(new URL('./src/domains/settings', import.meta.url))
    }
  },
  server: {
    proxy: {
      // Forward API calls to Wrangler dev (default 8787) during `vite dev`
      '/api': 'http://127.0.0.1:8787'
    }
  }
}))
