import { Routes, Route, useNavigate, useLocation, Navigate } from 'react-router-dom';
import { useEffect, useState, useMemo, useCallback, Fragment } from 'react';
import { PageLayout, SearchBar, AppLogo, ThemeToggle, LoginButton, MoreOptionsButton, SavedContentAccessCard, BookCard, ErrorToast } from '@shared/components';
import HomePageSection from '@shared/components/organisms/Layout/HomePageSection';
import { ExpandableSection } from './shared/components/organisms/ExpandableSection';
import { ThemeProvider } from './shared/context/ThemeContext';
import QuranChaptersPage from './domains/reader/pages/quran/ChaptersPage';
import QuranReadPage from './domains/reader/pages/quran/ReadPage';
import RisaleReadPage from './domains/reader/pages/risale/ReadPage';
import RisaleChapterSelectionPage from './domains/reader/pages/risale/ChapterSelection';
import TafsirReadPage from './domains/reader/pages/tafsir/ReadPage';
import TafsirChapterSelectionPage from './domains/reader/pages/tafsir/ChapterSelection';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import SavedContentPage from './domains/reader-interactions/bookmarks/pages/SavedContentPage';
// Topics module removed. Add Tags pages instead.
import TagsDiscoveryPage from '@reader/pages/tags/TagsDiscoveryPage';
import TagDetailRoute from '@reader/pages/tags/TagDetailRoute';
import { useLibraryStore } from './domains/library/store/librarystore';
import { IBook, ICategory } from './domains/library/models/types';
import { LoadingState } from './shared/components/molecules/Feedback/LoadingState';
import { useAuthStore } from './domains/auth/store/authStore';
import ResetPasswordPage from './domains/auth/pages/ResetPasswordPage';
import UsernameSetupPage from './domains/auth/pages/UsernameSetupPage';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { useAuth } from '@domains/auth/hooks/useAuth';
import { AuthSheet } from '@domains/auth/pages/Components/AuthSheet';
import { ProfileSheet } from '@domains/auth/pages/Components/ProfileSheet';
import { filterByNormalizedQuery, normalizeSearchTerm } from '@shared/utils/searchUtils';
// Hata yönetimi bileşenleri
import ErrorBoundary from '@shared/components/organisms/ErrorBoundary/ErrorBoundary';
import NetworkStatusMonitor from '@shared/components/organisms/NetworkStatusMonitor/NetworkStatusMonitor';

// Annotation Service ve hook'ları
import { AnnotationService } from './domains/reader-interactions/services/AnnotationService';
import { useAnnotationManager } from './domains/reader-interactions/annotations/hooks/useAnnotationManager';


// ✅ YENİ ORGANİZE YAPISI - Topic sistemi
import { TagsExploreCard } from '@shared/components';
// Yeni store'u import et
import { useContentInteractionStore } from '@domains/reader-interactions/store/contentInteractionStore';
import { useErrorToast } from '@shared/hooks/useErrorToast';


// Dynamic Redirect component
const DynamicRedirect = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const newPath = location.pathname.replace('/tefsir', '/tafsir');
    navigate(newPath, { replace: true });
  }, [navigate, location.pathname]);

  return null;
};

function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const { openLoginModal } = useAuth();
  const { errors, removeError } = useErrorToast();


  // State'leri ayrı ayrı seç (ince kontrol ve gereksiz yeniden render'ları önlemek için)
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const isUsernameSetupRequired = useAuthStore((state) => state.isUsernameSetupRequired);
  const isLoading = useAuthStore((state) => state.isLoading);
  const user = useAuthStore((state) => state.user);

  useEffect(() => {
    // Auth yükleniyor ve kararlar henüz verilememiş ise bekle
    if (isLoading) {
      console.log('[AuthRedirectManager] Auth state loading, waiting...');
      return;
    }

    const currentPath = window.location.pathname;

    // Eğer kullanıcı girişi var ve username ekranına ihtiyaç varsa, gerekli yönlendirmeyi yap
    if (isAuthenticated && isUsernameSetupRequired) {
      if (currentPath !== '/profil-tamamla') {
        console.log('[AuthRedirectManager] Username setup required, redirecting to setup page');
        navigate('/profil-tamamla', { replace: true });
      }
    }
    // Eğer kullanıcı username sayfasında ama username ayarlanmış ise ana sayfaya yönlendir
    else if (currentPath === '/profil-tamamla' && isAuthenticated && !isUsernameSetupRequired) {
      console.log('[AuthRedirectManager] Username already set, redirecting from setup page to home');
      navigate('/', { replace: true });
    }
    // Sadece hata ayıklama için
    else {
      console.log('[AuthRedirectManager] No redirects needed. Auth state:', {
        isAuthenticated,
        isUsernameSetupRequired,
        currentPath,
        provider: user?.app_metadata?.provider
      });
    }
  }, [isAuthenticated, isUsernameSetupRequired, isLoading, navigate, location.pathname, user?.app_metadata?.provider]);

  // Annotation service'i initialize etmek için hook'ları çağır
  const { createAnnotation, getAnnotations } = useAnnotationManager();

  // App ilk yüklendiğinde servisi initialize et
  useEffect(() => {
    AnnotationService.initialize(createAnnotation, getAnnotations);
  }, [createAnnotation, getAnnotations]);

  // Home page component
  const HomePage = () => {
    const isMobile = useIsMobile();
    

    // Get state and actions from the library store
    const books = useLibraryStore((state) => state.books);
    const categories = useLibraryStore((state) => state.categories);
    const fetchBooks = useLibraryStore((state) => state.fetchBooks);
    const fetchCategories = useLibraryStore((state) => state.fetchCategories);
    const loadingBooks = useLibraryStore((state) => state.loading.books);
    const loadingCategories = useLibraryStore((state) => state.loading.categories);

    // Auth state and interaction store
    const {
      favorites,
      recentActivities,
      fetchFavorites,
      fetchRecentActivities,
      error: interactionError,
      clearError
    } = useContentInteractionStore();

    // Error toast hook
    const { showError } = useErrorToast();

    // State for search term
    const [searchTerm, setSearchTerm] = useState('');

    // Fetch data on component mount
    useEffect(() => {
      fetchBooks();
      fetchCategories();
    }, [fetchBooks, fetchCategories]);

    // Fetch user interactions when authenticated - memoized callbacks
    const memoizedFetchFavorites = useCallback(() => {
      fetchFavorites();
    }, [fetchFavorites]);

    const memoizedFetchRecentActivities = useCallback(() => {
      fetchRecentActivities();
    }, [fetchRecentActivities]);

    useEffect(() => {
      if (isAuthenticated) {
        memoizedFetchFavorites();
        memoizedFetchRecentActivities();
      }
    }, [isAuthenticated, memoizedFetchFavorites, memoizedFetchRecentActivities]);

    // Show error toast when interaction error occurs
    useEffect(() => {
      if (interactionError) {
        showError(interactionError);
        // Error'ı gösterdikten sonra temizle
        clearError();
      }
    }, [interactionError, showError, clearError]);

    // Group books by category using data from the store
    const booksByCategory = useMemo(() => {
      return categories.map(category => {
        const categoryBooks = books.filter(book => book.category_id === category.id);
        return {
          category,
          books: categoryBooks
        };
      });
    }, [books, categories]);


    // Helper to find the original category for a book
    const getCategoryForBook = (book: IBook) => {
      return categories.find(cat => cat.id === book.category_id);
    };

    // Create final sections, including recents and favorites
    const allSections = useMemo(() => {
      if (books.length === 0) {
        return booksByCategory;
      }

      const bookMap = new Map(books.map(book => [String(book.id), book]));
      
      const recentBooks = recentActivities
        .map(activity => bookMap.get(activity.content_id))
        .filter((book): book is IBook => !!book);
        
      const favoriteBooks = favorites
        .map(fav => bookMap.get(fav.content_id))
        .filter((book): book is IBook => !!book);

      const dynamicSections: { category: ICategory; books: IBook[] }[] = [];

      if (isAuthenticated && favoriteBooks.length > 0) {
        dynamicSections.push({
          category: { 
            id: 999, // Use a unique number for ID
            name: 'Favorilerim',
            description: 'Sık kullandığınız kitaplar',
            gradient_start: '#6e6e6e', // Neutral color
            gradient_end: '#4a4a4a'       // Neutral color
          },
          books: favoriteBooks
        });
      }
      
      if (isAuthenticated && recentBooks.length > 0) {
        dynamicSections.push({
          category: { 
            id: 998, // Use a unique number for ID
            name: 'Son Okunanlar',
            description: 'En son görüntülediğiniz kitaplar',
            gradient_start: '#6e6e6e', // Neutral color
            gradient_end: '#4a4a4a'      
          },
          books: recentBooks
        });
      }

      return [...dynamicSections, ...booksByCategory];
    }, [books, favorites, recentActivities, isAuthenticated, booksByCategory]);

    // Define Navbar Center Content with Besmele
    const homeNavbarCenter = (
      <p className="text-base font-arabic m-0 leading-none" style={{ color: 'var(--text-color)' }}>
        بِسْمِ اللّٰهِ الرَّحْمٰنِ الرَّح۪يمِ
      </p>
    );

    // Define Navbar Second Row Content for Mobile
    const homeNavbarSecondRow = isMobile ? (
      <div className="flex items-center justify-between w-full px-2 h-full">
        <AppLogo />
        <div className="flex items-center space-x-1 sm:space-x-2">
          <LoginButton onClick={openLoginModal} />
          <ThemeToggle />
          <div className="relative">
            <MoreOptionsButton />
          </div>
        </div>
      </div>
    ) : null;

    // Navbar Actions removed

    // Filter logic
    const filteredBooksByCategory = useMemo(() => {
      if (!searchTerm.trim()) {
        return allSections;
      }

      return allSections.map((categoryItem: { category: ICategory; books: IBook[] }) => {
        // Filter books within the category
        const filteredBooks = filterByNormalizedQuery(categoryItem.books, searchTerm, (book: IBook) => [
          book.title,
          book.author // Assuming book has an author property to search
        ]);

        // Return the category with filtered books
        return { ...categoryItem, books: filteredBooks };
      }).filter((categoryItem: { category: { name: string; }; books: IBook[] }) => {
        // Also check if the category name itself matches
        const categoryNameMatches = normalizeSearchTerm(categoryItem.category.name).includes(normalizeSearchTerm(searchTerm));
        // Keep the category if its name matches OR if it has any books left after filtering
        return categoryNameMatches || categoryItem.books.length > 0;
      });
    }, [allSections, searchTerm]);

    if (loadingBooks || loadingCategories) {
      return (
        <PageLayout
          navbarCenterContent={homeNavbarCenter}
          secondRowContent={homeNavbarSecondRow}
          navbarTwoRows={isMobile}
          showBackButton={false}
          showLogo={!isMobile}
          showThemeToggle={!isMobile}
          showLoginButton={!isMobile}
          showMoreOptions={!isMobile}
        >
          <main className="container mx-auto px-4 py-8 flex justify-center items-center">
            <LoadingState message="Kitaplar yükleniyor..." />
          </main>
        </PageLayout>
      );
    }

    return (
      <Fragment>
        <PageLayout
          navbarCenterContent={homeNavbarCenter}
          secondRowContent={homeNavbarSecondRow}
          navbarTwoRows={isMobile}
          showBackButton={false}
          showLogo={!isMobile}
          showThemeToggle={!isMobile}
          showLoginButton={!isMobile}
          showMoreOptions={!isMobile}
        >
          <main className="container mx-auto px-4 py-8">
            <div className="max-w-xl mx-auto">
              <SearchBar
                value={searchTerm}
                onChange={setSearchTerm}
                placeholder="Kitap veya kategori ara..."
                className="mb-6"
              />

              <HomePageSection className="mb-6">
                <div className="grid grid-cols-2 gap-4">
                  {/* Kaydetme Kartı - sadece giriş yapan kullanıcılar için */}
                  {isAuthenticated && (
                    <SavedContentAccessCard />
                  )}

                  {/* Topic Keşif Kartı - herkese açık */}
                  <TagsExploreCard />
                </div>
              </HomePageSection>
            </div>

            <div className="space-y-6">
              {filteredBooksByCategory.map(({ category, books }) => {
                // Check if it's a special dynamic section
                const isDynamicSection = category.id === 999 || category.id === 998;

                return (
                  <ExpandableSection
                    key={category.id}
                    title={category.name}
                    items={books}
                    // For dynamic sections, don't pass a single category
                    // Let the renderItem handle it
                    category={isDynamicSection ? undefined : category}
                    initialExpanded={true}
                    className="mb-4"
                    // Provide a custom renderItem for dynamic sections
                    renderItem={isDynamicSection ? (book) => {
                      const originalCategory = getCategoryForBook(book);
                      if (!originalCategory) return null;
                      return <BookCard book={book} category={originalCategory} />;
                    } : undefined}
                  />
                );
              })}
            </div>
          </main>
        </PageLayout>
      </Fragment>
    );
  };

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        // Hata izleme servisi entegrasyonu buraya eklenebilir
        console.error('Application Error:', error, errorInfo);
      }}
    >
      <ThemeProvider>
        <NetworkStatusMonitor
          offlineMessage="İnternet bağlantınız kesildi. İçerik yükleme ve diğer çevrimiçi özellikler çalışmayabilir."
        >
          <div className="min-h-screen bg-[var(--bg-color)]">
            <Routes>
              {/* Homepage */}
              <Route path="/" element={
                <ErrorBoundary>
                  <HomePage />
                </ErrorBoundary>
              } />

              {/* Profil Tamamlama Sayfası */}
              <Route path="/profil-tamamla" element={
                <ErrorBoundary>
                  <UsernameSetupPage />
                </ErrorBoundary>
              } />

              {/* Şifre Sıfırlama Sayfası */}
              <Route path="/reset-password" element={
                <ErrorBoundary>
                  <ResetPasswordPage />
                </ErrorBoundary>
              } />

              {/* Kuran Sayfaları (Chapter Listesi) */}
              <Route path="/kuran-sureler" element={
                <ErrorBoundary>
                  <QuranChaptersPage />
                </ErrorBoundary>
              } />
              <Route path="/mealli-kuran-sureler" element={
                <ErrorBoundary>
                  <QuranChaptersPage />
                </ErrorBoundary>
              } />
              <Route path="/kelime-mealli-kuran-sureler" element={
                <ErrorBoundary>
                  <QuranChaptersPage />
                </ErrorBoundary>
              } />
              <Route path="/kuran-meali-sureler" element={
                <ErrorBoundary>
                  <QuranChaptersPage />
                </ErrorBoundary>
              } />

              {/* Kuran Okuma Sayfaları */}
              <Route path="/kuran/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />
              <Route path="/kuran/oku/:bookId/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />
              <Route path="/mealli-kuran/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />
              <Route path="/kelime-mealli-kuran/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />
              <Route path="/kuran-meali/:surahId" element={
                <ErrorBoundary>
                  <QuranReadPage />
                </ErrorBoundary>
              } />

              {/* Risale Sayfaları */}
              <Route path="/risale/:bookId" element={
                <ErrorBoundary>
                  <RisaleChapterSelectionPage />
                </ErrorBoundary>
              } />
              <Route path="/risale/:bookId/:sectionId" element={
                <ErrorBoundary>
                  <RisaleReadPage />
                </ErrorBoundary>
              } />

              {/* Tafsir Sayfaları */}
              <Route path="/tafsir/:bookId" element={<ErrorBoundary><TafsirChapterSelectionPage /></ErrorBoundary>} />
              <Route path="/tafsir/:bookId/:surahId" element={<ErrorBoundary><TafsirReadPage /></ErrorBoundary>} />
              <Route path="/tafsir/:bookId/:surahId/:verseNumber" element={<ErrorBoundary><TafsirReadPage /></ErrorBoundary>} />

              {/* Redirect for common typo */}
              <Route path="/tefsir/:bookId/:surahId" element={<DynamicRedirect />} />
              <Route path="/tefsir/:bookId/:surahId/:verseNumber" element={<DynamicRedirect />} />
              
              {/* Saved Content Page */}
              <Route path="/saved-content" element={
                <ErrorBoundary>
                  <SavedContentPage />
                </ErrorBoundary>
              } />
              {/* Türkçe URL için redirect */}
              <Route path="/kaydettiğim-icerikler" element={
                <ErrorBoundary>
                  <SavedContentPage />
                </ErrorBoundary>
              } />

              {/* Tags Pages */}
              <Route path="/tags" element={
                <ErrorBoundary>
                  <TagsDiscoveryPage />
                </ErrorBoundary>
              } />
              <Route path="/tags/:tagId" element={
                <ErrorBoundary>
                  <TagDetailRoute />
                </ErrorBoundary>
              } />

              {/* Backward compatibility redirects for removed Topics */}
              <Route path="/topics" element={<Navigate to="/tags" replace />} />
              <Route path="/topics/:topicId" element={<Navigate to="/tags" replace />} />
            </Routes>
            <AuthSheet />
            <ProfileSheet />

            {/* Error Toasts */}
            {errors.map((error: { id: string; message: string }) => (
              <ErrorToast
                key={error.id}
                message={error.message}
                onClose={() => removeError(error.id)}
              />
            ))}
          </div>
        </NetworkStatusMonitor>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
