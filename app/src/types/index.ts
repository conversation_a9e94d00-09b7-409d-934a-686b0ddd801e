// Core type definitions for data

export interface IBook {
  id: number;
  title: string;
  author: string;
  description: string;
  cover_image: string;
  category_id: number;
  is_available: boolean;
}

export interface ICategory {
  id: number;
  name: string;
  description: string;
  gradient_start: string;
  gradient_end: string;
}

export type ThemeMode = 'dark' | 'light' | 'krem';

export interface IThemeColors {
  text: string;
  bg: string;
} 