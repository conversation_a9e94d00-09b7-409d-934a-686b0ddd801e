@import './fonts.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --text-color: #ffffff;
  --bg-color: #121212;
  --heading-1-size: 2.5rem;
  --heading-2-size: 2rem;
  --heading-3-size: 1.5rem;
  --heading-line-height: 1.4;
  background-color: var(--bg-color);
  color: var(--text-color);
}

body {
  margin: 0;
  padding: 0;
}

/* Base Typography Styles */
h1 {
  font-size: var(--heading-1-size);
  line-height: var(--heading-line-height);
  font-weight: 700;
  margin: 1.5rem 0 1rem;
}

h2 {
  font-size: var(--heading-2-size);
  line-height: var(--heading-line-height);
  font-weight: 600;
  margin: 1.25rem 0 0.75rem;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

h3 {
  font-size: var(--heading-3-size);
  line-height: var(--heading-line-height);
  font-weight: 500;
  margin: 1rem 0 0.5rem;
}

/* Custom styles for center and right aligned content */
.center-aligned {
  display: block;
  text-align: center;
  margin: 1.5rem 0;
}

.right-aligned {
  display: block;
  text-align: right;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :root {
    --heading-1-size: 2rem;
    --heading-2-size: 1.5rem;
    --heading-3-size: 1.25rem;
  }
}

/* Styles for Risale Content Tags */
.risale-content ca {
  display: block; /* Ensure it takes full width */
  text-align: center; /* Center the text */
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  @apply font-arabic; /* Apply Arabic font */
}

.risale-content ra {
  display: block; /* Ensure it takes full width */
  text-align: right; /* Right-align the text */
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  @apply font-arabic; /* Apply Arabic font */
}

/* Ensure standard tags are styled correctly within Risale content */
.risale-content b {
  font-weight: bold;
}

.risale-content i {
  font-style: italic;
}

/* Interactive spans for dictionary/footnotes */
.interactive-word,
.interactive-footnote {
  cursor: pointer;
  text-decoration: underline;
  text-decoration-style: dotted;
  text-decoration-color: var(--text-color); /* Use theme color */
  text-underline-offset: 2px; /* Original offset */
  transition: color 0.2s ease;
}

/* Styles for all Arabic text */
.arabic-text,
.risale-content .arabic-text,
.risale-content ca .arabic-text {
  font-size: 140% !important;
  @apply font-arabic;
  line-height: 2; /* Added for better readability */
}

/* Interactive spans for Arabic phrases - with adjusted underline offset */
.interactive-arabic-phrase {
  cursor: pointer;
  transition: color 0.2s ease;
}

/* Hover effect for all interactive elements */
.interactive-word:hover,
.interactive-footnote:hover,
.interactive-arabic-phrase:hover {
  color: color-mix(in srgb, var(--text-color) 70%, transparent); /* Slightly dimmed */
}

/* Tooltip Component Style */
.tooltip-component {
  background-color: color-mix(in srgb, var(--bg-color) 90%, #ffffff); /* Slightly lighter/darker than bg */
  color: var(--text-color);
  padding: 0.6rem 0.9rem;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  max-width: 300px; /* Adjust as needed */
  font-size: 0.9rem;
  line-height: 1.5;
  border: 1px solid color-mix(in srgb, var(--text-color) 20%, transparent);
}

.tooltip-component p { /* Ensure paragraphs inside tooltip have reasonable margin */
  margin-bottom: 0.5em;
}
.tooltip-component p:last-child {
  margin-bottom: 0;
}

/* Müfredat İçeriği Stilleri */
.mufredat-content {
  line-height: 1.7;
  text-align: justify;
}

.mufredat-content b {
  font-weight: 600;
  color: color-mix(in srgb, var(--text-color) 100%, #1e40af 10%);
}

.mufredat-content i {
  font-style: italic;
  opacity: 0.9;
}

.mufredat-content br {
  display: block;
  margin: 0.4em 0;
  content: "";
}

.mufredat-content sub {
  font-size: 0.75em;
  vertical-align: sub;
  opacity: 0.7;
}

/* Paragraf aralıkları */
.mufredat-content p {
  margin-bottom: 0.8em;
}

.mufredat-content p:last-child {
  margin-bottom: 0;
}
