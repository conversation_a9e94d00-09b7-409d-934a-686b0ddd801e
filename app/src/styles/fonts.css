
@font-face {
  font-family: 'Geist';
  src: url('/fonts/Geist/Geist-VariableFont_wght.ttf') format('truetype-variations');
  font-weight: 1 900; /* Defines the available weight range for the variable font */
  font-style: normal;
  font-display: swap;
}

/* Fallback */
@font-face {
  font-family: '<PERSON><PERSON><PERSON>';
  src: url('/fonts/<PERSON>haikh <PERSON>/<PERSON>haikhHamdullah<PERSON>af.ttf') format('truetype');
  font-weight: 400; /* Regular */
  font-style: normal;
  font-display: swap; /* Added for performance */
}
