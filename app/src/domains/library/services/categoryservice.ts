import { ICategory } from '../models/types';
import { fetchData } from '@shared/utils/dataFetcher';

// Path to the categories data in the R2 bucket
const CATEGORIES_PATH = 'library/categories.json';

/**
 * Service to manage category-related operations, fetching data from R2 storage.
 */
export const categoryService = {
  /**
   * Fetches all categories from R2 storage, utilizing the centralized cache in fetchData.
   */
  async getAllCategories(): Promise<ICategory[]> {
    try {
      console.log(`[categoryService] Fetching categories from R2 via dataFetcher: ${CATEGORIES_PATH}`);
      const categoriesData: ICategory[] = await fetchData<ICategory[]>(CATEGORIES_PATH);
      console.log(`[categoryService] Categories fetched successfully. Count: ${categoriesData.length}`);
      return categoriesData;
    } catch (error) {
      console.error(`[categoryService] Failed to fetch categories from R2 path: ${CATEGORIES_PATH}`, error);
      // On error, return an empty array to prevent UI crashes.
      return [];
    }
  },

  /**
   * Gets a category by its ID.
   */
  async getCategoryById(id: number): Promise<ICategory | undefined> {
    const allCategories = await this.getAllCategories();
    return allCategories.find(category => category.id === id);
  }
}; 