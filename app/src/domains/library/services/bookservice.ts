import { IBook } from '../models/types';
import { fetchData } from '@shared/utils/dataFetcher';

// Path to the books data in the R2 bucket
const BOOKS_PATH = 'library/books.json';

/**
 * Service to manage book-related operations, fetching data from R2 storage.
 */
export const bookService = {
  /**
   * Fetches all books from R2 storage, utilizing the centralized cache in fetchData.
   */
  async getAllBooks(): Promise<IBook[]> {
    try {
      console.log(`[bookService] Fetching books from R2 via dataFetcher: ${BOOKS_PATH}`);
      const booksData: IBook[] = await fetchData<IBook[]>(BOOKS_PATH);
      console.log(`[bookService] Books fetched successfully. Count: ${booksData.length}`);
      return booksData;
    } catch (error) {
      console.error(`[bookService] Failed to fetch books from R2 path: ${BOOKS_PATH}`, error);
      // On error, return an empty array to prevent UI crashes and re-throw for upstream handling.
      return [];
    }
  },

  /**
   * Gets a book by its ID.
   */
  async getBookById(id: number): Promise<IBook | undefined> {
    const allBooks = await this.getAllBooks();
    return allBooks.find(book => book.id === id);
  },

  /**
   * Filters books by category ID.
   */
  async getBooksByCategory(categoryId: number): Promise<IBook[]> {
    const allBooks = await this.getAllBooks();
    return allBooks.filter(book => book.category_id === categoryId);
  },

  /**
   * Searches for books by title or author.
   */
  async searchBooks(query: string): Promise<IBook[]> {
    const searchTerm = query.toLowerCase().trim().replace(/[-''`´\s]/g, '');

    if (!searchTerm) {
      return this.getAllBooks();
    }

    const allBooks = await this.getAllBooks();

    return allBooks.filter(book => {
      const bookTitle = book.title.toLowerCase().replace(/[-''`´\s]/g, '');
      const bookAuthor = book.author.toLowerCase().replace(/[-''`´\s]/g, '');

      return bookTitle.includes(searchTerm) || bookAuthor.includes(searchTerm);
    });
  }
}; 