import { useState, useEffect, useRef } from 'react';
import { IBook } from '@library/models/types';
// import { fetchDataFromR2 } from '@shared/utils/r2DataFetcher'; // Use R2 data fetcher
import { fetchData } from '@shared/utils/dataFetcher'; // Use local data fetcher

// REMOVED: Base URL and cache Map are now handled by fetchData
// const LOCAL_DATA_BASE_URL = '/data';
// const cache = new Map<string, IBook[]>();

// The path to the books data - remove leading slash for R2
const BOOKS_PATH = '/library/books.json';

export function useBookService() {
  const [books, setBooks] = useState<IBook[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef<boolean>(false);

  useEffect(() => {
    isMountedRef.current = true;
    setBooks([]); // Reset state on mount/rerun
    setError(null);
    setLoading(true);

    fetchData<IBook[]>(BOOKS_PATH)
      .then((data: IBook[]) => { // Add explicit type for data
        if (isMountedRef.current) {
           // Optional: Sort books if needed (e.g., by title)
           // data.sort((a, b) => a.title.localeCompare(b.title));
           setBooks(data);
        }
      })
      .catch((err: unknown) => { // Use unknown or Error for err type
         console.error("[useBookService] Error fetching books:", err);
         if (isMountedRef.current) {
            // Type check before setting error state
            setError(err instanceof Error ? err : new Error('Kitaplar yüklenirken bilinmeyen bir hata oluştu.'));
         }
      })
      .finally(() => {
         if (isMountedRef.current) {
            setLoading(false);
         }
      });

    // Cleanup function
    return () => {
      isMountedRef.current = false;
    };
  }, []); // Fetch only once on mount

  return { books, loading, error };
} 