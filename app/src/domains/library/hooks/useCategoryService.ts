import { useState, useEffect, useRef } from 'react';
import { ICategory } from '@library/models/types';
// import { fetchDataFromR2 } from '@shared/utils/r2DataFetcher'; // Use R2 data fetcher
import { fetchData } from '@shared/utils/dataFetcher'; // Use local data fetcher

// Path to the categories data - remove leading slash for R2
const CATEGORIES_PATH = '/library/categories.json';

export function useCategoryService() {
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef<boolean>(false);

  useEffect(() => {
    isMountedRef.current = true;
    setCategories([]); // Reset state
    setError(null);
    setLoading(true);

    fetchData<ICategory[]>(CATEGORIES_PATH)
      .then((data: ICategory[]) => {
        if (isMountedRef.current) {
          setCategories(data);
        }
      })
      .catch((err: unknown) => {
        console.error("[useCategoryService] Error fetching categories:", err);
        if (isMountedRef.current) {
          setError(err instanceof Error ? err : new Error('Kategoriler yüklenirken bilinmeyen bir hata oluştu.'));
        }
      })
      .finally(() => {
        if (isMountedRef.current) {
          setLoading(false);
        }
      });

    // Cleanup function
    return () => {
      isMountedRef.current = false;
    };
  }, []); // Fetch only once

  return { categories, loading, error };
} 