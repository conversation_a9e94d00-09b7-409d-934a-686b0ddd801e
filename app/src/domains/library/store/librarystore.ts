import { create } from 'zustand';
import { IBook, ICategory, ILoadingState, ISearchParams } from '../models/types';
import { bookService } from '../services/bookservice';
import { categoryService } from '../services/categoryservice';

/**
 * Library domaininin state'ini ve işlevlerini yöneten Zustand store
 */
interface LibraryState {
  // State
  books: IBook[];
  categories: ICategory[];
  searchParams: ISearchParams;
  loading: ILoadingState;
  
  // Actions
  fetchBooks: () => Promise<void>;
  fetchCategories: () => Promise<void>;
  searchBooks: (query: string) => Promise<void>;
  getBooksByCategory: (categoryId: number) => IBook[];
  
  // Action Helpers
  resetSearch: () => void;
  setSearchLoading: (isLoading: boolean) => void;
}

export const useLibraryStore = create<LibraryState>((set, get) => ({
  // Initial state
  books: [],
  categories: [],
  searchParams: {
    query: '',
  },
  loading: {
    books: false,
    categories: false,
  },
  
  // Fetch all books
  fetchBooks: async () => {
    set(state => ({ loading: { ...state.loading, books: true } }));
    
    try {
      const books = await bookService.getAllBooks();
      set({ books, loading: { ...get().loading, books: false } });
    } catch (error) {
      console.error('Failed to fetch books:', error);
      set(state => ({ loading: { ...state.loading, books: false } }));
    }
  },
  
  // Fetch all categories
  fetchCategories: async () => {
    set(state => ({ loading: { ...state.loading, categories: true } }));
    
    try {
      const categories = await categoryService.getAllCategories();
      set({ categories, loading: { ...get().loading, categories: false } });
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      set(state => ({ loading: { ...state.loading, categories: false } }));
    }
  },
  
  // Search books
  searchBooks: async (query: string) => {
    set(state => ({ 
      searchParams: { ...state.searchParams, query },
      loading: { ...state.loading, books: true } 
    }));
    
    try {
      const books = await bookService.searchBooks(query);
      set({ books, loading: { ...get().loading, books: false } });
    } catch (error) {
      console.error('Failed to search books:', error);
      set(state => ({ loading: { ...state.loading, books: false } }));
    }
  },
  
  // Get books filtered by category
  getBooksByCategory: (categoryId: number) => {
    return get().books.filter(book => book.category_id === categoryId);
  },
  
  // Reset search
  resetSearch: () => {
    set(state => ({ 
      searchParams: { ...state.searchParams, query: '' } 
    }));
    get().fetchBooks();
  },
  
  // Set loading state for search
  setSearchLoading: (isLoading: boolean) => {
    set(state => ({ 
      loading: { ...state.loading, books: isLoading } 
    }));
  }
})); 