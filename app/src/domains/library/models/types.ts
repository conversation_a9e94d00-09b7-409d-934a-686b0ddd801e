// Base entity interface
export interface IEntity {
  id: number;
  slug?: string; // SEO dostu URL'ler için
}

// Kitap
export interface IBook extends IEntity {
  title: string;
  author: string;
  description: string;
  cover_image: string;
  category_id: number;
  is_available: boolean;
  arabic_title?: string;
  arabic_author?: string;
}

// Kitap kategorisi
export interface ICategory extends IEntity {
  name: string;
  description: string;
  gradient_start: string;
  gradient_end: string;
}

// Search parametreleri
export interface ISearchParams {
  query: string;
  categoryId?: number;
}

// UI için loading state'leri
export interface ILoadingState {
  books: boolean;
  categories: boolean;
}

// Tema türü
export type ThemeMode = 'dark' | 'light' | 'krem';

// Tema renkleri
export interface IThemeColors {
  text: string;
  bg: string;
} 