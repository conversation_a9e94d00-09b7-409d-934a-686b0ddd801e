// Base entity interface (reusing from library domain)
import { IEntity } from '../../../domains/library/models/types';
// import { ContentMode } from './hooks/quran/usecontentmode'; // Eski yanlış yol
// import { ContentMode } from '../hooks/quran/usecontentmode'; // Artık kullanılmıyor

// --- Quran Domain Types ---

// --- Chunk Data Structures ---
// These interfaces represent the data structure within the chunk files

export interface VerseArabicChunk {
  verse_no: number;
  arabic_text: string;
}

export interface WordMeaningChunk {
  turkish_meali: string;
}

export interface VerseWordMeaningsChunk {
  verse_no: number;
  words: MeaningWordChunk[];
}

export interface WordAnalysisFieldsChunk {
  english_meaning: string;
  arapca_gramer: string;
  zaman: string;
  turkce_gramer: string;
  sarf_bilgisi: string;
  bab: string;
  kok: string;
}

export interface VerseWordAnalysisChunk {
  verse_no: number;
  words: WordAnalysisFieldsChunk[];
}

// --- End of Chunk Data Structures ---

// YENİ: word_turkish_meaning chunk'larındaki kelime yapısı
export interface MeaningWordChunk {
  arabic_word: string;
  turkish_meaning: string; // Betikten gelen isim
}

// GÜNCELLENDİ: useVerseLoader tarafından UI component'lerine sağlanan kelime yapısı
export interface VerseWord {
  arabic: string;
  meaning: string; // UI için standartlaştırılmış isim ('turkish_meaning' yerine)
  analysis?: WordAnalysisFieldsChunk;
}

// Define ITranslator here so it's available for other types
export interface ITranslator {
    id: string;
    name: string;
}

// Ayet çeviri verisinin tip tanımı (group dosyalarındaki yapıya göre)
export interface TranslationData {
  paragraphs: string[];
  footnotes?: string[];
}

// Çeviri objesinin tipi - DÜZELTİLDİ: Tüm ayetleri içermeli
export type VerseTranslationsMap = {
  [verseKey: string]: { // Anahtar: Ayet numarası (string olarak)
    [translatorId: string]: TranslationData; // İçerik: Çevirmen ID'sine göre TranslationData
  };
} | null;

// Removed commented-out legacy Verse types

// GÜNCELLENDİ: useVerseLoader tarafından UI component'lerine sağlanan ayet yapısı
// (Mevcutsa kontrol et, yoksa ekle)
export interface CombinedVerseData {
  verse_no: number;
  arabic_text: string | null; // Mode 1, 2 için
  words?: VerseWord[];        // Mode 3 için
  translations: { [translatorId: string]: TranslationData } | null; // Mode 2, 3, 4 için
  sajdah?: boolean; // Secde ayeti bilgisi
}

// İçerik tipi (base content type)
export interface IContent extends IEntity {
  bookId: number;
  chapterId?: number;
  title: string;
  content: string;
  order?: number;
}

// Kuran-ı Kerim tipi
export interface IQuranVerse extends IContent {
  surahId?: number;
  chapterId: number;
  verseNumber: number;
  pageNumber?: number;
  isSajdahVerse?: boolean;
  arabicText?: string;
  translation?: string;
  transliteration?: string;
  tafsirRef?: number;
  wordByWordTranslation?: Array<{
    arabic: string;
    turkish: string;
  }>;
}

// Risale tipi
export interface IRisaleSection extends IContent {
  chapterId: number;
  sectionNumber?: number;
  bookSection?: string;
  isImportant?: boolean;
  keywords?: string[];
  notes?: string[];
}

// Tefsir tipi
export interface ITafsirSection extends IContent {
  chapterId: number;
  verseRef?: number;
  verseReference?: string;
  author?: string;
  sourceText?: string;
  authorNotes?: string;
}

// Okuma Pozisyonu
export interface IReadingPosition {
  userId: number;
  bookId: number;
  contentId?: number;
  chapterId?: number;
  sectionId?: number;
  pageNumber?: number;
  scrollPosition?: number;
  lastReadDate: Date;
}

// Okuma ayarları
export interface IReadingSettings {
  fontSize: number;
  lineHeight: number;
  fontFamily: string;
  textAlignment: 'left' | 'center' | 'right' | 'justify';
  showFootnotes: boolean;
  arabicFontSize: number;
  translationFontSize: number;
  showWordByWord: boolean;
  selectedTranslators?: string[];
}

// Okuyucu UI durumu
export interface IReaderUIState {
  sidebarOpen: boolean;
  footnotesPanelOpen: boolean;
  bookmarksVisible: boolean;
  notesVisible: boolean;
  searchVisible: boolean;
  tableOfContentsVisible: boolean;
  isActionSheetOpen: boolean;
  actionSheetVerseKey: string | null;
  actionSheetAnchorEl: HTMLElement | null; // Popover'ın bağlanacağı element
  isNavigationSheetOpen: boolean;
  navigationType: 'surah' | 'verse';
  isWordDetailSheetOpen: boolean;
  wordDetailSheetVerseKey: string | null;
}

// Okuma istatistikleri
export interface IReadingStats {
  userId: number;
  bookId: number;
  totalTimeSpent: number; // milliseconds
  lastReadDate: Date;
  visitCount: number;
  chaptersCompleted: number[];
}

// Yer İmi
export interface IBookmark extends IEntity {
  userId: number;
  bookId: number;
  contentId?: number;
  chapterId?: number;
  sectionId?: number;
  title: string;
  text: string;
  createdAt: Date;
}

// Not
export interface INote extends IEntity {
  userId: number;
  bookId: number;
  contentId?: number;
  chapterId?: number;
  sectionId?: number;
  selectedText: string;
  highlightColor?: string;
  note: string;
  createdAt: Date;
  updatedAt?: Date;
}

// Reader UI için loading state'leri
export interface IReaderLoadingState {
  settings?: boolean; // Keep only settings loading state
}

/**
 * Quran Domain Types
 */
export interface Surah {
  id: number;
  name: string;
  arabic_name: string;
  revelation_place: string;
  verse_count: number;
}

export interface Book {
  id: number;
  title: string;
  author?: string;
  category_id?: number;
  is_public?: boolean;
}

/**
 * Risale Domain Types
 */
export interface RisaleSectionDef { // Renamed from RisaleSection to avoid naming conflict
  id: string;
  title: string;
}

export interface RisaleFootnote {
  number: string;
  content: string;
}

// Represents a single "sentence" or text block within a Risale section's content
export type RisaleSentence = {
    id?: string;
    text?: string;
    type?: 'title' | string;
    alignment?: 'left' | 'center' | 'right';
    role?: 'divider';
    paragraph_start?: boolean;
    verse_break_after?: boolean;
    line_break?: boolean;
    divider_after?: boolean;
};

// Represents a dictionary entry for a word found in a Risale section.
export type RisaleDictionaryItem = {
  word: string;
  meaning: string;
}

export interface RisaleArabicPhrase { // Added new interface for Arabic phrases
  arabic: string;
  translation: string;
  // Optional: Add other properties if your JSON has them, e.g., id, notes, etc.
}

/**
 * Risale Domain Types
 */
export interface RisaleContentData {
  sentences: RisaleSentence[];
  footnotes: RisaleFootnote[];
  dictionary?: RisaleDictionaryItem[];
  arabic_phrases?: RisaleArabicPhrase[]; // Added optional arabic_phrases array
}

/**
 * Tafsir Domain Types
 */
// Tafsir Surah definition - similar to RisaleSectionDef
export type TafsirSurahDef = {
  id: string;
  name: string;
  arabic_name: string;
  verse_count?: number;
  revelation_place?: string;
};

export interface TafsirFootnote {
  number: string;
  content: string;
}

// Represents a single "sentence" or text block within a Tafsir surah's content
export type TafsirSentence = {
  id?: string;
  arabic_text?: string; // Main difference from Risale - contains Arabic text
  text?: string; // Turkish interpretation/explanation
  type?: 'normal' | 'alert-info' | string; // Deprecated - no longer used in data after cleanup
  alignment?: 'left' | 'center' | 'right';
  paragrap_start?: boolean; // Deprecated - no longer used in data after cleanup
  paragraph_start?: boolean; // Deprecated - no longer used in data after cleanup
  verse_break_after?: boolean;
  line_break?: boolean;
};

// Represents a verse containing multiple sentences
export interface TafsirVerse {
  verse_no: number;
  sentences: TafsirSentence[];
  footnotes: TafsirFootnote[];
}

// Main content structure for a Tafsir surah
export interface TafsirContentData {
  introduction?: TafsirSentence[];
  verses: TafsirVerse[];
}

// Book index for Tafsir books
export interface TafsirBookIndexModule {
  metadata: {
    id: string;
    title: string;
    author?: string;
    [key: string]: unknown;
  };
  structure: {
    surahs: TafsirSurahDef[];
    [key: string]: unknown;
  };
}

// Combined state type for useTafsirSurah hook
export interface TafsirSurahStateData {
  content: TafsirContentData;
  title: string;
  book: Book;
  structure: {
    surahs: TafsirSurahDef[];
    prevSurah: TafsirSurahDef | null;
    nextSurah: TafsirSurahDef | null;
    [key: string]: unknown;
  };
  metadata?: {
    title?: string;
    author?: string;
    [key: string]: unknown;
  };
}

/**
 * Shared Hook/Module Related Types
 */
// Used by data loading hooks for modules that might have default export
export type DynamicImportResult<T> = T | { default: T };

// Specific module types (interfaces combining base types)
export interface BookIndexModule { // For Risale index
  metadata: {
    id: string;
    title: string;
    author?: string;
    [key: string]: unknown;
  };
  structure: {
    sections: RisaleSectionDef[];
    [key: string]: unknown;
  };
}

export interface ContentModule { // For Risale content
  content: RisaleContentData;
}

// Combined state type for useRisaleSection hook
export interface SectionStateData { // For Risale reading page state
  content: RisaleContentData;
  title: string;
  structure: {
    sections: RisaleSectionDef[];
    prevSection: RisaleSectionDef | null;
    nextSection: RisaleSectionDef | null;
    [key: string]: unknown;
  };
  metadata?: {
    title?: string;
    author?: string;
    [key: string]: unknown;
  };
}

// Represents a single book entry from the main library/books.json file
export interface LibraryBook {
  id: number;
  title: string;
  author: string;
  description: string;
  category_id: number;
  is_available: boolean;
}

// Represents the raw, untransformed data structure of a book's index.json file
export interface RawBookIndexData {
  id: string;
  title: string;
  author?: string;
  sections: Array<{ id: string; title: string; [key: string]: any }>;
  [key: string]: any;
}

// GÜNCELLENDİ: useVerseLoader'ın döndürdüğü tip
export interface UseVerseLoaderReturn {
  verses: CombinedVerseData[] | null; // combinedVerses -> verses olarak değiştirildi
  loading: boolean;
  error: Error | null;
  availableTranslators: ITranslator[]; // Now ITranslator is defined above
  totalVerseCount: number | null; // Eksik alan eklendi
  surahInfo: Surah | null; // Eksik alan eklendi
}

// useSurahs'ın döndürdüğü tip
export interface UseSurahsReturn {
  data: Surah[] | null;
  loading: boolean;
  error: Error | null;
}

// Diğer hook ve component tipleri...
// ... (varsa dosyanın geri kalanı) ...

// export type ViewMode = '1' | '2' | '3' | '4'; // Bu ContentMode olarak zaten var
