// Bu store şu anda kullanılmamaktadır.

import { create } from 'zustand';
import { 
  // IBookmark, // Remove unused import
  // IContent, // Remove unused import
  // INote, // Remove unused import
  IReaderLoadingState, 
  IReaderUIState, // Keep UI state for now, might be used soon
  // IReadingPosition, // Remove unused import
  IReadingSettings 
} from '../models/types';
// import { contentService } from '../services/contentservice'; // Remove unused import
import { readerSettingsService } from '../services/readersettingsservice';

// Reader domain state'ini ve işlevlerini yöneten Zustand store
interface ReaderState {
  // State
  // currentBookId: number | null; // Remove - Managed by page params/hooks
  // currentContent: IContent[]; // Remove - Managed by hooks like useVerseLoader
  // bookmarks: IBookmark[]; // Remove - Feature not implemented
  // notes: INote[]; // Remove - Feature not implemented
  // readingPosition: IReadingPosition | null; // Remove - Feature not implemented
  readingSettings: IReadingSettings; // Keep - Actively used
  uiState: IReaderUIState; // Keep - Might be used soon for sidebar etc.
  loading: IReaderLoadingState; // Keep - Only settings part used now
  expandedFootnotes: Record<string, boolean>; // Added state for expanded footnotes
  
  // Content actions - Remove
  // setCurrentBook: (bookId: number) => void;
  // fetchContent: (bookId: number) => Promise<void>;
  // fetchChapterContent: (bookId: number, chapterId: number) => Promise<void>;
  // searchInContent: (query: string) => Promise<void>;
  
  // Bookmark actions - Remove
  // fetchBookmarks: (userId: number, bookId: number) => Promise<void>;
  // addBookmark: (bookmark: Omit<IBookmark, 'id'>) => Promise<void>;
  // deleteBookmark: (bookmarkId: number) => Promise<void>;
  
  // Note actions - Remove
  // fetchNotes: (userId: number, bookId: number) => Promise<void>;
  // addNote: (note: Omit<INote, 'id'>) => Promise<void>;
  // updateNote: (noteId: number, updates: Partial<INote>) => Promise<void>;
  // deleteNote: (noteId: number) => Promise<void>;
  
  // Reading position actions - Remove
  // fetchReadingPosition: (userId: number, bookId: number) => Promise<void>;
  // saveReadingPosition: (position: IReadingPosition) => Promise<void>;
  
  // Settings actions - Keep
  fetchReadingSettings: (userId: number) => Promise<void>;
  updateReadingSettings: (userId: number, settings: Partial<IReadingSettings>) => Promise<void>;
  resetReadingSettings: (userId: number) => Promise<void>;
  
  // UI state actions - Keep for now
  toggleSidebar: () => void;
  toggleFootnotesPanel: () => void;
  toggleBookmarksVisibility: () => void;
  toggleNotesVisibility: () => void;
  toggleSearchVisibility: () => void;
  toggleTableOfContentsVisibility: () => void;
  
  // Verse Action Sheet actions
  openVerseActionSheet: (verseKey: string, anchorEl: HTMLElement | null) => void;
  closeVerseActionSheet: () => void;

  // Navigation Sheet actions
  openNavigationSheet: (type: 'surah' | 'verse') => void;
  closeNavigationSheet: () => void;
  setNavigationType: (type: 'surah' | 'verse') => void;

  // Word Detail Sheet actions
  openWordDetailSheet: (verseKey: string) => void;
  closeWordDetailSheet: () => void;

  // Footnote action - Added
  toggleFootnote: (verseKey: string) => void;
}

// Default UI state - Keep
const defaultUIState: IReaderUIState = {
  sidebarOpen: false,
  footnotesPanelOpen: false,
  bookmarksVisible: false,
  notesVisible: false,
  searchVisible: false,
  tableOfContentsVisible: false,
  // Action Sheet State
  isActionSheetOpen: false,
  actionSheetVerseKey: null,
  actionSheetAnchorEl: null, // Popover'ın bağlanacağı element
  // Navigation Sheet State
  isNavigationSheetOpen: false,
  navigationType: 'surah',
  // Word Detail Sheet State
  isWordDetailSheetOpen: false,
  wordDetailSheetVerseKey: null,
};

// Default reading settings - Keep
const defaultReadingSettings: IReadingSettings = {
  fontSize: 16,
  lineHeight: 1.6,
  fontFamily: 'Geist, sans-serif',
  textAlignment: 'left',
  showFootnotes: true,
  arabicFontSize: 24,
  translationFontSize: 14,
  showWordByWord: false,
  selectedTranslators: ['diyanet_isleri', 'omer_nasuhi_bilmen'], 
};

// Default loading state - Simplify
const defaultLoadingState: IReaderLoadingState = {
  // content: false, // Remove
  // bookmarks: false, // Remove
  // notes: false, // Remove
  // position: false, // Remove
  settings: false, // Keep only settings loading state
};

export const useReaderStore = create<ReaderState>()((set, get) => ({
  // Initial state - Simplify
  // currentBookId: null,
  // currentContent: [],
  // bookmarks: [],
  // notes: [],
  // readingPosition: null,
  readingSettings: defaultReadingSettings,
  uiState: defaultUIState,
  loading: defaultLoadingState,
  expandedFootnotes: {}, // Initialize expandedFootnotes
  
  // Content actions - Remove implementations
  
  // Bookmark actions - Remove implementations

  // Note actions - Remove implementations
  
  // Reading position actions - Remove implementations
  
  // Settings actions - Keep implementations
  fetchReadingSettings: async (userId: number) => {
    set(state => ({ loading: { ...state.loading, settings: true } })); 
    try {
      const settings = await readerSettingsService.getReadingSettings(userId); 
      const mergedSettings = { 
        ...defaultReadingSettings, 
        ...settings, 
        selectedTranslators: Array.isArray(settings.selectedTranslators) ? settings.selectedTranslators : [] 
      };
      set({
        readingSettings: mergedSettings,
        loading: { ...get().loading, settings: false }, 
      });
    } catch (error) {
      console.error('Failed to fetch reading settings:', error);
      set(state => ({ loading: { ...state.loading, settings: false } })); 
    }
  },

  updateReadingSettings: async (userId: number, settings: Partial<IReadingSettings>) => {
    const currentSettings = get().readingSettings;
    const newSettings = { ...currentSettings, ...settings };
    if (Object.prototype.hasOwnProperty.call(settings, 'selectedTranslators')) {
      if (Array.isArray(settings.selectedTranslators)) {
        newSettings.selectedTranslators = settings.selectedTranslators;
      } else {
        console.warn('selectedTranslators must be an array, reverting to previous or default.', settings.selectedTranslators);
        newSettings.selectedTranslators = currentSettings.selectedTranslators || []; 
      }
    } 
    
    // Apply settings locally first for immediate UI update
    set({ readingSettings: newSettings }); 
    
    // Set loading state just before the async call
    set(state => ({ loading: { ...state.loading, settings: true } })); 
    try {
      await readerSettingsService.saveReadingSettings(userId, newSettings); 
      // Set loading false on success
      set(state => ({ loading: { ...state.loading, settings: false } })); 
    } catch (error) {
      console.error('Failed to save reading settings:', error);
      // Revert to previous settings on error
      set({ readingSettings: currentSettings }); 
      // Ensure loading is set to false even on error
      set(state => ({ loading: { ...state.loading, settings: false } })); 
    }
  },

  resetReadingSettings: async (userId: number) => {
    set(state => ({ loading: { ...state.loading, settings: true } }));
    try {
      const defaultSettings = await readerSettingsService.resetToDefaults(userId);
      const resetSettings = {
        ...defaultSettings,
        selectedTranslators: Array.isArray(defaultSettings.selectedTranslators) ? defaultSettings.selectedTranslators : []
      };
      set({
        readingSettings: resetSettings,
        loading: { ...get().loading, settings: false }
      });
    } catch (error) {
      console.error('Failed to reset reading settings:', error);
      set(state => ({ loading: { ...state.loading, settings: false } }));
    }
  },
  
  // UI state actions - Keep implementations
  toggleSidebar: () => {
    set(state => ({ 
      uiState: { ...state.uiState, sidebarOpen: !state.uiState.sidebarOpen } 
    }));
  },
  
  toggleFootnotesPanel: () => {
    set(state => ({ 
      uiState: { ...state.uiState, footnotesPanelOpen: !state.uiState.footnotesPanelOpen } 
    }));
  },
  
  toggleBookmarksVisibility: () => {
    set(state => ({ 
      uiState: { ...state.uiState, bookmarksVisible: !state.uiState.bookmarksVisible } 
    }));
  },
  
  toggleNotesVisibility: () => {
    set(state => ({ 
      uiState: { ...state.uiState, notesVisible: !state.uiState.notesVisible } 
    }));
  },
  
  toggleSearchVisibility: () => {
    set(state => ({ 
      uiState: { ...state.uiState, searchVisible: !state.uiState.searchVisible } 
    }));
  },
  
  toggleTableOfContentsVisibility: () => {
    set(state => ({ 
      uiState: { ...state.uiState, tableOfContentsVisible: !state.uiState.tableOfContentsVisible } 
    }));
  },
  
  // Verse Action Sheet actions
  openVerseActionSheet: (verseKey: string, anchorEl: HTMLElement | null) => {
    set(state => ({
      uiState: {
        ...state.uiState,
        isActionSheetOpen: true,
        actionSheetVerseKey: verseKey,
        actionSheetAnchorEl: anchorEl, // Gelen anchor element'i state'e kaydet
      }
    }));
  },

  closeVerseActionSheet: () => {
    set(state => ({
      uiState: {
        ...state.uiState,
        isActionSheetOpen: false,
        actionSheetVerseKey: null,
        actionSheetAnchorEl: null, // Menü kapandığında anchor'ı temizle
      }
    }));
  },

  // Navigation Sheet actions
  openNavigationSheet: (type: 'surah' | 'verse') => {
    set(state => ({
      uiState: {
        ...state.uiState,
        isNavigationSheetOpen: true,
        navigationType: type,
      }
    }));
  },

  closeNavigationSheet: () => {
    set(state => ({
      uiState: {
        ...state.uiState,
        isNavigationSheetOpen: false,
      }
    }));
  },

  setNavigationType: (type: 'surah' | 'verse') => {
    set(state => ({
      uiState: {
        ...state.uiState,
        navigationType: type,
      }
    }));
  },

  // Word Detail Sheet actions
  openWordDetailSheet: (verseKey: string) => {
    set(state => ({
      uiState: {
        ...state.uiState,
        isWordDetailSheetOpen: true,
        wordDetailSheetVerseKey: verseKey,
      }
    }));
  },

  closeWordDetailSheet: () => {
    set(state => ({
      uiState: {
        ...state.uiState,
        isWordDetailSheetOpen: false,
        wordDetailSheetVerseKey: null,
      }
    }));
  },

  // Footnote action - Added implementation
  toggleFootnote: (verseKey: string) => {
    set((state) => ({
      expandedFootnotes: {
        ...state.expandedFootnotes,
        [verseKey]: !state.expandedFootnotes[verseKey] // Toggle the specific key
      }
    }));
  },
})); 