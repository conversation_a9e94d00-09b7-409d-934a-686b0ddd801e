import { IReadingPosition, IReadingSettings } from '../models/types';

// Varsayılan okuma ayarları
const DEFAULT_READING_SETTINGS: IReadingSettings = {
  fontSize: 16,
  lineHeight: 1.6,
  fontFamily: 'Geist, sans-serif',
  textAlignment: 'left',
  showFootnotes: true,
  arabicFontSize: 24,
  translationFontSize: 14,
  showWordByWord: false,
};

/**
 * Okuma ayarları servisi - Kullanıcı okuma ayarlarını yönetir
 */
export const readerSettingsService = {
  /**
   * Kullanıcı okuma ayarlarını getirir
   */
  async getReadingSettings(userId: number): Promise<IReadingSettings> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'dan okuyoruz
    try {
      const storedSettings = localStorage.getItem(`reader_settings_${userId}`);
      if (storedSettings) {
        return JSON.parse(storedSettings);
      }
      return DEFAULT_READING_SETTINGS;
    } catch (error) {
      console.error('Failed to get reading settings:', error);
      return DEFAULT_READING_SETTINGS;
    }
  },
  
  /**
   * Kullanıcı okuma ayarlarını kaydeder
   */
  async saveReadingSettings(userId: number, settings: IReadingSettings): Promise<IReadingSettings> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'a kaydediyoruz
    try {
      localStorage.setItem(`reader_settings_${userId}`, JSON.stringify(settings));
      return settings;
    } catch (error) {
      console.error('Failed to save reading settings:', error);
      return settings;
    }
  },
  
  /**
   * Kullanıcının bir kitapta kaldığı yeri (okuma pozisyonu) getirir
   */
  async getReadingPosition(userId: number, bookId: number): Promise<IReadingPosition | null> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'dan okuyoruz
    try {
      const storedPosition = localStorage.getItem(`reading_position_${userId}_${bookId}`);
      if (storedPosition) {
        const position = JSON.parse(storedPosition);
        // Date objesi olarak dönüştür
        position.lastReadDate = new Date(position.lastReadDate);
        return position;
      }
      return null;
    } catch (error) {
      console.error('Failed to get reading position:', error);
      return null;
    }
  },
  
  /**
   * Kullanıcının bir kitapta kaldığı yeri (okuma pozisyonu) kaydeder
   */
  async saveReadingPosition(position: IReadingPosition): Promise<IReadingPosition> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'a kaydediyoruz
    try {
      localStorage.setItem(
        `reading_position_${position.userId}_${position.bookId}`, 
        JSON.stringify(position)
      );
      return position;
    } catch (error) {
      console.error('Failed to save reading position:', error);
      return position;
    }
  },
  
  /**
   * Varsayılan okuma ayarlarına sıfırlar
   */
  async resetToDefaults(userId: number): Promise<IReadingSettings> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'a kaydediyoruz
    try {
      localStorage.setItem(`reader_settings_${userId}`, JSON.stringify(DEFAULT_READING_SETTINGS));
      return DEFAULT_READING_SETTINGS;
    } catch (error) {
      console.error('Failed to reset reading settings:', error);
      return DEFAULT_READING_SETTINGS;
    }
  }
}; 