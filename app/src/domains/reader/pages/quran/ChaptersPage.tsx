import { useState, useMemo, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSurahs } from '@reader/hooks';
import {
  PageLayout,
  SearchBar,
  ContentCard,
  EmptyState,
  LoadingState
} from '@shared/components';
import { Surah } from '@reader/models/types';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { usePageLayout, useErrorHandling } from '@reader/hooks';
import { CONTAINER_SIZES, SPACING, GRID_LAYOUTS } from '@reader/constants/layout';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';
import { useScrollToTop } from '@shared/hooks/useScrollToTop';

const ChaptersPage = () => {
  const { data: surahs, loading, error } = useSurahs();
  const navigate = useNavigate();
  const location = useLocation();
  const search = new URLSearchParams(location.search).get('search') || '';
  const [searchTerm, setSearchTerm] = useState(search);
  const isMobile = useIsMobile();
  const { renderErrorState } = useErrorHandling();

  // Sayfa yüklendiğinde en üste scroll et
  useScrollToTop();

  // Ekstra scroll to top - component mount olduğunda
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Filtrelenen sureler (Hook'tan gelen veriye göre)
  const filteredSurahsMemo = useMemo(() => {
    if (!surahs) return [];
    return filterByNormalizedQuery(surahs, searchTerm, (surah) => [
      surah.name,
      surah.arabic_name,
      surah.id.toString()
    ]);
  }, [surahs, searchTerm]);

  // URL'den uygulama modunu belirle (pathname'e göre)
  const pathname = location.pathname;
  const getUrlForSurah = (surahId: number) => {
    const pathname = location.pathname;
    let baseUrl = '/kuran'; // Default

    // Check for the most specific path first
    if (pathname.includes('kelime-mealli-kuran-sureler')) {
      baseUrl = '/kelime-mealli-kuran';
    } else if (pathname.includes('mealli-kuran-sureler')) {
      baseUrl = '/mealli-kuran';
    } else if (pathname.includes('kuran-meali-sureler')) {
      baseUrl = '/kuran-meali';
    }

    const finalPath = `${baseUrl}/${surahId}`;
    return finalPath;
  };

  // URL'e göre sayfa başlığını belirle
  const getPageTitle = () => {
    if (pathname.includes('kelime-mealli-kuran-sureler')) {
      return "Kur'an-ı Kerim (Kelime Mealli)";
    } else if (pathname.includes('mealli-kuran-sureler')) {
      return "Kur'an-ı Kerim (Mealli)";
    } else if (pathname.includes('kuran-meali-sureler')) {
      return "Kur'an Meali";
    }
    return "Kur'an-ı Kerim";
  };

  const pageLayoutConfig = usePageLayout({
    title: getPageTitle(),
    isMobile
  });

  // Yükleniyor durumu
  if (loading) {
    return (
      <PageLayout {...pageLayoutConfig}>
        <LoadingState message="Sureler yükleniyor..." />
      </PageLayout>
    );
  }

  // Hata durumu
  if (error) {
    return (
      <PageLayout {...pageLayoutConfig}>
        {renderErrorState({ error }, 'Sure listesi')}
      </PageLayout>
    );
  }

  // Ana render
  return (
    <PageLayout {...pageLayoutConfig}>
      {/* SearchBar */}
      <div className={`mt-4 mb-4 ${CONTAINER_SIZES.default} mx-auto ${SPACING.container}`}>
        <SearchBar
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Sure ara..."
        />
      </div>

      <div className={`${CONTAINER_SIZES.default} mx-auto ${SPACING.container} ${SPACING.section}`}>
        {/* Sure kartları */}
        {filteredSurahsMemo.length > 0 ? (
          <div className={`grid ${GRID_LAYOUTS.chapters} ${SPACING.card}`}>
            {filteredSurahsMemo.map((surah: Surah) => (
              <div key={surah.id} className="h-full">
                <ContentCard
                  title={`${surah.id}. ${surah.name}`}
                  subtitle={surah.arabic_name}
                  metadata={
                    <div className="w-full flex justify-between">
                      <span>{surah.revelation_place}</span>
                      <span>{surah.verse_count} Ayet</span>
                    </div>
                  }
                  onClick={() => {
                    // URL'yi kullanarak hedef yolu oluştur
                    const targetPath = getUrlForSurah(surah.id);
                    navigate(targetPath);
                  }}
                />
              </div>
            ))}
          </div>
        ) : (
          // Sure bulunamadı veya arama sonucu yok
          <EmptyState message={searchTerm ? "Arama sonucu bulunamadı" : "Sure bulunamadı"} />
        )}
      </div>
    </PageLayout>
  );
};

export default ChaptersPage;
