import { useCallback } from 'react';
import { PageLayout, EnhancedErrorState } from '@shared/components';
import { useReadPageLogic } from '@reader/hooks/quran/useReadPageLogic';
import { ReadPageHeader } from './Components/ReadPage/ReadPageHeader';
import { ReadPageContent } from './Components/ReadPage/ReadPageContent';
import { ReadPageSheets } from './Components/ReadPage/ReadPageSheets';

const ReadPage = () => {
  const logic = useReadPageLogic();

  const onSelectVerse = useCallback((verseNo: number) => {
    const targetSurahId = logic.currentSurahInfo?.id;
    if (!targetSurahId) return;

    // Mevcut surede ayet seçildi, sadece o ayete kaydır
    logic.scrollToVerse(verseNo);

    // Navigasyon menüsünü kapat
    logic.closeNavigationSheet();
  }, [
    logic.currentSurahInfo?.id,
    logic.scrollToVerse,
    logic.closeNavigationSheet,
  ]);

  // Yüklenme ve Hata Durumları
  if (logic.surahsError || logic.versesError) {
    const error = logic.surahsError || logic.versesError;
    const errorMessage = [logic.surahsError?.message, logic.versesError?.message].filter(Boolean).join(' \n ');
    let errorType: 'network' | 'content' | 'general' = 'general';
    if (error?.message?.includes('network') || error?.message?.includes('fetch')) {
      errorType = 'network';
    } else if (error?.message?.includes('not found') || error?.message?.includes('404')) {
      errorType = 'content';
    }
    return (
      <EnhancedErrorState
        error={errorMessage}
        errorType={errorType}
        onRetry={() => window.location.reload()}
      />
    );
  }
  
  const headerProps = ReadPageHeader({
    contentMode: logic.contentMode,
    isContentModeOpen: logic.isContentModeOpen,
    setIsContentModeOpen: logic.setIsContentModeOpen,
    changeContentMode: logic.changeContentMode,
    isTranslationSheetOpen: logic.isTranslationSheetOpen,
    setIsTranslationSheetOpen: logic.setIsTranslationSheetOpen,
    translatorSearch: logic.translatorSearch,
    setTranslatorSearch: logic.setTranslatorSearch,
    availableTranslators: logic.availableTranslators,
    selectedTranslators: logic.selectedTranslators,
    onTranslatorSelectionChange: logic.handleTranslatorSelectionChange,
    translationsLoading: logic.translationsLoading,
    currentSurahInfo: logic.currentSurahInfo || null,
    currentVerse: logic.currentVerse,
    openNavigationSheet: logic.openNavigationSheet,
    surahsLoading: logic.surahsLoading,
    isMobile: logic.isMobile,
    showButtonTexts: logic.showButtonTexts,
  });

  if (logic.surahsLoading || !logic.displayedSurahInfo) {
    return (
      <PageLayout {...headerProps}>
        <div className="flex justify-center items-center h-64">
          <p>Yükleniyor...</p>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout {...headerProps}>
      <ReadPageSheets
        // Navigation Sheet - Proplar store'a taşındığı için kaldırıldı.
        surahSearch={logic.surahSearch}
        setSurahSearch={logic.setSurahSearch}
        verseSearch={logic.verseSearch}
        setVerseSearch={logic.setVerseSearch}
        displayedSurahInfo={logic.displayedSurahInfo}
        currentVerse={logic.currentVerse}
        navFilteredSurahs={logic.navFilteredSurahs || []}
        activeVerses={logic.activeVerses}
        onSelectVerse={onSelectVerse}
        handleNavigation={logic.handleNavigation}
        
        // Action Sheet Data
        verses={logic.displayVerses || []}
        availableTranslators={logic.availableTranslators}
        selectedTranslators={logic.selectedTranslators}
      />

      <ReadPageContent
        displayedSurahInfo={logic.displayedSurahInfo}
        displayVerses={logic.displayVerses || []}
        surahs={logic.surahs}
        contentMode={logic.contentMode}
        selectedTranslators={logic.selectedTranslators}
        availableTranslators={logic.availableTranslators}
        translationsLoading={logic.translationsLoading}
        translatorTitleColor={logic.translatorTitleColor}
        footnoteColor={logic.footnoteColor}
        sectionLeftBorderColor={logic.sectionLeftBorderColor}
        verseBorderColor={logic.verseBorderColor}
        onVerseMenuClick={logic.handleVerseMenuClick}
        handleNavigation={logic.handleNavigation}
      />
    </PageLayout>
  );
};

export default ReadPage;