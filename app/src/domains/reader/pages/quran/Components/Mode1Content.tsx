import React from 'react';
import { CombinedVerseData } from '@reader/models/types';
import { toArabicNumeral } from '@shared/utils/formatters';
import { MoreVertical } from 'lucide-react';

interface Mode1ContentProps {
  verse: Pick<CombinedVerseData, 'arabic_text' | 'verse_no' | 'sajdah'>;
  verseNumberBg: string;
  onVerseMenuClick?: (verseKey: string, event: React.MouseEvent) => void;
}

export const Mode1Content: React.FC<Mode1ContentProps> = ({
  verse,
  verseNumberBg,
  onVerseMenuClick,
}) => {
  const arabicText = verse.arabic_text || '';

  const handleVerseClick = (event: React.MouseEvent) => {
    if (onVerseMenuClick) {
      event.stopPropagation();
      onVerseMenuClick(verse.verse_no.toString(), event);
    }
  };

  return (
    <span
      onClick={handleVerseClick}
      className="text-[1.75rem] font-arabic whitespace-pre-wrap cursor-pointer"
      style={{
        color: 'var(--text-color)',
        display: 'inline',
        textAlign: 'justify',
      }}
    >
      {arabicText}
      {verse.sajdah && (
        <span className="text-amber-500 mx-1" title="Secde Ayeti">
          ۩
        </span>
      )}
      <span
        className="inline-flex items-center justify-center relative w-10 rounded-[0.625rem] text-base font-arabic"
        style={{
          color: 'var(--text-color)',
          backgroundColor: verseNumberBg,
          margin: '0 0.5rem 0 0.25rem',
          height: '2.1rem',
        }}
      >
        <span
          style={{
            transform: 'scale(1.3)',
            position: 'relative',
            top: '-0.05rem',
            left: '0.0rem',
          }}
        >
          {toArabicNumeral(verse.verse_no)}
        </span>
      </span>
    </span>
  );
};
