import React, { useRef, useEffect, useMemo, useState } from 'react';
import { Languages, ChevronDown, Search, Check, X } from 'lucide-react';
import { FixedSizeList as List } from 'react-window';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

// --- Type Definitions ---
interface ISelectableItem {
    id: string;
    name: string;
}

interface TranslationSelectorSheetProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  searchQuery: string;
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
  availableItems: ISelectableItem[] | undefined;
  selectedItems: string[];
  onSelectionChange: (newSelection: string[]) => void;
  triggerButtonLabel?: string;
  sheetTitle?: string;
}

// --- Constants ---
const DEBOUNCE_DELAY = 250;
const ROW_HEIGHT = 44;
const MAX_LIST_HEIGHT = 352; // 8 items * 44px

// --- Main Component ---
export const TranslationSelectorSheet: React.FC<TranslationSelectorSheetProps> = ({
  isOpen,
  setIsOpen,
  searchQuery,
  setSearchQuery,
  availableItems,
  selectedItems,
  onSelectionChange,
  triggerButtonLabel = "Mealler",
  sheetTitle = "Gösterilecek Mealler"
}) => {
  const sheetRef = useRef<HTMLDivElement>(null);
  const [debouncedSearch, setDebouncedSearch] = useState(searchQuery);

  // --- Hooks ---

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => setDebouncedSearch(searchQuery), DEBOUNCE_DELAY);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Handle clicks outside the component to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sheetRef.current && !sheetRef.current.contains(event.target as Node)) {
        const triggerButton = (event.target as Element)?.closest('[data-trigger-button="translation-selector-sheet"]');
        if (!triggerButton) {
          setIsOpen(false);
        }
      }
    };
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, setIsOpen]);

  // Memoize filtered items for performance
  const filteredItems = useMemo(() => {
    if (!availableItems) return [];
    return filterByNormalizedQuery(availableItems, debouncedSearch, (t) => [t.name, t.id]);
  }, [availableItems, debouncedSearch]);

  // --- Theming and Dynamic Colors (Inspired by NavigationSheet) ---
  const popoverBg = useAutoOverlay(8, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');

  // --- Event Handlers ---
  const handleSelectionToggle = (itemId: string) => {
    const newSelection = selectedItems.includes(itemId)
      ? selectedItems.filter(id => id !== itemId)
      : [...selectedItems, itemId];
    onSelectionChange(newSelection);
  };

  // --- Sub-Components / Render Functions ---

  const ItemRow: React.FC<{ index: number; style: React.CSSProperties }> = ({ index, style }) => {
    const item = filteredItems[index];
    const isSelected = selectedItems.includes(item.id);

    return (
      <button
        style={style}
        className="w-full text-left text-sm flex items-center justify-start relative"
        onClick={() => handleSelectionToggle(item.id)}
      >
        <div
          className={`w-full flex items-center justify-between rounded-lg px-3 transition-colors duration-200 text-[var(--text-color)] ${
            isSelected
              ? 'bg-[var(--bg-color)] shadow py-1.5'
              : 'py-2 hover:bg-[var(--text-color)]/5'
          }`}
        >
          <div className="flex items-center gap-3">
            <div
              className={`w-5 h-5 rounded border flex items-center justify-center transition-colors duration-150 ${
                isSelected
                  ? 'bg-[var(--text-color)] border-[var(--text-color)]'
                  : 'border-[var(--text-color)]/80'
              }`}
            >
              {isSelected && (
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" style={{ color: 'var(--bg-color)' }}>
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              )}
            </div>
            <span className={`truncate ${isSelected ? 'font-bold' : 'font-normal'}`}>
              {item.name}
            </span>
          </div>
        </div>
      </button>
    );
  };

  const renderSheet = () => {
    if (!isOpen) return null;

    const listHeight = Math.min(filteredItems.length * ROW_HEIGHT, MAX_LIST_HEIGHT);

    return (
      <>
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black/10 backdrop-blur-[0.5px] z-40 transition-opacity"
          onClick={() => setIsOpen(false)}
        />

        {/* Sheet Container - Responsive: Sheet on mobile, Popover on desktop */}
        <div
          ref={sheetRef}
          className="fixed bottom-0 left-0 right-0 max-h-[80vh] rounded-t-2xl shadow-2xl z-50 flex flex-col overflow-hidden border-t md:absolute md:top-full md:right-0 md:bottom-auto md:left-auto md:mt-2 md:w-72 md:max-h-none md:rounded-xl md:border"
          style={{
            backgroundColor: popoverBg,
            borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
          }}
        >
          {/* Mobile Pull Indicator */}
          <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15 flex-shrink-0" />
          
          {/* Header */}
          <div className="mx-4 mt-2 md:mt-4 mb-1 flex items-center justify-between">
            <h3 className="text-base font-medium text-[var(--text-color)]">
              {sheetTitle}
            </h3>
            <button
              onClick={() => setIsOpen(false)}
              className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
              aria-label="Kapat"
            >
              <X size={18} />
            </button>
          </div>

          {/* Content Area */}
          <div className="px-4 pt-2 pb-3 overflow-y-auto">
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Ara..."
                  className="w-full px-4 py-2 pl-9 rounded-lg text-sm text-[var(--text-color)]/90 placeholder:text-[var(--text-color)]/30 focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors duration-200"
                  style={{ backgroundColor: listAreaBgColor }}
                />
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                />
              </div>
              <div
                className="space-y-1 p-2 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                {filteredItems.length > 0 ? (
                  <List
                    height={listHeight}
                    itemCount={filteredItems.length}
                    itemSize={ROW_HEIGHT}
                    width="100%"
                  >
                    {ItemRow}
                  </List>
                ) : (
                  <div className="h-24 flex items-center justify-center text-sm text-[var(--text-color)] opacity-60">
                    Sonuç bulunamadı
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(prev => !prev)}
        className="px-2 py-1.5 rounded-lg flex items-center gap-1 text-[var(--text-color)] hover:bg-[var(--text-color)]/5 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label={sheetTitle}
        disabled={!availableItems || availableItems.length === 0}
        data-trigger-button="translation-selector-sheet"
      >
        <Languages size={18} />
        {triggerButtonLabel && <span className="text-sm font-medium">{triggerButtonLabel}</span>}
        {triggerButtonLabel && <ChevronDown size={16} className={`ml-1 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />}
      </button>
      {renderSheet()}
    </div>
  );
};

export default TranslationSelectorSheet;
