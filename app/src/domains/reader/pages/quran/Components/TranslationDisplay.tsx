import React, { useCallback } from 'react';
import { CombinedVerseData, TranslationData } from '@reader/models/types';
import { ContentMode } from '@reader/hooks/quran/useContentMode'; // Might need ContentMode if logic depends on it, or use a simpler prop
import { ChevronDown, ChevronRight } from 'lucide-react';
import { useReaderStore } from '@reader/store/readerstore'; // Import the store

// Define ITranslator locally if not imported from a shared types location
interface ITranslator {
    id: string;
    name: string;
}

interface TranslationDisplayProps {
  verse: CombinedVerseData;
  selectedTranslators: string[]; // Array of translator IDs
  availableTranslators: ITranslator[]; // Add this prop to get names
  sectionLeftBorderColor: string;
  translatorTitleColor: string;
  footnoteColor: string;
  displayMode: ContentMode; // Use ContentMode ('2', '3', '4') to control layout
}

export const TranslationDisplay: React.FC<TranslationDisplayProps> = ({
  verse,
  selectedTranslators,
  availableTranslators, // Destructure the new prop
  sectionLeftBorderColor,
  translatorTitleColor,
  footnoteColor,
  displayMode,
}) => {
  // Get state and action from the store
  const expandedFootnotes = useReaderStore((state) => state.expandedFootnotes);
  const toggleFootnote = useReaderStore((state) => state.toggleFootnote); // Use store action

  // Memoized function to check if footnotes are expanded
  const isFootnoteExpanded = useCallback((verseNo: number, translator: string) => {
    const key = `${verseNo}-${translator}`;
    return expandedFootnotes[key] || false;
  }, [expandedFootnotes]);

  // Helper function to get display name from available translators list
  const getDisplayName = useCallback((translatorId: string): string => {
    const translator = availableTranslators?.find(t => t.id === translatorId);
    return translator ? translator.name : translatorId; // Fallback to ID if not found
  }, [availableTranslators]);

  // Check if translations exist for the verse
  const verseTranslations = verse.translations;
  if (!verseTranslations || selectedTranslators.length === 0) {
    return null; // No translations available or selected
  }

  // Conditional container styling based on displayMode
  const containerClasses = `space-y-2 text-left ${
    displayMode === '3' ? 'pt-4 mt-4' : // Removed border-t
    displayMode === '4' ? 'pt-2' : 
    'pt-2' // Default for Mode 2
  }`;
  const containerStyle = {}; // Removed conditional borderColor for Mode 3

  return (
    <div className={containerClasses} style={containerStyle}>
      {selectedTranslators.map(translatorId => { // Renamed translator to translatorId
        const translation: TranslationData | undefined = verseTranslations?.[translatorId];
        if (!translation) return null;

        // Get the display name using the helper function
        const translatorDisplayName = getDisplayName(translatorId);

        // Get text from paragraphs - handle if it's empty or doesn't exist
        // Assuming paragraphs is string[] based on our types
        const translationText = translation.paragraphs?.join('\n\n') || ''; // Join paragraphs

        // Check if there are any footnotes to display - explicit undefined check
        const footnotes = translation.footnotes || [];
        const hasFootnotes = footnotes.length > 0;
        const footnotesExpanded = isFootnoteExpanded(verse.verse_no, translatorId);

        return (
          <div 
            key={translatorId} 
            className="text-sm py-2" 
          >
            {/* Inner div for border and padding */}
            <div 
              className="border-l-2 pl-3" 
              style={{ borderColor: sectionLeftBorderColor }}
            >
              <div className="text-xs mb-1" style={{ color: translatorTitleColor }}>
                <span className="font-bold">{translatorDisplayName}</span>:
              </div>
              
              {/* Mode 4: Prepend verse number */}
              {displayMode === '4' && (
                <>
                  <span className="font-semibold mr-1.5">{verse.verse_no}.</span>
                  <div 
                    className="inline" 
                    style={{ color: 'var(--text-color)', direction: 'ltr', unicodeBidi: 'embed' }} 
                    dangerouslySetInnerHTML={{ __html: translationText }}
                  />
                </>
              )}

              {/* Mode 2 & 3: Just display text */}
              {(displayMode === '2' || displayMode === '3') && (
                 <div 
                    style={{ color: 'var(--text-color)', direction: 'ltr', unicodeBidi: 'embed' }} 
                    dangerouslySetInnerHTML={{ __html: translationText }}
                 />
              )}

              {/* Dipnotlar Toggle (Common for all modes) */}
              {hasFootnotes && (
                <>
                  <button
                    onClick={() => {
                      const key = `${verse.verse_no}-${translatorId}`;
                      toggleFootnote(key);
                    }}
                    className="flex items-center text-left mt-2 py-1 text-xs font-medium"
                    style={{ color: footnoteColor }}
                  >
                    <span>Dipnotlar</span>
                    {footnotesExpanded
                      ? <ChevronDown size={14} className="ml-1 align-middle" />
                      : <ChevronRight size={14} className="ml-1 align-middle" />
                    }
                  </button>
                  
                  {footnotesExpanded && (
                    <div 
                      className="mt-1 text-xs"
                      style={{ color: footnoteColor }}
                    >
                      {footnotes.map((note: string, index: number) => (
                        <p key={index} className="mb-1" dangerouslySetInnerHTML={{ __html: note }} />
                      ))}
                    </div>
                  )}
                </>
              )}
            </div> {/* Bordered div closes AFTER footnotes */}
          </div>
        );
      })}
    </div>
  );
}; 