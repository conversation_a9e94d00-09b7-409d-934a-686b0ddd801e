import React, { useRef, useEffect } from 'react';
import { ChevronDown, LayoutGrid, X, Check } from 'lucide-react';
import { ContentMode } from '@reader/hooks/quran/useContentMode';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface ContentModeSelectorProps {
  contentMode: ContentMode;
  onChange: (value: ContentMode) => void;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  showText?: boolean;
}

const modes: { id: ContentMode; title: string; description: string }[] = [
  { id: '1', title: "Sadece Kur'an", description: "Orijinal Arapça metin." },
  { id: '2', title: "Kur'an ve Meal", description: "Arapça metin ve altında çevirisi." },
  { id: '3', title: "Kelime Meali", description: "<PERSON>p<PERSON> kelimeler ve Türkçe anlamları." },
  { id: '4', title: "<PERSON><PERSON><PERSON>", description: "Sadece çeviri metni." },
];

export const ContentModeSelector: React.FC<ContentModeSelectorProps> = ({
  contentMode,
  onChange,
  isOpen,
  setIsOpen,
  showText = true,
}) => {
  const sheetRef = useRef<HTMLDivElement>(null);
  const popoverBg = useAutoOverlay(8, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sheetRef.current && !sheetRef.current.contains(event.target as Node)) {
        const triggerButton = (event.target as Element)?.closest('[data-trigger-button="content-mode-selector"]');
        if (!triggerButton) {
          setIsOpen(false);
        }
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, setIsOpen]);

  const handleSelectMode = (modeId: ContentMode) => {
    onChange(modeId);
    setIsOpen(false);
  };

  const currentMode = modes.find(m => m.id === contentMode) || modes[0];

  const renderSheet = () => {
    if (!isOpen) return null;

    return (
      <>
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black/10 backdrop-blur-[0.5px] z-40 transition-opacity"
          onClick={() => setIsOpen(false)}
        />

        {/* Sheet/Popover Container */}
        <div
          ref={sheetRef}
          className="fixed bottom-0 left-0 right-0 max-h-[80vh] rounded-t-2xl shadow-2xl z-50 flex flex-col overflow-hidden border-t md:absolute md:top-full md:right-0 md:bottom-auto md:left-auto md:mt-2 md:w-72 md:max-h-none md:rounded-xl md:border"
          style={{
            backgroundColor: popoverBg,
            borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)',
          }}
        >
          {/* Mobile Pull Indicator */}
          <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15 flex-shrink-0" />
          
          {/* Header */}
          <div className="mx-4 mt-2 md:mt-4 mb-1 flex items-center justify-between">
            <h3 className="text-base font-medium text-[var(--text-color)]">Okuma Modu</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
              aria-label="Kapat"
            >
              <X size={18} />
            </button>
          </div>

          {/* Content Area */}
          <div className="px-4 pt-2 pb-3 overflow-y-auto">
            <div
              className="space-y-1 p-2 rounded-xl"
              style={{ backgroundColor: listAreaBgColor }}
            >
              {modes.map((mode) => {
                const isSelected = contentMode === mode.id;
                return (
                  <button
                    key={mode.id}
                    onClick={() => handleSelectMode(mode.id)}
                    className={`w-full text-left rounded-lg text-sm flex items-center justify-between relative transition-colors duration-200 text-[var(--text-color)] ${
                      isSelected ? 'bg-[var(--bg-color)] shadow' : 'hover:bg-[var(--text-color)]/5'
                    }`}
                  >
                    <div className="px-3 py-2.5">
                      <div className={`${isSelected ? 'font-bold' : 'font-medium'}`}>{mode.title}</div>
                      <div className="text-xs opacity-70 mt-0.5">{mode.description}</div>
                    </div>
                    {isSelected && (
                      <div className="pr-4">
                        <Check size={16} className="text-[var(--color-primary)]" />
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(prev => !prev)}
        data-trigger-button="content-mode-selector"
        className="px-2 py-1.5 rounded-lg flex items-center gap-1 text-[var(--text-color)] hover:bg-[var(--text-color)]/5 transition-colors"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label="Görünüm seçeneklerini değiştir"
      >
        <LayoutGrid size={18} />
        {showText && <span className="text-sm font-medium">{currentMode.title}</span>}
        {showText && <ChevronDown size={16} className={`ml-1 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />}
      </button>
      {renderSheet()}
    </div>
  );
};

export default ContentModeSelector;
