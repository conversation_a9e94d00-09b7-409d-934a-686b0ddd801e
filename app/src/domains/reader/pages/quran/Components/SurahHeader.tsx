import React from 'react';
import { Surah } from '@reader/models/types';

interface SurahHeaderProps {
  currentSurahInfo: Surah;
}

export const SurahHeader: React.FC<SurahHeaderProps> = ({ currentSurahInfo }) => {
  if (!currentSurahInfo) {
    return null;
  }

  return (
    <>
      {/* Surah Title - Updated Layout */}
      <div className="flex items-center justify-center mb-8 text-center">
        {/* Container for Side-by-Side Names */}
        <div className="flex items-center justify-center space-x-3">
          {/* Turkish Name */}
          <h1
            className="text-[1.8rem] font-bold leading-tight m-0" // Adjusted size and margin
            style={{ color: 'var(--text-color)' }}
          >
            {currentSurahInfo.name} Sûresi
          </h1>
          {/* Separator */}
          <span className="text-[1.8rem] font-light opacity-60" style={{ color: 'var(--text-color)' }}>|</span>
          {/* Arabic Name */}
          <p
            className="text-[1.5rem] m-0 font-arabic" // Adjusted size and margin, added font-arabic
            style={{ color: 'var(--text-color)' }}
          >
            {currentSurahInfo.arabic_name}
          </p>
        </div>
      </div>

      {/* Besmele - 1. sure hariç */}
      {currentSurahInfo.id !== 1 && (
        <div className="text-center mt-2 pt-4 border-t"
          style={{
            borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)',
            borderTopWidth: '1px',
            marginBottom: '0'
          }}
        >
          <p className="text-[1.75rem] font-arabic m-0" style={{
            color: 'var(--text-color)'
          }}>
            بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ
          </p>
        </div>
      )}
    </>
  );
};

export default SurahHeader; 