import React from 'react';
import { ChevronDown } from 'lucide-react';
import { Surah } from '@reader/models/types';

interface BreadcrumbNavigationProps {
  currentSurahInfo: Surah | null;
  currentVerse: number;
  openNavigationSheet: (type: 'surah' | 'verse') => void;
  surahsLoading: boolean;
}

export const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  currentSurahInfo,
  currentVerse,
  openNavigationSheet,
  surahsLoading
}) => {
  return (
    <div className="w-full overflow-x-auto whitespace-nowrap text-center py-1">
      <div className="inline-flex items-center justify-center min-w-max">
        <button
          onClick={() => openNavigationSheet('surah')}
          className="flex items-center gap-0.5 px-[0.6rem] py-[0.5rem] rounded-lg hover:bg-[var(--text-color)]/5 transition-colors"
          style={{ color: 'var(--text-color)' }}
          disabled={surahsLoading}
          title={currentSurahInfo?.name ? `${currentSurahInfo.id}. ${currentSurahInfo.name} Sûresi` : 'Sure Seçin'}
        >
          <span className="text-sm font-medium">
            {currentSurahInfo?.id}. {currentSurahInfo?.name || 'Sure'} Sûresi
          </span>
          <ChevronDown size={14} className="opacity-80 flex-shrink-0" />
        </button>

        <span className="mx-[0.125rem] text-sm opacity-50" style={{ color: 'var(--text-color)' }}>/</span>

        {/* --- Ayet Butonu --- */}
        <button
          onClick={() => openNavigationSheet('verse')}
          className="flex items-center gap-0.5 px-[0.6rem] py-[0.5rem] rounded-lg hover:bg-[var(--text-color)]/5 transition-colors"
          style={{ color: 'var(--text-color)' }}
          disabled={surahsLoading}
          aria-label="Ayet seç"
          title={`${currentVerse} / ${currentSurahInfo?.verse_count || '-'} Ayet`}
        >
          <span className="text-sm font-medium">
            {currentVerse} / {currentSurahInfo?.verse_count || '-'} Ayet
          </span>
          <ChevronDown size={14} className="ml-1 opacity-80 flex-shrink-0" />
        </button>
      </div>
    </div>
  );
};

export default BreadcrumbNavigation; 