import React from 'react';
import { NavigationFooter } from '@shared/components';
import { SurahHeader } from '../SurahHeader';
import { VerseList } from '../VerseList';
import { Surah, CombinedVerseData, ITranslator } from '@reader/models/types';
import { ContentMode } from '@reader/hooks/quran/useContentMode';

interface ReadPageContentProps {
  // Data
  displayedSurahInfo: Surah | null;
  displayVerses: CombinedVerseData[];
  surahs: Surah[] | null;
  
  // UI State
  contentMode: ContentMode;
  selectedTranslators: string[];
  availableTranslators: ITranslator[];
  translationsLoading: boolean;
  
  // Colors
  translatorTitleColor: string;
  footnoteColor: string;
  sectionLeftBorderColor: string;
  verseBorderColor: string;
  
  // Handlers
  onVerseMenuClick: (verseKey: string, event: React.MouseEvent) => void;
  handleNavigation: (surahId: number) => void;
}

export const ReadPageContent: React.FC<ReadPageContentProps> = ({
  displayedSurahInfo,
  displayVerses,
  surahs,
  contentMode,
  selectedTranslators,
  availableTranslators,
  translationsLoading,
  translatorTitleColor,
  footnoteColor,
  sectionLeftBorderColor,
  verseBorderColor,
  onVerseMenuClick,
  handleNavigation,
}) => {
  return (
    <div className="pb-20">
      <main className="max-w-3xl mx-auto px-5 py-10 space-y-10">
        {displayedSurahInfo && <SurahHeader currentSurahInfo={displayedSurahInfo} />}

        <VerseList
          verses={displayVerses}
          viewMode={contentMode}
          selectedTranslators={selectedTranslators}
          availableTranslators={availableTranslators}
          translatorTitleColor={translatorTitleColor}
          footnoteColor={footnoteColor}
          sectionLeftBorderColor={sectionLeftBorderColor}
          verseBorderColor={verseBorderColor}
          onVerseMenuClick={onVerseMenuClick}
          surahName={displayedSurahInfo?.name}
        />

        <NavigationFooter
          prevSection={displayedSurahInfo && displayedSurahInfo.id > 1 ? {
            id: String(displayedSurahInfo.id - 1),
            title: surahs?.find((s: Surah) => s.id === displayedSurahInfo.id - 1)?.name || '',
            onClick: () => handleNavigation(displayedSurahInfo.id - 1)
          } : undefined}
          nextSection={displayedSurahInfo && displayedSurahInfo.id < 114 ? {
            id: String(displayedSurahInfo.id + 1),
            title: surahs?.find((s: Surah) => s.id === displayedSurahInfo.id + 1)?.name || '',
            onClick: () => handleNavigation(displayedSurahInfo.id + 1)
          } : undefined}
        />

        {translationsLoading &&
          <div className="text-center">
            <p>Çeviriler yükleniyor...</p>
          </div>
        }
      </main>
    </div>
  );
};
