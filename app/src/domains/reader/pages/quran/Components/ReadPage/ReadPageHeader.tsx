import React from 'react';
import { ContentModeSelector } from '../ContentModeSelector';
import { TranslationSelectorSheet } from '../TranslationSelectorSheet';
import { BreadcrumbNavigation } from '../BreadcrumbNavigation';
import { ContentMode } from '@reader/hooks/quran/useContentMode';
import { Surah, ITranslator } from '@reader/models/types';

interface ReadPageHeaderProps {
  // Content mode
  contentMode: ContentMode;
  isContentModeOpen: boolean;
  setIsContentModeOpen: React.Dispatch<React.SetStateAction<boolean>>;
  changeContentMode: (mode: ContentMode) => void;
  
  // Translation
  isTranslationSheetOpen: boolean;
  setIsTranslationSheetOpen: React.Dispatch<React.SetStateAction<boolean>>;
  translatorSearch: string;
  setTranslatorSearch: React.Dispatch<React.SetStateAction<string>>;
  availableTranslators: ITranslator[];
  selectedTranslators: string[];
  onTranslatorSelectionChange: (selection: string[]) => void;
  translationsLoading: boolean;
  
  // Navigation
  currentSurahInfo: Surah | null;
  currentVerse: number;
  openNavigationSheet: (type: 'surah' | 'verse') => void;
  surahsLoading: boolean;
  
  // UI
  isMobile: boolean;
  showButtonTexts: boolean;
}

export const ReadPageHeader = ({
  contentMode,
  isContentModeOpen,
  setIsContentModeOpen,
  changeContentMode,
  isTranslationSheetOpen,
  setIsTranslationSheetOpen,
  translatorSearch,
  setTranslatorSearch,
  availableTranslators,
  selectedTranslators,
  onTranslatorSelectionChange,
  translationsLoading,
  currentSurahInfo,
  currentVerse,
  openNavigationSheet,
  surahsLoading,
  isMobile,
  showButtonTexts,
}: ReadPageHeaderProps) => {
  const breadcrumbContent = (
    <BreadcrumbNavigation
      currentSurahInfo={currentSurahInfo}
      currentVerse={currentVerse}
      openNavigationSheet={openNavigationSheet}
      surahsLoading={surahsLoading}
    />
  );

  const navbarActions = (
    <div className="flex items-center gap-0.5">
      {(contentMode === '2' || contentMode === '3' || contentMode === '4') && (
        <div className="relative">
          <TranslationSelectorSheet
            isOpen={isTranslationSheetOpen}
            setIsOpen={setIsTranslationSheetOpen}
            searchQuery={translatorSearch}
            setSearchQuery={setTranslatorSearch}
            availableItems={availableTranslators}
            selectedItems={selectedTranslators}
            onSelectionChange={onTranslatorSelectionChange}
            triggerButtonLabel={showButtonTexts ? "Mealler" : ""}
            sheetTitle="Gösterilecek Mealler"
          />
          {translationsLoading && (
            <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 flex items-center justify-center">
              <div className="text-xs bg-[var(--bg-color)] px-2 py-1 rounded-full shadow-md" style={{ color: 'var(--text-color)' }}>
                <span className="inline-block animate-pulse">•</span>
                <span className="ml-1">Mealler yükleniyor</span>
              </div>
            </div>
          )}
        </div>
      )}
      <ContentModeSelector
        contentMode={contentMode}
        isOpen={isContentModeOpen}
        setIsOpen={setIsContentModeOpen}
        onChange={changeContentMode}
        showText={showButtonTexts}
      />
    </div>
  );

  return {
    breadcrumbContent,
    navbarActions,
    navbarCenterContent: isMobile ? null : breadcrumbContent,
    secondRowContent: isMobile ? breadcrumbContent : undefined,
    navbarTwoRows: isMobile,
    showBackButton: true,
    showLogo: true,
    showThemeToggle: !isMobile,
    showLoginButton: !isMobile,
    showMoreOptions: true,
  };
};
