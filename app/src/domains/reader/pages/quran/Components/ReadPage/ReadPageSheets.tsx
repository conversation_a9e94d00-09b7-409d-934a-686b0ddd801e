import React, { useState, useEffect } from 'react';
import { NavigationSheet } from '../NavigationSheet';
import TafsirNavigationSheet from '../../../tafsir/Components/NavigationSheet';
import { UnifiedActionMenu } from '../UnifiedActionMenu';
import { WordDetailSheet } from '../WordDetailSheet';
import { AnnotationSheet } from '@domains/reader-interactions/annotations/components/AnnotationSheet';
import { AnnotationEditModal } from '@domains/reader-interactions/annotations/components/AnnotationEditModal';
import { NotesListSheet } from '@domains/reader-interactions/notes/components/NotesListSheet';
import { BookmarkBottomSheet } from '@domains/reader-interactions/bookmarks/components/BookmarkBottomSheet';
import { AnnotationToast } from '@domains/reader-interactions/shared/components/AnnotationToast';
import { useReaderActions } from '@domains/reader-interactions/hooks/useReaderActions';
import { Surah, CombinedVerseData, ITranslator, Book } from '@reader/models/types';
import { useReaderStore } from '@reader/store/readerstore';

interface ReadPageSheetsProps {
  // Navigation Sheet - State now comes from store
  surahSearch: string;
  setSurahSearch: (search: string) => void;
  verseSearch: string;
  setVerseSearch: (search: string) => void;
  displayedSurahInfo: Surah;
  currentVerse: number;
  navFilteredSurahs: Surah[];
  activeVerses: number[];
  onSelectVerse: (verseNo: number) => void;
  handleNavigation: (surahId: number, verseNo?: number) => void;
  
  // Action Sheet Data
  verses: CombinedVerseData[];
  availableTranslators: ITranslator[];
  selectedTranslators: string[];
}

export const ReadPageSheets: React.FC<ReadPageSheetsProps> = ({
  surahSearch,
  setSurahSearch,
  verseSearch,
  setVerseSearch,
  displayedSurahInfo,
  currentVerse,
  navFilteredSurahs,
  activeVerses,
  onSelectVerse,
  handleNavigation,
  verses,
  availableTranslators,
  selectedTranslators
}) => {
  const navigationType = useReaderStore(state => state.uiState.navigationType);
  const setNavigationType = useReaderStore(state => state.setNavigationType);
  const [navSheetSurah, setNavSheetSurah] = useState<Surah>(displayedSurahInfo);
  const [navSheetVerses, setNavSheetVerses] = useState<number[]>(activeVerses);

  // Tafsir NavigationSheet için state'ler
  const [isTafsirNavigationOpen, setIsTafsirNavigationOpen] = useState(false);
  const [tafsirBooks, setTafsirBooks] = useState<Book[]>([]);
  const [tafsirBookSearch, setTafsirBookSearch] = useState('');
  // Tafsir için verseKey'i sakla (tafsir sheet kapanana kadar)
  const [savedTafsirVerseKey, setSavedTafsirVerseKey] = useState<string | null>(null);

  // Sync the sheet's state with the main page's state when it changes
  useEffect(() => {
    setNavSheetSurah(displayedSurahInfo);
    setNavSheetVerses(activeVerses);
  }, [displayedSurahInfo, activeVerses]);

  // Tafsir kitaplarını yükle
  useEffect(() => {
    if (isTafsirNavigationOpen && tafsirBooks.length === 0) {
      loadTafsirBooks();
    }
  }, [isTafsirNavigationOpen, tafsirBooks.length]);

  const loadTafsirBooks = async () => {
    try {
      const response = await fetch('/data/library/books.json');
      const allBooks: Book[] = await response.json();
      const tafsirBooksFiltered = allBooks.filter(book => book.category_id === 4);
      setTafsirBooks(tafsirBooksFiltered);
    } catch (error) {
      console.error('Tafsir kitapları yüklenirken hata:', error);
    }
  };

  const handleSurahPreview = (surahId: number) => {
    const newSurah = navFilteredSurahs.find(s => s.id === surahId);
    if (newSurah) {
      setNavSheetSurah(newSurah);
      const newVerses = Array.from({ length: newSurah.verse_count }, (_, i) => i + 1);
      setNavSheetVerses(newVerses);
    }
  };

  // 🔄 Store selectors - separate for better performance
  const isNavigationSheetOpen = useReaderStore(state => state.uiState.isNavigationSheetOpen);
  const isActionSheetOpen = useReaderStore(state => state.uiState.isActionSheetOpen);
  const isWordDetailSheetOpen = useReaderStore(state => state.uiState.isWordDetailSheetOpen);
  const actionSheetVerseKey = useReaderStore(state => state.uiState.actionSheetVerseKey);
  const actionSheetAnchorEl = useReaderStore(state => state.uiState.actionSheetAnchorEl);
  const wordDetailSheetVerseKey = useReaderStore(state => state.uiState.wordDetailSheetVerseKey);
  const closeNavigationSheet = useReaderStore(state => state.closeNavigationSheet);
  const closeVerseActionSheet = useReaderStore(state => state.closeVerseActionSheet);
  const closeWordDetailSheet = useReaderStore(state => state.closeWordDetailSheet);

  const getVerseData = (verseKey: string | null): CombinedVerseData | undefined => {
    if (!verseKey || !verses) return undefined;
    const verseNo = parseInt(verseKey.split('-')[1], 10);
    return verses.find((v: CombinedVerseData) => v.verse_no === verseNo);
  };

  const actionSheetVerseData = getVerseData(actionSheetVerseKey);


  // 🎯 Use our beautiful custom hook for all reader actions
  const {
    sheetStates,
    toastState,
    collections,
    actionContext,
    closeSheet,
    hideToast,
    handleNoteAction,
    handleNotesListAction,
    handleBookmarkAction,
    handleNoteSave,
    handleBookmarkSave,
    handleCreateCollection,
    handleNoteEditAction,
    onNoteUpdated,
    closeNoteEditModal
  } = useReaderActions(
    actionSheetVerseKey,
    actionSheetVerseData,
    displayedSurahInfo?.name,
    availableTranslators,
    selectedTranslators
  );

  const sheetTitleFromContext = (() => {
    const { verseKey, surahName } = actionContext;
    if (!verseKey) return 'Ayet';
    const [surahNum, verseNum] = verseKey.split('-');
    if (!verseNum) return 'Ayet bilgisi yükleniyor...';
    return `${surahName || `Sûre ${surahNum}`}, ${verseNum}. Ayet`;
  })();

  return (
    <>
      {/* 🧭 Navigation Sheet */}
      {isNavigationSheetOpen && (
        <NavigationSheet
          isOpen={isNavigationSheetOpen}
          onClose={() => {
            closeNavigationSheet();
            // Reset state when closing
            setTimeout(() => {
              setNavigationType('surah');
              setNavSheetSurah(displayedSurahInfo);
              setNavSheetVerses(activeVerses);
            }, 200);
          }}
          navigationType={navigationType}
          onChangeNavigationType={setNavigationType}
          surahSearch={surahSearch}
          onChangeSurahSearch={setSurahSearch}
          verseSearch={verseSearch}
          onChangeVerseSearch={setVerseSearch}
          currentSurahId={navSheetSurah.id}
          currentVerse={displayedSurahInfo.id === navSheetSurah.id ? currentVerse : -1}
          filteredSurahs={navFilteredSurahs}
          filteredVerses={navSheetVerses}
          onSelectSurah={(surahId, navigateToPage = true) => {
            if (navigateToPage) {
              handleNavigation(surahId);
            } else {
              handleSurahPreview(surahId);
            }
          }}
          onSelectVerse={(verseNo) => {
            if (navSheetSurah.id !== displayedSurahInfo.id) {
              // Farklı bir suredeysek, o sureye git ve ayet numarasını state ile taşı.
              // handleNavigation fonksiyonunun bu state'i işlemesi beklenir.
              handleNavigation(navSheetSurah.id, verseNo);
            } else {
              // Aynı suredeysek, sadece o ayete kaydır.
              onSelectVerse(verseNo);
            }
            // İşlem sonrası menüyü kapat.
            closeNavigationSheet();
          }}
        />
      )}

      {/* 🎯 Action Menu - Clean and Beautiful */}
      {isActionSheetOpen && actionSheetVerseKey && (
        <UnifiedActionMenu
          isOpen={isActionSheetOpen}
          verseKey={actionSheetVerseKey}
          onClose={closeVerseActionSheet}
          surahName={displayedSurahInfo?.name}
          verseData={actionSheetVerseData}
          availableTranslators={availableTranslators}
          selectedTranslators={selectedTranslators}
          anchorEl={actionSheetAnchorEl}
          onOpenNoteSheet={(verseKey, verseData, surahName) => {
            handleNotesListAction(verseKey, verseData, surahName);
          }}
          onOpenBookmarkSheet={(verseKey, verseData, surahName) => {
            handleBookmarkAction(verseKey, verseData, surahName);
          }}
          onOpenTafsirSheet={() => {
            // Tafsir için NavigationSheet aç - direk kitap seçimine başla

            // VerseKey'i sakla (tafsir sheet kapanana kadar)
            setSavedTafsirVerseKey(actionSheetVerseKey);

            setIsTafsirNavigationOpen(true);
          }}
        />
      )}

      {/* 🧭 Tafsir Navigation Sheet */}
      {isTafsirNavigationOpen && (
        <TafsirNavigationSheet
          isOpen={isTafsirNavigationOpen}
          onClose={() => {
            setIsTafsirNavigationOpen(false);
            // Tafsir sheet kapanınca verseKey'i temizle
            setSavedTafsirVerseKey(null);
          }}
          showOnlyBooksTab={true}
          navigationType="books" // Sadece kitap seçimi
          onChangeNavigationType={() => {}} // Değiştirme yok
          books={tafsirBooks}
          currentBookId=""
          selectedBookId={null}
          bookSearch={tafsirBookSearch}
          onChangeBookSearch={setTafsirBookSearch}
          surahSearch=""
          onChangeSurahSearch={() => {}}
          verseSearch=""
          onChangeVerseSearch={() => {}}
          // Diğer gerekli props'lar
          currentSurahId=""
          filteredSurahs={[]} // Kullanılmayacak
          onSelectSurah={() => {}} // Kullanılmayacak
          onSelectVerse={() => {}} // Kullanılmayacak
          onSelectBook={(bookId: number) => {
            // Tafsir kitabını seç ve direk yönlendir

            if (savedTafsirVerseKey) {
              const [surahId, verseNumber] = savedTafsirVerseKey.split('-').map(Number);
              const tafsirUrl = `/tefsir/${bookId}/${surahId}/${verseNumber}`;

              window.location.href = tafsirUrl;
            } else {
              // Fallback: tafsir ana sayfasına yönlendir
              window.location.href = `/tefsir/${bookId}`;
            }
            setIsTafsirNavigationOpen(false);
          }}
          isLoadingSurahs={false}
          hasSingleSurah={false}
        />
      )}

      {/* 📝 Notes List Sheet - Mevcut notları göster */}
      {actionContext.verseKey && (
        <NotesListSheet
          isOpen={sheetStates.notesList.isOpen}
          onClose={() => closeSheet('notesList')}
          verseKey={actionContext.verseKey}
          surahName={actionContext.surahName}
          onEditNote={handleNoteEditAction} // Pass the handler down
          onAddNote={() => {
            // Notlar listesini kapat ve not ekleme sheet'ini aç
            closeSheet('notesList');
            if (actionContext.verseKey && actionContext.verseData) {
              handleNoteAction(actionContext.verseKey, actionContext.verseData, actionContext.surahName);
            }
          }}
        />
      )}

      {/* 📝 Note Sheet (Artık AnnotationSheet kullanıyor) */}
      <AnnotationSheet
        isOpen={sheetStates.note.isOpen}
        onClose={() => closeSheet('note')}
        selectedText={sheetTitleFromContext}
        annotationType="note"
        allowPublicToggle={true}
        onSubmit={handleNoteSave}
        isLoading={false} // Bu context'te loading state'i handleNoteSave içinde yönetiliyor
      />

      {/* 📝 Note Edit Modal - The new shiny modal */}
      {sheetStates.noteEdit.isOpen && sheetStates.noteEdit.annotation && (
        <AnnotationEditModal
          isOpen={sheetStates.noteEdit.isOpen}
          onClose={closeNoteEditModal}
          annotation={sheetStates.noteEdit.annotation}
          onUpdated={onNoteUpdated}
        />
      )}

      {/* 🔖 Bookmark Sheet - Enterprise Grade */}
      <BookmarkBottomSheet
        isOpen={sheetStates.bookmark.isOpen}
        onClose={() => closeSheet('bookmark')}
        onSelectCollection={handleBookmarkSave}
        onCreateCollection={handleCreateCollection}
        collections={collections}
        selectedText={sheetTitleFromContext}
        isQuranVerse={true}
        availableTranslators={availableTranslators}
        selectedTranslators={selectedTranslators}
      />

      {/* 🎨 Toast Notifications - Centralized */}
      <AnnotationToast
        isVisible={toastState.isVisible}
        message={toastState.message}
        type={toastState.type}
        onClose={hideToast}
      />

      {/* 📚 Word Detail Sheet */}
      {isWordDetailSheetOpen && wordDetailSheetVerseKey && (
        <WordDetailSheet
          isOpen={isWordDetailSheetOpen}
          onClose={closeWordDetailSheet}
          verseKey={wordDetailSheetVerseKey}
          sheetTitle={`${displayedSurahInfo?.name || 'Sure'}, ${wordDetailSheetVerseKey.split('-')[1]}. Ayet - Kelime Detayı`}
        />
      )}
    </>
  );
};
