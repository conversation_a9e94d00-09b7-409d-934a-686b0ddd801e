import React, { useState, useEffect } from 'react';
import { X, ChevronRight, Search } from 'lucide-react';
import { Surah } from '@reader/models/types';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

// Helper function to capitalize the first letter
const capitalizeFirstLetter = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

interface NavigationSheetProps {
  isOpen: boolean;
  onClose: () => void;
  navigationType: 'surah' | 'verse';
  onChangeNavigationType: (type: 'surah' | 'verse') => void;
  surahSearch: string;
  onChangeSurahSearch: (value: string) => void;
  verseSearch: string;
  onChangeVerseSearch: (value: string) => void;
  currentSurahId: number;
  currentVerse: number;
  filteredSurahs: Surah[];
  filteredVerses: number[];
  onSelectSurah: (surahId: number, navigateToPage?: boolean) => void;
  onSelectVerse: (verseNo: number) => void;
}

export const NavigationSheet: React.FC<NavigationSheetProps> = ({
  isOpen,
  onClose,
  navigationType,
  onChangeNavigationType,
  surahSearch,
  onChangeSurahSearch,
  verseSearch,
  onChangeVerseSearch,
  currentSurahId,
  currentVerse,
  filteredSurahs,
  filteredVerses,
  onSelectSurah,
  onSelectVerse
}) => {
  const navBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const toolbarBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const verseSquareBgColor = useAutoOverlay(4, 'var(--bg-color)');

  if (!isOpen) return null;
  
  // Sure seçip ayet görünümüne geçiş yapan ortak fonksiyon
  const selectSurahAndShowVerses = (surahId: number) => {
    // Önce seçilen sureyi güncelle - navigateToPage=false ile sayfa navigasyonunu devre dışı bırak
    onSelectSurah(surahId, false);
    
    // Sonra ayet görünümüne geç
    onChangeNavigationType('verse');
  };
  
  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        style={{ opacity: isOpen ? 1 : 0 }}
        onClick={onClose}
      />
      
      {/* Sheet - Increased rounding */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[390px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{ 
          backgroundColor: navBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />

        {/* Segment Control with Close Button - Increased rounding */}
        <div className="mx-4 mt-4 flex items-center justify-between">
          <div className="flex-1 flex p-1 rounded-lg" style={{ backgroundColor: toolbarBgColor }}>
            <button
              onClick={() => onChangeNavigationType('surah')}
              className={`flex-1 flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${navigationType === 'surah' ? 'shadow' : ''}`}
              style={{
                backgroundColor: navigationType === 'surah' ? 'var(--bg-color)' : 'transparent',
                color: 'var(--text-color)'
              }}
            >
              Sure
            </button>
            <button
              onClick={() => onChangeNavigationType('verse')}
              className={`flex-1 flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${navigationType === 'verse' ? 'shadow' : ''}`}
              style={{
                backgroundColor: navigationType === 'verse' ? 'var(--bg-color)' : 'transparent',
                color: 'var(--text-color)'
              }}
            >
              Ayet
            </button>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center ml-2 rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
          >
            <X size={18} />
          </button>
        </div>

        {/* Content */}
        <div className="px-4 pt-3 pb-1 overflow-y-auto max-h-[calc(80vh-4rem)] md:max-h-[calc(70vh-6rem)] mt-2">
          {navigationType === 'surah' && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={surahSearch}
                  onChange={(e) => onChangeSurahSearch(e.target.value)}
                  placeholder="Sure adı veya numarası..."
                  className={`w-full px-4 py-2.5 pl-9 rounded-lg text-sm text-[var(--text-color)]/90 placeholder:text-[var(--text-color)]/30 focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors duration-200`}
                  style={{ 
                    backgroundColor: listAreaBgColor
                  }}
                />
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                />
              </div>
              <div 
                className="space-y-1 p-2 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                {filteredSurahs.map((s) => {
                  const isCurrentSurah = s.id === currentSurahId;
                    
                  return (
                    <button
                      key={s.id}
                      onClick={() => {
                        // Sureye tıklandığında sayfa navigasyonu yapmak istiyoruz
                        onSelectSurah(s.id, true);
                      }}
                      className={`w-full px-3 py-2.5 text-left rounded-lg text-sm flex items-center justify-between relative transition-colors duration-200 text-[var(--text-color)] ${
                        isCurrentSurah ? 'bg-[var(--bg-color)] shadow' : 'hover:bg-[var(--text-color)]/5'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <span className="w-6 text-right opacity-80">{s.id}.</span>
                        <span 
                          className={`truncate ${isCurrentSurah ? 'font-bold' : 'font-medium'}`}
                        >
                          {s.name}
                        </span>
                        <span className="text-xs opacity-60">
                          {capitalizeFirstLetter(s.revelation_place)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs opacity-60">{s.verse_count} Ayet</span>
                        <button
                          type="button"
                          className="p-1.5 rounded-full hover:bg-[var(--text-color)]/10"
                          onClick={(e) => {
                            e.stopPropagation(); // Bu tıklamanın üst butona geçmesini engelle
                            selectSurahAndShowVerses(s.id);
                          }}
                          title={`${s.name} Suresi Ayetleri`}
                        >
                          <ChevronRight size={14} style={{ color: 'var(--text-color)' }} />
                        </button>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {navigationType === 'verse' && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={verseSearch}
                  onChange={(e) => onChangeVerseSearch(e.target.value)}
                  placeholder="Ayet numarası..."
                  className={`w-full px-4 py-2.5 pl-9 rounded-lg text-sm text-[var(--text-color)]/90 placeholder:text-[var(--text-color)]/30 focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors duration-200`}
                  style={{ 
                    backgroundColor: listAreaBgColor
                  }}
                />
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                />
              </div>
              
              {/* Seçili sure başlığı ekleniyor */}
              <div className="mb-2 py-2 text-sm font-medium">
                <span className="opacity-60">Sure: </span>
                <span className="font-bold">{filteredSurahs.find(s => s.id === currentSurahId)?.name || `Sure ${currentSurahId}`}</span>
              </div>
              
              <div className="grid grid-cols-5 gap-2 mt-3">
                {filteredVerses.map((verseNo) => {
                  const isCurrentVerse = verseNo === currentVerse;
                  
                  return (
                    <button
                      key={verseNo}
                      onClick={() => {
                        onSelectVerse(verseNo);
                        onClose();
                      }}
                      className={`aspect-square p-1 text-sm rounded-lg flex items-center justify-center transition-all duration-200 ${
                        isCurrentVerse 
                          ? 'bg-[var(--bg-color)] shadow font-bold' 
                          : 'hover:bg-[var(--text-color)]/5 font-medium'
                      }`}
                      style={{ 
                        backgroundColor: isCurrentVerse ? 'var(--bg-color)' : verseSquareBgColor
                      }}
                    >
                      <span>{verseNo}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default NavigationSheet;
