import React from 'react';
import { CombinedVerseData, ITranslator } from '@reader/models/types';
import { TranslationDisplay } from './TranslationDisplay';
import { MoreVertical } from 'lucide-react';

interface Mode4ContentProps {
  verse: CombinedVerseData;
  verseBorderColor: string;
  translatorTitleColor: string;
  sectionLeftBorderColor: string;
  footnoteColor: string;
  selectedTranslators: string[];
  availableTranslators: ITranslator[];
  onVerseMenuClick?: (verseKey: string, event: React.MouseEvent) => void;
  openMenuVerseKey?: string | null;
  onCloseVerseMenu?: () => void;
  surahName?: string;
}

export const Mode4Content: React.FC<Mode4ContentProps> = ({
  verse,
  verseBorderColor,
  translatorTitleColor,
  sectionLeftBorderColor,
  footnoteColor,
  selectedTranslators,
  availableTranslators,
  onVerseMenuClick,
  openMenuVerseKey: _openMenuVerseKey,
  onCloseVerseMenu: _onCloseVerseMenu,
  surahName: _surahName,
}) => {
  const verseKey = `${verse.verse_no}`;

  return (
    <div className="space-y-4 pb-5 mb-3 border-b px-2 pt-2 relative" style={{ borderColor: verseBorderColor }}>
      <div className="flex justify-end mb-4">
        {onVerseMenuClick && (
          <button
            data-verse-menu-button={verseKey}
            onClick={(e) => {
              e.stopPropagation();
              onVerseMenuClick(verseKey, e);
            }}
            className="p-1 rounded-full hover:bg-[var(--text-color)]/10 transition-colors duration-150"
            style={{ color: 'var(--text-color)' }}
          >
            <MoreVertical size={14} />
          </button>
        )}
      </div>

      <TranslationDisplay
        verse={verse}
        selectedTranslators={selectedTranslators}
        availableTranslators={availableTranslators}
        sectionLeftBorderColor={sectionLeftBorderColor}
        translatorTitleColor={translatorTitleColor}
        footnoteColor={footnoteColor}
        displayMode={'4'}
      />
    </div>
  );
};