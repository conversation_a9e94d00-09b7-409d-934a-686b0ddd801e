import React, { memo } from 'react';
import { CombinedVerseData, ITranslator } from '../../../models/types';
import { ContentMode } from '../../../hooks/quran/useContentMode';
import { VerseContent } from './VerseContent';

interface MemoizedVerseProps {
  verse: CombinedVerseData;
  viewMode: ContentMode;
  selectedTranslators: string[];
  availableTranslators: ITranslator[];
  verseBorderColor: string;
  translatorTitleColor: string;
  sectionLeftBorderColor: string;
  footnoteColor: string;
  surahName?: string;
  onVerseMenuClick?: (verseKey: string, event: React.MouseEvent) => void;
  openMenuVerseKey?: string | null;
  onCloseVerseMenu?: () => void;
}

const MemoizedVerseComponent = memo(({
  verse,
  viewMode,
  selectedTranslators,
  availableTranslators,
  verseBorderColor,
  translatorTitleColor,
  sectionLeftBorderColor,
  footnoteColor,
  surahName,
  onVerseMenuClick,
  openMenuVerseKey,
  onCloseVerseMenu,
}: MemoizedVerseProps) => {
  const evenVerseClass = viewMode === '2' && verse.verse_no % 2 === 0
    ? 'bg-[var(--text-color)]/[0.05] rounded-xl p-2'
    : '';

  return (
    <div 
      id={`verse-${verse.verse_no}`}
      className={`${evenVerseClass} ${viewMode !== '1' ? 'hover:bg-[var(--text-color)]/[0.02] block' : ''} transition-colors duration-200`}
    >
      <VerseContent
        verse={verse}
        viewMode={viewMode}
        selectedTranslators={selectedTranslators}
        availableTranslators={availableTranslators}
        verseBorderColor={verseBorderColor}
        translatorTitleColor={translatorTitleColor}
        sectionLeftBorderColor={sectionLeftBorderColor}
        footnoteColor={footnoteColor}
        onVerseMenuClick={onVerseMenuClick}
        openMenuVerseKey={openMenuVerseKey}
        onCloseVerseMenu={onCloseVerseMenu}
        surahName={surahName}
      />
    </div>
  );
});

interface VerseListProps {
  verses: CombinedVerseData[];
  viewMode: ContentMode;
  selectedTranslators: string[];
  availableTranslators: ITranslator[];
  verseBorderColor: string;
  translatorTitleColor: string;
  sectionLeftBorderColor: string;
  footnoteColor: string;
  surahName?: string;
  onVerseMenuClick?: (verseKey: string, event: React.MouseEvent) => void;
  openMenuVerseKey?: string | null;
  onCloseVerseMenu?: () => void;
}

export const VerseList: React.FC<VerseListProps> = ({
  verses,
  viewMode,
  selectedTranslators,
  availableTranslators,
  verseBorderColor,
  translatorTitleColor,
  sectionLeftBorderColor,
  footnoteColor,
  surahName,
  onVerseMenuClick,
  openMenuVerseKey,
  onCloseVerseMenu,
}) => {
  const containerStyle = viewMode === '1' 
    ? {
        dir: 'rtl' as const,
        className: 'text-right text-justify',
        style: {
          wordBreak: 'keep-all' as const, 
          display: 'block',
          maxWidth: '100%',
          padding: '0 0 12px 0',
          lineHeight: '2.1',
          textJustify: 'distribute' as any,
          textAlignLast: 'right' as any,
        }
      }
    : {
        className: 'space-y-0',
        style: {
          wordBreak: 'keep-all' as const, 
          display: 'block',
          maxWidth: '100%',
          padding: '0 0 12px 0',
          lineHeight: 'normal',
        }
      };

  return (
    <div 
      dir={containerStyle.dir} 
      className={containerStyle.className} 
      style={containerStyle.style}
    >
      {verses.map((verse: CombinedVerseData) => (
        viewMode === '1' ? (
          <span
            key={verse.verse_no}
            id={`verse-${verse.verse_no}`}
            className="inline px-1 py-1"
          >
            <VerseContent
              verse={verse}
              viewMode={viewMode}
              selectedTranslators={selectedTranslators}
              availableTranslators={availableTranslators}
              verseBorderColor={verseBorderColor}
              translatorTitleColor={translatorTitleColor}
              sectionLeftBorderColor={sectionLeftBorderColor}
              footnoteColor={footnoteColor}
              surahName={surahName}
              onVerseMenuClick={onVerseMenuClick}
            />
          </span>
        ) : (
          <MemoizedVerseComponent
            key={verse.verse_no}
            verse={verse}
            viewMode={viewMode}
            selectedTranslators={selectedTranslators}
            availableTranslators={availableTranslators}
            verseBorderColor={verseBorderColor}
            translatorTitleColor={translatorTitleColor}
            sectionLeftBorderColor={sectionLeftBorderColor}
            footnoteColor={footnoteColor}
            onVerseMenuClick={onVerseMenuClick}
            openMenuVerseKey={openMenuVerseKey}
            onCloseVerseMenu={onCloseVerseMenu}
            surahName={surahName}
          />
        )
      ))}
    </div>
  );
};

export default VerseList;
