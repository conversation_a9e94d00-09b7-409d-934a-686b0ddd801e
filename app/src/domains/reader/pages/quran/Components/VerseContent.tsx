import React from 'react';
import { CombinedVerseData, ITranslator } from '@reader/models/types';
import { ContentMode } from '@reader/hooks/quran/useContentMode';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { Mode1Content } from './Mode1Content';
import { Mode2Content } from './Mode2Content';
import { Mode3Content } from './Mode3Content';
import { Mode4Content } from './Mode4Content';

interface VerseContentProps {
  verse: CombinedVerseData;
  viewMode: ContentMode;
  selectedTranslators: string[];
  availableTranslators: ITranslator[];
  verseBorderColor: string;
  translatorTitleColor: string;
  sectionLeftBorderColor: string;
  footnoteColor: string;
  surahName?: string;
  onVerseMenuClick?: (verseKey: string, event: React.MouseEvent) => void;
  openMenuVerseKey?: string | null;
  onCloseVerseMenu?: () => void;
}

export const VerseContent: React.FC<VerseContentProps> = ({
  verse,
  viewMode,
  selectedTranslators,
  availableTranslators,
  verseBorderColor,
  translatorTitleColor,
  sectionLeftBorderColor,
  footnoteColor,
  surahName,
  onVerseMenuClick,
  openMenuVerseKey,
  onCloseVerseMenu,
}) => {
  const verseNumberBg = useAutoOverlay(21, 'var(--bg-color)');

  if (viewMode === '1') {
    return (
      <Mode1Content
        verse={verse}
        verseNumberBg={verseNumberBg}
        onVerseMenuClick={onVerseMenuClick}
      />
    );
  } else if (viewMode === '2') {
    return (
      <Mode2Content
        combinedVerse={verse}
        verseBorderColor={verseBorderColor}
        translatorTitleColor={translatorTitleColor}
        sectionLeftBorderColor={sectionLeftBorderColor}
        footnoteColor={footnoteColor}
        verseNumberBg={verseNumberBg}
        selectedTranslators={selectedTranslators}
        availableTranslators={availableTranslators}
        onVerseMenuClick={onVerseMenuClick}
        openMenuVerseKey={openMenuVerseKey}
        onCloseVerseMenu={onCloseVerseMenu}
        surahName={surahName}
      />
    );
  } else if (viewMode === '3') {
    return (
      <Mode3Content
        verse={verse}
        verseBorderColor={verseBorderColor}
        translatorTitleColor={translatorTitleColor}
        sectionLeftBorderColor={sectionLeftBorderColor}
        footnoteColor={footnoteColor}
        verseNumberBg={verseNumberBg}
        selectedTranslators={selectedTranslators}
        availableTranslators={availableTranslators}
        onVerseMenuClick={onVerseMenuClick}
        openMenuVerseKey={openMenuVerseKey}
        onCloseVerseMenu={onCloseVerseMenu}
        surahName={surahName}
      />
    );
  } else if (viewMode === '4') {
    return (
      <Mode4Content
        verse={verse}
        verseBorderColor={verseBorderColor}
        translatorTitleColor={translatorTitleColor}
        sectionLeftBorderColor={sectionLeftBorderColor}
        footnoteColor={footnoteColor}
        selectedTranslators={selectedTranslators}
        availableTranslators={availableTranslators}
        onVerseMenuClick={onVerseMenuClick}
        openMenuVerseKey={openMenuVerseKey}
        onCloseVerseMenu={onCloseVerseMenu}
        surahName={surahName}
      />
    );
  } else {
    return null;
  }
};
