import React from 'react';
import { X } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { CombinedVerseData } from '@reader/models/types';

// MobileActionMenuProps interface definition
export interface MobileActionMenuProps {
  isOpen?: boolean;
  onClose?: () => void;
  sheetTitle?: string;
  menuItems?: ActionMenuItem[];
  executeAction?: (e: React.MouseEvent, action?: ((e: React.MouseEvent) => void) | undefined) => void;
  onOpenNoteSheet?: (verseKey: string, verseData: CombinedVerseData, surahName?: string) => void;
  onOpenBookmarkSheet?: (verseKey: string, verseData: CombinedVerseData, surahName?: string) => void;
  onOpenTafsirSheet?: () => void;
}

// ActionMenuItem interface definition
export interface ActionMenuItem {
  icon: any;
  text: string;
  action: string;
  handler?: ((e?: React.MouseEvent) => void) | undefined;
  isGreen?: boolean;
}

export const MobileActionSheet: React.FC<MobileActionMenuProps> = ({
  isOpen,
  onClose,
  sheetTitle,
  menuItems,
  executeAction,
  onOpenNoteSheet,
  onOpenBookmarkSheet,
  onOpenTafsirSheet,
}) => {
  const sheetBgColor = useAutoOverlay(8, 'var(--bg-color)') as string;
  const listBgColor = useAutoOverlay(4, 'var(--bg-color)') as string;
  
  if (!isOpen) return null;

  // Mobil için olan UI kodunu UnifiedActionMenu'dan buraya taşıdık.
  const backdropClasses = "fixed inset-0 bg-black/40 backdrop-blur-[1px] z-40 transition-opacity";
  const wrapperClasses = `fixed z-50 bottom-0 left-0 right-0 transition-transform duration-300 ease-in-out ${isOpen ? 'translate-y-0' : 'translate-y-full'}`;
  const sheetContainerClasses = 'w-full rounded-t-2xl shadow-2xl border-t';

  return (
    <>
      <div className={`${backdropClasses} ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={onClose} />
      <div className={wrapperClasses} style={{ pointerEvents: isOpen ? 'auto' : 'none' }}>
        <div 
          className={`${sheetContainerClasses} border-opacity-20`} 
          style={{ backgroundColor: sheetBgColor, borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)' }} 
          onClick={(e) => e.stopPropagation()}
        >
          {/* Mobil UI için standart üst çubuk */}
          <div className="w-10 h-1 mx-auto mt-2 mb-0 rounded-full bg-[var(--text-color)] opacity-15" />
          
          {sheetTitle && (
            <div className="flex justify-between items-center px-4 pt-1 pb-1 mb-1" style={{ backgroundColor: sheetBgColor }}>
              <span className="text-base font-medium" style={{ color: 'var(--text-color)' }}>{sheetTitle}</span>
              <button onClick={onClose} className="p-1.5 rounded-full hover:bg-[var(--text-color)]/10 text-[var(--text-color)]" aria-label="Kapat">
                <X size={18} />
              </button>
            </div>
          )}

          <div className="px-4 pt-0 pb-3">
            <div className="rounded-xl px-3 py-1 overflow-hidden" style={{ backgroundColor: listBgColor}}> 
              <div className="max-h-[calc(80vh-4rem)] overflow-y-auto grid grid-cols-2">
                {menuItems.map((item: ActionMenuItem, index) => (
                  <button 
                    key={item.action} 
                    onClick={async (e) => {
                      try {
                        await executeAction(e, item.handler);
                      } catch (error) {
                        console.error('Mobile sheet action failed:', error);
                      }
                    }} 
                    disabled={!item.handler || item.disabled} 
                    className={`flex items-center w-full px-4 py-3 text-sm text-left rounded-none hover:bg-[var(--text-color)]/5 odd:border-r odd:border-[color-mix(in_srgb,var(--text-color)_10%,transparent)] ${index >= menuItems.length - 2 ? 'border-b-0' : 'border-b border-[color-mix(in_srgb,var(--text-color)_10%,transparent)]'}`} 
                    style={{ color: item.isGreen ? 'var(--success-color)' : 'var(--text-color)' }}
                  >
                    {item.icon && <item.icon size={18} className="mr-3 opacity-80 flex-shrink-0" />}
                    <span className="flex-grow truncate">{item.text}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
          {/* iPhone'larda alttaki çubuk için güvenli alan */}
          <div className="pb-safe"></div>
        </div>
      </div>
    </>
  );
};
