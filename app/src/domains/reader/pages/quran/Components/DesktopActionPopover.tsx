import React, { useState, useRef, useEffect } from 'react';
import { 
  useFloating, 
  FloatingPortal, 
  useClick, 
  useDismiss, 
  useRole, // Erişilebilirlik için rol ataması
  useFocus, // Odak yönetimi
  useListNavigation, // Liste navigasyonu (ok tuşları)
  useInteractions, 
  flip, 
  shift, 
  offset,
  autoUpdate
} from '@floating-ui/react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

// DesktopActionMenuProps interface definition
export interface DesktopActionMenuProps {
  isOpen?: boolean;
  onClose?: () => void;
  sheetTitle?: string;
  menuItems?: ActionMenuItem[];
  executeAction?: (e: React.MouseEvent, action?: ((e: React.MouseEvent) => void) | undefined) => void;
  anchorEl?: HTMLElement | null;
  placement?: 'left-start' | 'right-start' | 'top' | 'bottom';
  onOverflow?: () => void;
  widthClass?: string; // Default: 'w-auto min-w-[160px] max-w-[240px]'
}

// ActionMenuItem interface definition
export interface ActionMenuItem {
  icon: any;
  text: string;
  action: string;
  handler?: ((e?: React.MouseEvent) => void) | undefined;
  isGreen?: boolean;
}

export const DesktopActionPopover: React.FC<DesktopActionMenuProps> = ({
  isOpen,
  onClose,
  sheetTitle,
  menuItems,
  executeAction,
  anchorEl,
  placement = 'left-start',
  onOverflow,
  widthClass = 'w-auto min-w-[160px] max-w-[240px]'
}) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const listRef = useRef<Array<HTMLButtonElement | null>>([]);

  // Hook always called at the component level
  const popoverBgColor = useAutoOverlay(12, 'var(--bg-color)');

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: (open) => !open && onClose?.(),
    placement: placement,
    strategy: 'fixed',
    elements: {
      reference: anchorEl,
    },
    middleware: [
      offset(10),
      flip(),
      shift(),
    ],
    whileElementsMounted: autoUpdate,
  });

  // --- Erişilebilirlik için Etkileşim Kurulumu --- //
  const focus = useFocus(context);
  const click = useClick(context);
  const dismiss = useDismiss(context);
  const role = useRole(context, { role: 'menu' });
  const listNavigation = useListNavigation(context, {
    listRef,
    activeIndex,
    onNavigate: setActiveIndex,
  });

  const { getFloatingProps, getItemProps } = useInteractions([
    focus,
    click,
    dismiss,
    role,
    listNavigation,
  ]);

  // Menü kapandığında aktif indeksi sıfırla
  useEffect(() => {
    if (!isOpen) {
      setActiveIndex(null);
    }
  }, [isOpen]);

  // Taşma kontrolü
  useEffect(() => {
    if (isOpen && refs.floating.current) {
      const rect = refs.floating.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      
      // Sadece YATAY taşmayı kontrol et.
      // Eğer popover'ın solu veya sağı ekran dışına çıkıyorsa, taşma var demektir.
      // Dikey taşmayı floating-ui'nin kendisi `flip` ile yönettiği için onu sorun etmiyoruz.
      if (rect.left < 5 || rect.right > (viewportWidth - 5)) { // 5px tolerans payı
        onOverflow?.();
      }
    }
    // Bu useEffect, popover'ın konumu her güncellendiğinde (context.x/y) çalışır.
  }, [isOpen, refs.floating, context.x, context.y, onOverflow]);

  if (!isOpen || !anchorEl) return null;

  return (
    <FloatingPortal>
      <div
        ref={refs.setFloating}
        style={floatingStyles}
        {...getFloatingProps()}
        className="z-50"
      >
        <div
          className={`${widthClass} rounded-xl shadow-2xl border border-opacity-20 overflow-hidden whitespace-nowrap ${!sheetTitle ? 'pt-1' : ''}`}
          style={{ backgroundColor: popoverBgColor, borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)' }}
        >
          {sheetTitle && (
            <div className="px-3 py-2 border-b border-[color-mix(in_srgb,var(--text-color)_10%,transparent)]">
              <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>{sheetTitle}</span>
            </div>
          )}
          <div className="py-1" role="menu">
            {menuItems?.map((item: ActionMenuItem, index: number) => (
              <button
                key={item.action}
                ref={(node) => { listRef.current[index] = node; }} // Klavye navigasyonu için referans
                role="menuitem"
                tabIndex={activeIndex === index ? 0 : -1} // Sadece aktif eleman focus edilebilir olsun
                {...getItemProps({ // Etkileşim propları
                  onClick: async (e) => {
                    try {
                      if (executeAction) {
                        await executeAction(e, item.handler);
                      }
                    } catch (error) {
                      console.error('Desktop popover action failed:', error);
                    }
                  },
                })}
                disabled={!item.handler}
                className="flex items-center w-full px-3 py-2 text-sm text-left hover:bg-[var(--text-color)]/5 focus:bg-[var(--text-color)]/10 outline-none"
                style={{ color: item.isGreen ? 'var(--success-color)' : 'var(--text-color)' }}
              >
                {item.icon && <item.icon size={16} className="mr-3 opacity-80 flex-shrink-0" />}
                <span className="flex-grow truncate">{item.text}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </FloatingPortal>
  );
};
