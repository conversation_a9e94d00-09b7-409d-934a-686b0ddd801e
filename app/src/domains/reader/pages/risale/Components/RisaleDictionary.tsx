import React from 'react';
import { RisaleDictionaryItem } from '@reader/models/types';

interface RisaleDictionaryProps {
  dictionary?: RisaleDictionaryItem[];
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const RisaleDictionary: React.FC<RisaleDictionaryProps> = ({
  dictionary = [],
  isOpen,
  setIsOpen
}) => {
  if (!dictionary.length) return null;
  
  return (
    <div className="mt-10 border-t pt-6 border-[color-mix(in_srgb,var(--text-color)_20%,transparent)]"> 
      <details 
        open={isOpen}
        onToggle={(e) => {
          if (e.currentTarget) {
            setIsOpen(e.currentTarget.open);
          }
        }}
      > 
        <summary 
          onClick={(e) => { 
            e.preventDefault();
            e.stopPropagation();
            setIsOpen(!isOpen);
          }}
          className="text-xl font-semibold mb-4 cursor-pointer list-none">
          <span className="flex items-center"> 
            <svg 
              viewBox="0 0 24 24" 
              width="18" 
              height="18" 
              fill="currentColor" 
              className={`mr-2 transform transition-transform duration-200 ${isOpen ? 'rotate-90' : ''}`} 
              aria-hidden="true"
            >
              <polygon points="8 5 18 12 8 19" /> 
            </svg>
            Sözlük
          </span> 
        </summary> 
        <div className="mt-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2"> 
          {dictionary.map((item, index) => (
            <div key={`${item.word}-${index}`} className="text-sm border-b pb-1 border-[color-mix(in_srgb,var(--text-color)_10%,transparent)]"> 
              <span className="font-semibold">{item.word}:</span> <span className="opacity-90">{item.meaning}</span> 
            </div> 
          ))} 
        </div> 
      </details> 
    </div> 
  );
};

export default RisaleDictionary; 