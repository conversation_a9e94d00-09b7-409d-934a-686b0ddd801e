import React from 'react';
import { NavigationFooter } from '@shared/components';
import { RisaleSectionDef } from '@reader/models/types';

interface RisaleNavigationProps {
  prevSection: RisaleSectionDef | null;
  nextSection: RisaleSectionDef | null;
  navigateToSection: (id: string) => void;
}

const RisaleNavigation: React.FC<RisaleNavigationProps> = ({
  prevSection,
  nextSection,
  navigateToSection
}) => {
  return (
    <NavigationFooter
      prevSection={prevSection ? {
        id: prevSection.id,
        title: prevSection.title,
        onClick: () => navigateToSection(prevSection.id)
      } : undefined}
      nextSection={nextSection ? {
        id: nextSection.id,
        title: nextSection.title,
        onClick: () => navigateToSection(nextSection.id)
      } : undefined}
    />
  );
};

export default RisaleNavigation; 