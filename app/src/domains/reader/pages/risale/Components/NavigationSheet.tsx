import React from 'react';
import { X, ChevronRight, Loader2, Search } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { RisaleSectionDef, Book } from '@reader/models/types';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';

interface NavigationSheetProps {
  isOpen: boolean;
  onClose: () => void;
  navigationType: 'section' | 'books';
  onChangeNavigationType: (type: 'section' | 'books') => void;
  currentBookId: string;
  selectedBookId: string | null;
  books: Book[];
  sectionSearch: string;
  onChangeSectionSearch: (search: string) => void;
  bookSearch: string;
  onChangeBookSearch: (search: string) => void;
  currentSectionId?: string;
  filteredSections: RisaleSectionDef[];
  onSelectSection: (sectionId: string) => void;
  onSelectBook: (bookId: number, navigateToPage?: boolean) => void;
  isLoadingSections: boolean;
  hasSingleSection?: boolean;
}

const NavigationSheet = ({
  isOpen,
  onClose,
  navigationType,
  onChangeNavigationType,
  currentBookId,
  selectedBookId,
  books,
  sectionSearch,
  onChangeSectionSearch,
  bookSearch,
  onChangeBookSearch,
  currentSectionId,
  filteredSections,
  onSelectSection,
  onSelectBook,
  isLoadingSections,
  hasSingleSection = false,
}: NavigationSheetProps) => {
  const navBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const toolbarBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');

  if (!isOpen) return null;

  const filteredBooks = filterByNormalizedQuery(books, bookSearch, (book) => [
    book.title,
    book.author || ''
  ]);
  
  const selectBookAndShowSections = (bookId: number) => {
    onSelectBook(bookId, false);
    onChangeNavigationType('section');
  };
  
  const displayedBookId = selectedBookId || currentBookId;
  const currentBook = books.find(b => b.id.toString() === displayedBookId);

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        style={{ opacity: isOpen ? 1 : 0 }}
        onClick={onClose}
      />
      
      {/* Sheet */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[390px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{ 
          backgroundColor: navBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />
        
        {/* Segment Control with Close Button */}
        <div className="mx-4 mt-4 flex items-center justify-between">
          <div className="flex-1 flex p-1 rounded-lg" style={{ backgroundColor: toolbarBgColor }}>
            <button
              onClick={() => onChangeNavigationType('books')}
              className={`flex-1 flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${navigationType === 'books' ? 'shadow' : ''}`}
              style={{
                backgroundColor: navigationType === 'books' ? 'var(--bg-color)' : 'transparent',
                color: 'var(--text-color)'
              }}
            >
              Kitap
            </button>
            {!hasSingleSection && (
              <button
                onClick={() => onChangeNavigationType('section')}
                className={`flex-1 flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${navigationType === 'section' ? 'shadow' : ''}`}
                style={{
                  backgroundColor: navigationType === 'section' ? 'var(--bg-color)' : 'transparent',
                  color: 'var(--text-color)'
                }}
              >
                Bölüm
              </button>
            )}
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center ml-2 rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
          >
            <X size={18} />
          </button>
        </div>

        {/* Content */}
        <div className="px-4 pt-3 pb-1 overflow-y-auto max-h-[calc(80vh-4rem)] md:max-h-[calc(70vh-6rem)] mt-2">
          {navigationType === 'books' && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={bookSearch}
                  onChange={(e) => onChangeBookSearch(e.target.value)}
                  placeholder="Kitap adı ara..."
                  className={`w-full px-4 py-2.5 pl-9 rounded-lg text-sm text-[var(--text-color)]/90 placeholder:text-[var(--text-color)]/30 focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors duration-200`}
                  style={{ 
                    backgroundColor: listAreaBgColor
                  }}
                />
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                />
              </div>
              <div 
                className="space-y-1 p-2 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                {filteredBooks.map((book) => {
                  const isCurrentBook = book.id.toString() === currentBookId;
                    
                  return (
                    <button
                      key={book.id}
                      onClick={() => onSelectBook(book.id, true)}
                      className={`w-full px-3 py-2.5 text-left rounded-lg text-sm flex items-center justify-between relative transition-colors duration-200 text-[var(--text-color)] ${
                        isCurrentBook ? 'bg-[var(--bg-color)] shadow' : 'hover:bg-[var(--text-color)]/5'
                      }`}
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <span 
                          className={`truncate ${isCurrentBook ? 'font-bold' : 'font-medium'}`}
                        >
                          {book.title}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 pl-2">
                        <button
                          type="button"
                          className="p-1.5 rounded-full hover:bg-[var(--text-color)]/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            selectBookAndShowSections(book.id);
                          }}
                          title={`${book.title} Bölümleri`}
                        >
                          <ChevronRight size={14} style={{ color: 'var(--text-color)' }} />
                        </button>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {navigationType === 'section' && !hasSingleSection && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={sectionSearch}
                  onChange={(e) => onChangeSectionSearch(e.target.value)}
                  placeholder="Bölüm adı veya numarası ara..."
                  className={`w-full px-4 py-2.5 pl-9 rounded-lg text-sm text-[var(--text-color)]/90 placeholder:text-[var(--text-color)]/30 focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors duration-200`}
                  style={{ 
                    backgroundColor: listAreaBgColor
                  }}
                />
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                />
              </div>
              
              <div className="mb-2 py-2 text-sm font-medium">
                <span className="opacity-60">Kitap: </span>
                <span className="font-bold">{currentBook?.title || `Kitap ${currentBookId}`}</span>
              </div>

              <div 
                className="space-y-1 p-2 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                {isLoadingSections ? (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 size={24} className="animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  filteredSections.map(section => {
                    const isCurrent = section.id === currentSectionId;

                    return (
                      <button
                        key={section.id}
                        onClick={() => {
                          onSelectSection(section.id);
                          onClose();
                        }}
                        className={`w-full px-3 py-2.5 text-left rounded-lg text-sm relative transition-colors duration-200 text-[var(--text-color)] ${
                          isCurrent ? 'bg-[var(--bg-color)] shadow' : 'hover:bg-[var(--text-color)]/5'
                        }`}
                      >
                        <span className={isCurrent ? 'font-bold' : 'font-medium'}>
                          {section.title}
                        </span>
                      </button>
                    );
                  })
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default NavigationSheet;
