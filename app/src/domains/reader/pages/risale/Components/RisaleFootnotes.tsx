import React from 'react';
import { RisaleFootnote } from '@reader/models/types';

interface RisaleFootnotesProps {
  footnotes?: RisaleFootnote[];
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const RisaleFootnotes: React.FC<RisaleFootnotesProps> = ({
  footnotes = [],
  isOpen,
  setIsOpen
}) => {
  if (!footnotes.length) return null;
  
  return (
    <div className="mt-10 border-t pt-6 border-[color-mix(in_srgb,var(--text-color)_20%,transparent)]"> 
      <details 
        open={isOpen} 
        onToggle={(e) => {
          if (e.currentTarget) {
            setIsOpen(e.currentTarget.open);
          }
        }}
      > 
        <summary 
          onClick={(e) => { 
            e.preventDefault(); 
            e.stopPropagation(); 
            setIsOpen(!isOpen); 
          }}
          className="text-xl font-semibold mb-4 cursor-pointer list-none">
          <span className="flex items-center"> 
            <svg 
              viewBox="0 0 24 24" 
              width="18" 
              height="18" 
              fill="currentColor" 
              className={`mr-2 transform transition-transform duration-200 ${isOpen ? 'rotate-90' : ''}`} 
              aria-hidden="true"
            >
              <polygon points="8 5 18 12 8 19" /> 
            </svg>
            Dipnotlar
          </span> 
        </summary> 
        <div className="mt-3 space-y-3"> 
          {footnotes.map((note) => (
            <div key={note.number} className="flex text-sm"> 
              <span className="font-semibold mr-2">{note.number}.</span> 
              <span className="opacity-90">{note.content}</span> 
            </div> 
          ))} 
        </div> 
      </details> 
    </div>
  );
};

export default RisaleFootnotes; 