import React, { useMemo, forwardRef, useState, useCallback } from 'react';
import { RisaleSentence } from '@reader/models/types';
import type { Annotation } from '@domains/reader-interactions/shared/types';
import { TextSelectionHandler } from '@domains/reader-interactions/text-selection';
import { useStyledTextSegments, TextHighlighterStyles } from '@domains/reader-interactions/highlights/hooks/useStyledTextSegments.tsx';
import { AnnotationFloatingPanel } from '@domains/reader-interactions/shared';
import { AnnotationDetailSheet } from '@domains/reader-interactions/annotations/components/AnnotationDetailSheet';
import { AnnotationEditModal } from '@domains/reader-interactions/annotations/components/AnnotationEditModal';
import { useAnnotationManager } from '@domains/reader-interactions/annotations';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useParams } from 'react-router-dom';
import { ParagraphActionButton } from '@domains/reader-interactions/paragraph/components/ParagraphActionButton';

interface RisaleContentProps {
  sentences: RisaleSentence[] | null;
}

const StyledSentence: React.FC<{
  sentence: RisaleSentence;
  annotations: Annotation[];
  onAnnotationClick: (annotation: Annotation) => void;
  allSentences: { id: string; text: string }[];
  skipId?: boolean;
}> = ({ sentence, annotations, onAnnotationClick, allSentences, skipId = false }) => {
  const { styledText, trailingSpaceStyle } = useStyledTextSegments({
    text: sentence.text || '',
    annotations: annotations,
    onAnnotationClick: onAnnotationClick,
    sentenceId: sentence.id || '',
    allSentences: allSentences,
  });

  // Check if text already contains processed verse break HTML
  const hasProcessedVerseBreak = (sentence.text || '').includes('verse-separator');
  
  // Show verse break if: 
  // 1. verse_break_after property is true, OR
  // 2. text already contains processed verse break (don't add extra)
  const shouldShowVerseBreak = sentence.verse_break_after && !hasProcessedVerseBreak;

  const verseBreak = shouldShowVerseBreak ? (
    <span
      className="verse-separator inline-block w-3 h-3 rounded-full bg-current opacity-40 mx-2 align-middle"
      title="Ayet Sonu"
    />
  ) : (
    <span style={trailingSpaceStyle}> </span>
  );

  // Prepare data attributes
  const dataAttributes = !skipId ? {
    id: `sentence-${sentence.id}`,
    'data-sentence-id': sentence.id,
    ...(sentence.tags && sentence.tags.length > 0 && { 'data-tags': JSON.stringify(sentence.tags) })
  } : {};

  if (sentence.line_break) {
    return (
      <div {...dataAttributes}>
        {styledText}
        {verseBreak}
      </div>
    );
  }

  return (
    <span {...dataAttributes}>
      {styledText}
      {verseBreak}
    </span>
  );
};

const RisaleContent = forwardRef<HTMLDivElement, RisaleContentProps>((
  { sentences }, 
  ref
) => {
  const { bookId, sectionId } = useParams<{ bookId: string; sectionId: string }>();
  const user = useAuthStore((state) => state.user);
  
  const { annotations, loadAnnotations, deleteAnnotation, updateAnnotation } = useAnnotationManager({
    book_id: bookId,
    section_id: sectionId,
    user_id: user?.id,
  });

  // State for detail and edit modals
  const [isDetailSheetOpen, setIsDetailSheetOpen] = useState(false);
  const [selectedAnnotations, setSelectedAnnotations] = useState<Annotation[]>([]);
  const [initialAnnotationId, setInitialAnnotationId] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingAnnotation, setEditingAnnotation] = useState<Annotation | null>(null);

  // States for visibility toggles
  const [showNotes, setShowNotes] = useState(true);
  const [showSherhs, setShowSherhs] = useState(true);
  const [showHighlights, setShowHighlights] = useState(true);
  const [showBookmarks, setShowBookmarks] = useState(true);

  const handleParagraphTrigger = useCallback((btn: HTMLElement) => {
    // Programmatic selection approach - paragraph'ı seçip TextActionMenu'yu trigger et
    
    const paragraphId = btn.getAttribute('data-paragraph-id');
    const sentenceIdsRaw = btn.getAttribute('data-paragraph-sentences');
    
    if (!paragraphId) return;
    
    // Parse sentence IDs from button data
    let sentenceIds: string[] = [];
    try { 
      sentenceIds = JSON.parse(sentenceIdsRaw || '[]'); 
    } catch (e) {
      console.error('Failed to parse sentence IDs:', e);
      return;
    }
    
    if (sentenceIds.length === 0) return;
    
    // Find sentence elements by their exact IDs
    const sentenceElements: Element[] = [];
    sentenceIds.forEach(id => {
      const element = document.querySelector(`[data-sentence-id="${id}"]`);
      if (element) {
        sentenceElements.push(element);
      }
    });
    
    if (sentenceElements.length === 0) return;
    
    // Create selection spanning all sentences in the paragraph
    const selection = window.getSelection();
    const range = document.createRange();
    
    if (selection && sentenceElements.length > 0) {
      // Set range from first sentence start to last sentence end
      const firstSentence = sentenceElements[0];
      const lastSentence = sentenceElements[sentenceElements.length - 1];
      
      // Use selectNodeContents to select inside the elements, not around them
      if (sentenceElements.length === 1) {
        // Single sentence - select its contents
        range.selectNodeContents(firstSentence);
      } else {
        // Multiple sentences - select from start of first to end of last
        range.setStart(firstSentence, 0);
        range.setEnd(lastSentence, lastSentence.childNodes.length);
      }
      
      selection.removeAllRanges();
      selection.addRange(range);
      
      // Small delay to ensure selection is processed before triggering event
      setTimeout(() => {
        // TextSelectionHandler should now detect the sentence selection
        const selectionEvent = new Event('selectionchange', { bubbles: true });
        document.dispatchEvent(selectionEvent);
      }, 10);
    }
  }, []);

  const getAlignmentClass = (alignment?: 'left' | 'center' | 'right'): string => {
    switch (alignment) {
      case 'center': return 'text-center md:max-w-[90%] mx-auto';
      case 'right': return 'text-right';
      default: return 'text-left';
    }
  };

  const allSentences = useMemo(() => 
    (sentences || []).filter(s => s.id && s.text).map(s => ({ id: s.id!, text: s.text! })),
    [sentences]
  );

  const handleAnnotationClick = useCallback((clickedAnnotation: Annotation) => {
    // Find all annotations that are on the same sentences as the clicked one.
    const clickedSentenceIds = Array.isArray(clickedAnnotation.sentence_id)
      ? clickedAnnotation.sentence_id
      : [clickedAnnotation.sentence_id];

    if (!clickedSentenceIds || clickedSentenceIds.length === 0) return;

    const relatedAnnotations = annotations.filter(anno => {
      const annoSentenceIds = Array.isArray(anno.sentence_id) ? anno.sentence_id : [anno.sentence_id];
      if (!annoSentenceIds) return false;
      return annoSentenceIds.some((id: string) => clickedSentenceIds.includes(id));
    });

    setSelectedAnnotations(relatedAnnotations.length > 0 ? relatedAnnotations : [clickedAnnotation]);
    setInitialAnnotationId(clickedAnnotation.id);
    setIsDetailSheetOpen(true);
  }, [annotations]);

  const handleEditAnnotation = (annotation: Annotation) => {
    setEditingAnnotation(annotation);
    setIsDetailSheetOpen(false);
    setIsEditModalOpen(true);
  };

  const handleDeleteAnnotation = async (annotationId: string) => {
    const success = await deleteAnnotation(annotationId);
    if (success) {
      loadAnnotations(); // Refresh annotations
    }
    return success;
  };

  const handleAnnotationUpdated = () => {
    setIsEditModalOpen(false);
    setEditingAnnotation(null);
    loadAnnotations(); // Refresh the list
  };

  const handleChangeColor = async (annotation: Annotation, newColor: string): Promise<boolean> => {
    try {
      const result = await updateAnnotation(annotation.id, { color: newColor });
      if (result) {
        loadAnnotations(); // Refresh annotations to show updated color
        return true;
      }
      return false;
    } catch (error) {
      console.error('[RisaleContent] Color change error:', error);
      return false;
    }
  };

  const renderContent = () => {
    if (!sentences) return null;

    const contentElements: JSX.Element[] = [];
    let currentParagraphSentences: RisaleSentence[] = [];

    let paragraphCounter = 0;
    const processParagraph = () => {
      if (currentParagraphSentences.length === 0) return;

      const alignment = getAlignmentClass(currentParagraphSentences[0]?.alignment);

      const thisParagraphIndex = paragraphCounter++;
      const paragraphId = `paragraph-${thisParagraphIndex + 1}`; // 1-based gösterim

      const paragraphContent = currentParagraphSentences.map((sentence) => {
        let sentenceAnnotations = annotations.filter(annotation =>
          (Array.isArray(annotation.sentence_id)
            ? annotation.sentence_id.includes(sentence.id!)
            : annotation.sentence_id === sentence.id)
        );

        sentenceAnnotations = sentenceAnnotations.filter(annotation => {
          if (annotation.annotation_type === 'highlight' && !showHighlights) return false;
          if (annotation.annotation_type === 'note' && !showNotes) return false;
          if (annotation.annotation_type === 'bookmark' && !showBookmarks) return false;
          if (annotation.annotation_type === 'sherh' && !showSherhs) return false;
          return true;
        });

        const mappedAnnotations = sentenceAnnotations.map(annotation => ({
          ...annotation,
        } as Annotation));

        return (
          <StyledSentence
            key={sentence.id}
            sentence={sentence}
            annotations={mappedAnnotations}
            onAnnotationClick={handleAnnotationClick}
            allSentences={allSentences}
          />
        );
      });
      contentElements.push(
        <div
          key={`p-${contentElements.length}`}
          id={paragraphId}
          data-paragraph-id={paragraphId}
          data-paragraph-index={thisParagraphIndex}
          className={`mb-4 group ${alignment}`}
        >
          <span className="inline">{paragraphContent}</span>
          <ParagraphActionButton
            sentenceIds={currentParagraphSentences.map(s=>s.id!)}
            paragraphText={currentParagraphSentences.map(s=>s.text).join(' ')}
            paragraphId={paragraphId}
            paragraphIndex={thisParagraphIndex}
            onTrigger={(el) => handleParagraphTrigger(el)}
            variant="inline"
          />
        </div>
      );

      const lastSentence = currentParagraphSentences[currentParagraphSentences.length - 1];
      if (lastSentence.divider_after) {
        contentElements.push(<hr key={`d-${contentElements.length}`} className="my-6 w-4/5 mx-auto border-t-2" style={{ borderColor: 'color-mix(in srgb, var(--text-color) 30%, transparent)' }} />);
      }

      currentParagraphSentences = [];
    };

    sentences.forEach(sentence => {
      if (sentence.type === 'title') {
        processParagraph(); // Process any pending paragraph before the title

        // Prepare title data attributes
        const titleDataAttributes = {
          id: `sentence-${sentence.id}`,
          'data-sentence-id': sentence.id,
          ...(sentence.tags && sentence.tags.length > 0 && { 'data-tags': JSON.stringify(sentence.tags) })
        };

        contentElements.push(
          <h2 key={`h-${sentence.id}`} {...titleDataAttributes} className="text-3xl font-bold text-center my-7">
            <StyledSentence
              sentence={sentence}
              annotations={annotations.filter(a => a.sentence_id === sentence.id)}
              onAnnotationClick={handleAnnotationClick}
              allSentences={allSentences}
              skipId={true}
            />
          </h2>
        );
        return; // Continue to next sentence
      }

      if (sentence.paragraph_start && currentParagraphSentences.length > 0) {
        processParagraph();
      }
      currentParagraphSentences.push(sentence);
    });

    processParagraph(); // Process any remaining sentences

    return contentElements;
  };

  return (
    <>
      <TextHighlighterStyles />
      <div ref={ref}>
        <TextSelectionHandler
          className="risale-content leading-relaxed text-lg font-sans"
          onAnnotationCreated={loadAnnotations}
        >
          <div lang="tr">
            {renderContent()}
          </div>
        </TextSelectionHandler>
      </div>
      <AnnotationFloatingPanel
        annotations={annotations}
        showNotes={showNotes}
        setShowNotes={setShowNotes}
        showSherhs={showSherhs}
        setShowSherhs={setShowSherhs}
        showHighlights={showHighlights}
        setShowHighlights={setShowHighlights}
        showBookmarks={showBookmarks}
        setShowBookmarks={setShowBookmarks}
        onAnnotationDetailOpen={(annotation) => {
          // Tüm annotation'ları gönder (global navigation için)
          setSelectedAnnotations(annotations);
          setInitialAnnotationId(annotation.id);
          setIsDetailSheetOpen(true);
        }}
      />

      {isDetailSheetOpen && selectedAnnotations.length > 0 && initialAnnotationId && (
        <AnnotationDetailSheet
          isOpen={isDetailSheetOpen}
          onClose={() => setIsDetailSheetOpen(false)}
          annotations={selectedAnnotations}
          initialAnnotationId={initialAnnotationId}
          onEdit={handleEditAnnotation}
          onDelete={handleDeleteAnnotation}
          onChangeColor={handleChangeColor}
        />
      )}

      {isEditModalOpen && editingAnnotation && (
        <AnnotationEditModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          annotation={editingAnnotation}
          onUpdated={handleAnnotationUpdated}
        />
      )}
    </>
  );
});

export default RisaleContent;
