import React from 'react';

interface RisaleHeaderProps {
  title: string;
  metadata?: {
    author?: string;
    // Di<PERSON>er üst veriler eklenebilir
  };
}

const RisaleHeader: React.FC<RisaleHeaderProps> = ({ title, metadata }) => {
  return (
    <div className="mb-6">
      <h1 className="text-2xl sm:text-3xl font-semibold mb-1 text-[var(--text-color)]">{title}</h1>
      {metadata && (
        <div className="text-sm opacity-70 text-[var(--text-color)]">
          {metadata.author || '<PERSON> Nursi'}
        </div>
      )}
    </div>
  );
};

export default RisaleHeader; 