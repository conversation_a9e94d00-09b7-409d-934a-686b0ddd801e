import { useState, useRef } from 'react';
import * as React from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { PageLayout, EnhancedErrorState, LoadingState, Tooltip } from '@shared/components';
import { useRisaleSection, useRisaleProcess, useRisaleNavigationSheet, useReaderInteractivity } from '@reader/hooks'; // imports from index file
import { RisaleSectionDef } from '@reader/models/types';
import { ChevronDown } from 'lucide-react';
import NavigationSheet from './Components/NavigationSheet';
import { useIsMobile } from '@shared/hooks/useIsMobile';

import RisaleContent from './Components/RisaleContent';
import RisaleFootnotes from './Components/RisaleFootnotes';
import RisaleDictionary from './Components/RisaleDictionary';
import RisaleNavigation from './Components/RisaleNavigation';

const ReadPage = () => {
  const { bookId, sectionId } = useParams<{ bookId: string; sectionId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  // Marker color is computed dynamically in the helper via CSS color-mix

  const [isFootnotesOpen, setIsFootnotesOpen] = useState(false);
  const [isDictionaryOpen, setIsDictionaryOpen] = useState(false);
  const isMobile = useIsMobile();
  
  const contentRef = useRef<HTMLDivElement>(null);
  const actionMenuRef = useRef<HTMLDivElement>(null);

  const { data: sectionData, loading: isLoadingContent, error } = useRisaleSection(bookId, sectionId);
  
  const {
    isOpen,
    setIsOpen,
    navigationType,
    setNavigationType,
    sectionSearch,
    setSectionSearch,
    bookSearch,
    setBookSearch,
    handleNavigation: handleNavSheetNavigation,
    switchBookInSheet,
    selectedBookId,
    filteredSections,
    openNavigationSheet,
    isLoadingSections,
    books
  } = useRisaleNavigationSheet({ 
    currentBookId: bookId,
    initialBookTitle: sectionData?.metadata?.title,
  });

  const {
    isTooltipVisible,
    tooltipPosition,
    tooltipContent,
    hideTooltip,
  } = useReaderInteractivity({
    contentRef,
    actionMenuRef,
    sectionData,
  });

  const { processedSentences } = useRisaleProcess(sectionData);

  // This log helps verify what data is being passed to the content renderer
  React.useEffect(() => {
    if (processedSentences) {
      console.log('[DEBUG] ReadPage: Sentences being passed to RisaleContent:', JSON.parse(JSON.stringify(processedSentences)));
    }
  }, [processedSentences]);

  // Smooth-scroll to a target sentence and mark it if ?sentence=<id> is present
  React.useEffect(() => {
    const params = new URLSearchParams(location.search);
    const sid = params.get('sentence');
    if (!sid) return;
    // Ensure content is rendered
    const container = contentRef.current;
    if (!container) return;
    let sentenceEl = container.querySelector(`[data-sentence-id="${sid}"]`) as HTMLElement | null;
    if (!sentenceEl) {
      // Small retry in case DOM isn't painted yet
      setTimeout(() => {
        const retryEl = container.querySelector(`[data-sentence-id="${sid}"]`) as HTMLElement | null;
        if (!retryEl) return;
        runScrollAndHighlight(retryEl);
      }, 150);
      return;
    }
    runScrollAndHighlight(sentenceEl);
    // Remove the query param to avoid re-highlighting on re-render
    try {
      const url = new URL(window.location.href);
      url.searchParams.delete('sentence');
      window.history.replaceState({}, '', url.pathname + url.search);
    } catch {}
  }, [location.search, processedSentences]);

  const runScrollAndHighlight = (sentenceEl: HTMLElement) => {
    // Target the whole paragraph container for a wide framed effect
    const paragraphEl = (sentenceEl.closest('[data-paragraph-id]') as HTMLElement) || sentenceEl;

    // Calculate navbar offset (like Quran page)
    let offsetTop = 0;
    const mainNavbar = document.getElementById('page-layout-navbar');
    if (mainNavbar) offsetTop += mainNavbar.offsetHeight;
    const secondRow = document.getElementById('page-layout-second-row');
    if (secondRow) offsetTop += secondRow.offsetHeight;
    if (offsetTop === 0) offsetTop = 90;
    const buffer = 20;

    // Smooth scroll with offset
    try {
      const rect = paragraphEl.getBoundingClientRect();
      const top = window.scrollY + rect.top - (offsetTop + buffer) + rect.height / 2 - window.innerHeight / 2;
      window.scrollTo({ top, behavior: 'smooth' });
    } catch {
      paragraphEl.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
    }

    // Paragraph-wide inner overlay (no outer ring), with cleanup
    const initialBg = paragraphEl.style.backgroundColor;
    const initialRadius = paragraphEl.style.borderRadius;
    const initialTransition = paragraphEl.style.transition;

    const cs = getComputedStyle(paragraphEl);
    const color = cs.color;
    let bg = 'rgba(0,0,0,0.08)';
    const m = color.match(/rgba?\(([^)]+)\)/);
    if (m) {
      const parts = m[1].split(',').map(v => parseFloat(v.trim()));
      const [r, g, b] = parts as any;
      bg = `rgba(${r|0}, ${g|0}, ${b|0}, 0.10)`;
    }

    paragraphEl.style.transition = 'background-color 700ms ease-out, border-radius 240ms';
    paragraphEl.style.borderRadius = '18px';
    paragraphEl.style.backgroundColor = bg;

    // Fade out
    setTimeout(() => {
      paragraphEl.style.transition = 'background-color 900ms ease-in';
      paragraphEl.style.backgroundColor = 'transparent';
      // Cleanup after animation
      setTimeout(() => {
        paragraphEl.style.borderRadius = initialRadius;
        paragraphEl.style.backgroundColor = initialBg;
        paragraphEl.style.transition = initialTransition;
      }, 900);
    }, 900);
  };

  if (isLoadingContent) return <LoadingState message="İçerik yükleniyor..." />;
  if (error) return <EnhancedErrorState error={error} onRetry={() => window.location.reload()} />;
  if (!sectionData || !sectionData.structure) return <LoadingState message="Bölüm bilgisi bulunamadı..." />;

  const { structure, content, title: sectionTitle, metadata } = sectionData;

  // Tek section var mı kontrol et
  const hasSingleSection = structure.sections.length === 1;

  const breadcrumbContent = (
    <div className="w-full overflow-x-auto whitespace-nowrap text-center py-1">
      <div className="inline-flex items-center justify-center min-w-max">
        <button
          onClick={() => openNavigationSheet('books')}
          className="flex items-center gap-0.5 px-2 py-1 rounded-lg"
          title={metadata?.title || 'Kitap Seç'}
        >
          <span className="text-sm font-medium">{metadata?.title || 'Kitap'}</span>
          <ChevronDown size={14} className="opacity-80" />
        </button>
        {!hasSingleSection && (
          <>
            <span className="mx-1 text-sm opacity-50">/</span>
            <button
              onClick={() => openNavigationSheet('section')}
              className="flex items-center gap-0.5 px-2 py-1 rounded-lg"
              title={sectionTitle || 'Bölüm Seç'}
            >
              <span className="text-sm font-medium">{sectionTitle || 'Bölüm'}</span>
              <ChevronDown size={14} className="ml-1 opacity-80" />
            </button>
          </>
        )}
      </div>
    </div>
  );

  return (
    <PageLayout
      navbarCenterContent={isMobile ? null : breadcrumbContent}
      secondRowContent={isMobile ? breadcrumbContent : null}
      navbarTwoRows={isMobile}
    >
      <NavigationSheet
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        navigationType={navigationType}
        onChangeNavigationType={setNavigationType}
        currentBookId={bookId || ''}
        selectedBookId={selectedBookId}
        books={books}
        sectionSearch={sectionSearch}
        onChangeSectionSearch={setSectionSearch}
        bookSearch={bookSearch}
        onChangeBookSearch={setBookSearch}
        currentSectionId={sectionId}
        filteredSections={filteredSections}
        onSelectSection={(sectionId) => handleNavSheetNavigation(sectionId)}
        onSelectBook={(bookId, navigateToPage = true) => {
          if (navigateToPage) {
            navigate(`/risale/${bookId}`);
            setIsOpen(false);
          } else {
            switchBookInSheet(bookId);
          }
        }}
        isLoadingSections={isLoadingSections}
        hasSingleSection={hasSingleSection}
      />
      <main className="risale-read-page max-w-3xl mx-auto px-4 py-8 space-y-6">
        <RisaleContent ref={contentRef} sentences={processedSentences} />
        <RisaleFootnotes
          footnotes={content.footnotes || []}
          isOpen={isFootnotesOpen}
          setIsOpen={setIsFootnotesOpen}
        />
        <RisaleDictionary
          dictionary={content.dictionary || []}
          isOpen={isDictionaryOpen}
          setIsOpen={setIsDictionaryOpen}
        />
        <RisaleNavigation
          prevSection={structure.prevSection as RisaleSectionDef || null}
          nextSection={structure.nextSection as RisaleSectionDef || null}
          navigateToSection={(id) => {
              if (bookId) navigate(`/risale/${bookId}/${id}`);
          }}
        />
      </main>
      {isTooltipVisible && tooltipPosition && (
        <Tooltip
          content={tooltipContent}
          position={tooltipPosition}
          isVisible={isTooltipVisible}
          onClose={hideTooltip}
        />
      )}
    </PageLayout>
  );
};

export default ReadPage;
