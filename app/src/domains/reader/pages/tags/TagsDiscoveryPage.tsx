import React, { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { PageLayout, EmptyState, LoadingState, SearchBar } from '@shared/components';
import { usePopularTags, useTagIndex, useTagSearch } from '@domains/reader/hooks/tags';
import { TagBadge } from '@domains/reader/components/tags/TagBadge';

const TagsDiscoveryPage: React.FC = () => {
  const navigate = useNavigate();
  const { popularTags, loading: loadingPopular } = usePopularTags(24);
  const { tagIndex, loading: loadingIndex } = useTagIndex();
  const { searchTags } = useTagSearch();
  const [query, setQuery] = useState('');
  const [visibleCount, setVisibleCount] = useState(200);

  const allTags = useMemo(() => {
    return Object.entries(tagIndex).map(([id, name]) => ({ id: Number(id), name }));
  }, [tagIndex]);

  const results = useMemo(() => {
    if (!query.trim()) return [] as Array<{ id: number; name: string }>;
    return searchTags(query);
  }, [query, searchTags]);

  const isLoading = loadingPopular || loadingIndex;
  const showSearchResults = query.trim().length > 0;

  const handleTagClick = (id: number) => navigate(`/tags/${id}`);

  return (
    <PageLayout onBackClick={() => navigate(-1)} title="Etiketler">
      <main className="mx-auto px-4 py-6 max-w-5xl">
        <div className="max-w-xl mx-auto mb-6">
          <SearchBar
            value={query}
            onChange={setQuery}
            placeholder="Etiket ara..."
          />
        </div>

        {isLoading && (
          <div className="flex justify-center py-12">
            <LoadingState message="Etiketler yükleniyor..." />
          </div>
        )}

        {!isLoading && (
          <div className="space-y-8">
            {showSearchResults ? (
              <section>
                <h2 className="text-sm font-semibold mb-3" style={{ color: 'var(--text-color)' }}>
                  Arama Sonuçları ({results.length})
                </h2>
                {results.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {results.map(t => (
                      <TagBadge key={t.id} id={t.id} name={t.name} size="md" onClick={() => handleTagClick(t.id)} />
                    ))}
                  </div>
                ) : (
                  <EmptyState message="Sonuç bulunamadı" />
                )}
              </section>
            ) : (
              <>
                <section>
                  <h2 className="text-sm font-semibold mb-3" style={{ color: 'var(--text-color)' }}>
                    Popüler Etiketler
                  </h2>
                  {popularTags.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {popularTags.map(t => (
                        <TagBadge key={t.id} id={t.id} name={t.name} size="md" onClick={() => handleTagClick(t.id)} />
                      ))}
                    </div>
                  ) : (
                    <EmptyState message="Popüler etiket bulunamadı" />
                  )}
                </section>

                <section>
                  <h2 className="text-sm font-semibold mb-3" style={{ color: 'var(--text-color)' }}>
                    Tüm Etiketler ({allTags.length})
                  </h2>
                  {allTags.length > 0 ? (
                    <>
                      <div className="flex flex-wrap gap-2">
                        {allTags.slice(0, visibleCount).map(t => (
                          <TagBadge key={t.id} id={t.id} name={t.name} onClick={() => handleTagClick(t.id)} />
                        ))}
                      </div>
                      {visibleCount < allTags.length && (
                        <div className="flex items-center justify-center mt-4 gap-2">
                          <button
                            className="px-4 py-2 rounded-md text-sm border"
                            style={{
                              backgroundColor: 'color-mix(in srgb, var(--text-color) 6%, transparent)',
                              borderColor: 'color-mix(in srgb, var(--text-color) 14%, transparent)',
                              color: 'var(--text-color)'
                            }}
                            onClick={() => setVisibleCount(c => Math.min(c + 200, allTags.length))}
                          >
                            Daha fazla yükle
                          </button>
                          <button
                            className="px-3 py-2 rounded-md text-sm"
                            style={{ color: 'var(--text-color)' }}
                            onClick={() => setVisibleCount(allTags.length)}
                          >
                            Hepsini göster
                          </button>
                        </div>
                      )}
                    </>
                  ) : (
                    <EmptyState message="Etiket bulunamadı" />
                  )}
                </section>
              </>
            )}
          </div>
        )}
      </main>
    </PageLayout>
  );
};

export default TagsDiscoveryPage;
