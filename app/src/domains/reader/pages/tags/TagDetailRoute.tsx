import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TagDetailPage } from '@domains/reader/components/tags/TagDetailPage';

const TagDetailRoute: React.FC = () => {
  const navigate = useNavigate();
  const params = useParams();
  const tagId = Number(params.tagId);

  if (!tagId || Number.isNaN(tagId)) {
    navigate('/tags', { replace: true });
    return null;
  }

  return (
    <TagDetailPage
      tagId={tagId}
      onBack={() => navigate(-1)}
      onSentenceClick={(sentence) => {
        // Navigate to the sentence context like in overlay
        const parts = sentence.id?.split('_') || [];
        if (parts.length >= 2) {
          const [bookId, sectionPart] = parts;
          const fullSectionId = `${bookId}_${sectionPart}`;
          navigate(`/risale/${bookId}/${fullSectionId}?sentence=${encodeURIComponent(sentence.id)}`);
        }
      }}
    />
  );
};

export default TagDetailRoute;
