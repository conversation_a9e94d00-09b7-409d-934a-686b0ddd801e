import { FC, useEffect, useMemo, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  PageLayout,
  LoadingState,
  ContentCard,
  EmptyState,
  SearchBar,
} from '@shared/components';
import { useLibraryStore } from '@domains/library/store/librarystore';
import { Surah } from '@domains/reader/models/types';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';
import { useScrollToTop } from '@shared/hooks/useScrollToTop';
import { CONTAINER_SIZES, SPACING, GRID_LAYOUTS } from '@reader/constants/layout';

const TafsirChapterSelectionPage: FC = () => {
  const { bookId } = useParams<{ bookId: string }>();
  const navigate = useNavigate();
  const books = useLibraryStore(state => state.books);
  const fetchBooks = useLibraryStore(state => state.fetchBooks);
  const [surahs, setSurahs] = useState<Surah[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useScrollToTop([bookId]);

  useEffect(() => {
    fetchBooks();
  }, [fetchBooks]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const currentBook = useMemo(() => {
    return books.find(b => b.id.toString() === bookId);
  }, [bookId, books]);

  useEffect(() => {
    const fetchSurahs = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/data/quran/surahs.json');
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        const surahList: Surah[] = await response.json();
        setSurahs(surahList);
      } catch (error) {
        console.error('Error fetching surahs:', error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchSurahs();
  }, []);

  const filteredSurahs = useMemo(() => {
    return filterByNormalizedQuery(surahs, searchTerm, (surah) => [
      surah.name,
      surah.arabic_name,
      surah.id.toString(),
    ]);
  }, [surahs, searchTerm]);

  if (isLoading) {
    return (
      <PageLayout showBackButton={true} title="Yükleniyor...">
        <LoadingState message="Sureler yükleniyor..." />
      </PageLayout>
    );
  }

  if (!currentBook) {
    return (
      <PageLayout showBackButton={true} title="Tafsir">
        <LoadingState message="Kitap bilgisi yükleniyor..." />
      </PageLayout>
    );
  }

  return (
    <PageLayout showBackButton={true} title={currentBook?.title || 'Tafsir'}>
      <div className={`${CONTAINER_SIZES.default} mx-auto ${SPACING.container}`}>
        <SearchBar
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Sure ara..."
          className="my-4"
        />
        {filteredSurahs.length > 0 ? (
          <div className={`grid ${GRID_LAYOUTS.chapters} ${SPACING.card}`}>
            {filteredSurahs.map(surah => (
              <ContentCard
                key={surah.id}
                title={`${surah.id}. ${surah.name}`}
                subtitle={surah.arabic_name}
                onClick={() => navigate(`/tafsir/${bookId}/${surah.id}`)}
              />
            ))}
          </div>
        ) : (
          <EmptyState message="Sure bulunamadı." />
        )}
      </div>
    </PageLayout>
  );
};

export default TafsirChapterSelectionPage;
