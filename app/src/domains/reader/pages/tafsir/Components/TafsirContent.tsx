import { forwardRef, useMemo } from 'react';
import { ProcessedTafsirSentence } from '@reader/hooks/tafsir/useTafsirProcess';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface TafsirContentProps {
  sentences: ProcessedTafsirSentence[];
}

const TafsirContent = forwardRef<HTMLDivElement, TafsirContentProps>(({ sentences }, ref) => {
  const infoOverlayBgColor = useAutoOverlay(9, 'var(--bg-color)');
  const labelOverlayBgColor = useAutoOverlay(11, 'var(--bg-color)');

  const sentenceGroups = useMemo(() => {
    if (!sentences) return [];
    const groups: { type: string; sentences: ProcessedTafsirSentence[] }[] = [];
    let i = 0;
    while (i < sentences.length) {
      const currentSentence = sentences[i];
      const currentType = currentSentence.type || 'normal';

      if (currentType === 'alert-info') {
        const infoGroup = { type: 'alert-info', sentences: [currentSentence] };
        let j = i + 1;
        while (j < sentences.length && sentences[j].type === 'alert-info') {
          infoGroup.sentences.push(sentences[j]);
          j++;
        }
        groups.push(infoGroup);
        i = j;
      } else {
        groups.push({ type: currentType, sentences: [currentSentence] });
        i++;
      }
    }
    return groups;
  }, [sentences]);

  return (
    <div ref={ref} className="tafsir-content space-y-4">
      {sentenceGroups.map((group, groupIndex) => {
        const key = `${group.type}-${groupIndex}`;

        if (group.type === 'alert-info') {
          return (
            <div
              key={key}
              className="p-4 rounded-lg space-y-4"
              style={{ backgroundColor: infoOverlayBgColor }}
            >
              {group.sentences.map((sentence) => (
                <p
                  key={sentence.id}
                  id={`sentence-${sentence.id}`}
                  data-verse-no={sentence.verseNumber}
                  className="text-2xl font-arabic leading-relaxed text-right"
                  dir="rtl"
                >
                  {sentence.processedText}
                </p>
              ))}
            </div>
          );
        }

        if (group.type === 'alert-label') {
          const sentence = group.sentences[0];
          return (
            <div
              key={key}
              className="p-4 rounded-lg"
              style={{ backgroundColor: labelOverlayBgColor }}
            >
              <p
                id={`sentence-${sentence.id}`}
                data-verse-no={sentence.verseNumber}
                className="text-2xl font-arabic leading-relaxed text-center"
                dir="rtl"
              >
                {sentence.processedText}
              </p>
            </div>
          );
        }

        const sentence = group.sentences[0];
        return (
          <p
            key={sentence.id}
            id={`sentence-${sentence.id}`}
            data-verse-no={sentence.verseNumber}
            className="text-2xl font-arabic leading-relaxed text-right"
            dir="rtl"
          >
            {sentence.processedText}
          </p>
        );
      })}
    </div>
  );
});

TafsirContent.displayName = 'TafsirTextContent';

export default TafsirContent;
