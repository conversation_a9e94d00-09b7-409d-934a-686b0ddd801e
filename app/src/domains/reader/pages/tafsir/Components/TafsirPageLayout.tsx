import { PageLayout } from '@shared/components';
import { ReactNode } from 'react';

interface TafsirPageLayoutProps {
  children: ReactNode;
  navbarCenterContent?: ReactNode;
  secondRowContent?: ReactNode;
  navbarTwoRows?: boolean;
}

export const TafsirPageLayout: React.FC<TafsirPageLayoutProps> = ({
  children,
  ...props
}) => {
  return (
    <PageLayout {...props}>{children}</PageLayout>
  );
};
