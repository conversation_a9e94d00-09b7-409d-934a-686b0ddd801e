import { X, ChevronRight, ChevronLeft, Loader2, Search } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { TafsirSurahDef, Book, Surah } from '@reader/models/types';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';

interface NavigationSheetProps {
  isOpen: boolean;
  onClose: () => void;
  navigationType: 'surah' | 'books' | 'verse';
  onChangeNavigationType: (type: 'surah' | 'books' | 'verse') => void;
  showOnlyBooksTab?: boolean; // Sadece kitap sekmesini göster
  currentBookId: string;
  selectedBookId: string | null;
  books: Book[];
  surahSearch: string;
  onChangeSurahSearch: (search: string) => void;
  bookSearch: string;
  onChangeBookSearch: (search: string) => void;
  verseSearch: string;
  onChangeVerseSearch: (search: string) => void;
  currentSurahId?: string;
  filteredSurahs: TafsirSurahDef[] | Surah[];
  onSelectSurah?: (surahId: string, navigateToPage?: boolean) => void;
  onSelectVerse: (verseNumber: number) => void;
  onSelectBook: (bookId: number, navigateToPage?: boolean) => void;
  isLoadingSurahs: boolean;
  hasSingleSurah?: boolean;
  isRtl?: boolean;
}

const NavigationSheet = ({
  isOpen,
  onClose,
  navigationType,
  onChangeNavigationType,
  showOnlyBooksTab = false,
  currentBookId,
  selectedBookId,
  books,
  surahSearch,
  onChangeSurahSearch,
  bookSearch,
  onChangeBookSearch,
  verseSearch,
  onChangeVerseSearch,
  currentSurahId,
  filteredSurahs,
  onSelectSurah,
  onSelectVerse,
  onSelectBook,
  isLoadingSurahs,
  hasSingleSurah = false,
  isRtl = false,
}: NavigationSheetProps) => {
  const navBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const toolbarBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');

  if (!isOpen) return null;


  const filteredBooks = filterByNormalizedQuery(books, bookSearch, (book) => [
    book.title,
    book.author || ''
  ]);

  const publicAndFilteredBooks = filteredBooks.filter(book => (book as any).is_available);

  const selectBookAndShowSurahs = (bookId: number) => {
    onSelectBook(bookId, false);
    onChangeNavigationType('surah');
  };

  const selectSurahAndShowVerses = (surahId: string) => {
    // Update current surah without navigation
    if (onSelectSurah) {
      onSelectSurah(surahId, false);
    }
    onChangeNavigationType('verse');
  };
  
  const displayedBookId = selectedBookId || currentBookId;
  const currentBook = books.find(b => b.id.toString() === displayedBookId);
  const currentSurah = filteredSurahs.find(s => s.id === currentSurahId);

  // Calculate verses for current surah with search filtering
  // Use regular calculation instead of useMemo to avoid hook order issues
  const currentSurahVerses = (() => {
    // Always return an array, even if currentSurah is null
    if (!currentSurah) {
      return currentSurahId ? [1] : []; // Return [1] if we have surahId but no currentSurah data yet
    }

    const verseCount = currentSurah.verse_count || 1;
    const verseList = Array.from({ length: verseCount }, (_, i) => i + 1);

    // Add introduction if exists (tafsir books may have introductions)
    const hasIntro = currentSurahId && parseInt(currentSurahId) <= 5; // Early surahs often have introductions
    if (hasIntro) {
      verseList.unshift(0); // Add introduction as verse 0
    }

    // Apply search filter
    const searchLower = verseSearch.toLowerCase();
    if (!searchLower) {
      return verseList;
    }

    return verseList.filter(v => String(v).includes(searchLower) || (v === 0 && 'giriş'.includes(searchLower)));
  })();

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        style={{ opacity: isOpen ? 1 : 0 }}
        onClick={onClose}
      />
      
      {/* Sheet */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[390px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20"
        style={{ 
          backgroundColor: navBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15" />
        
        {/* Segment Control with Close Button */}
        <div className="mx-4 mt-4 flex items-center justify-between">
          <div className="flex-1 flex p-1 rounded-lg" style={{ backgroundColor: toolbarBgColor }}>
            {showOnlyBooksTab ? (
              // Sadece kitap sekmesi gösterilsin
              <button
                onClick={() => onChangeNavigationType('books')}
                className={`w-full flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${navigationType === 'books' ? 'shadow' : ''}`}
                style={{
                  backgroundColor: navigationType === 'books' ? 'var(--bg-color)' : 'transparent',
                  color: 'var(--text-color)'
                }}
              >
                Kitap
              </button>
            ) : (
              // Normal görünüm - tüm sekmeler
              <>
                <button
                  onClick={() => onChangeNavigationType('books')}
                  className={`flex-1 flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${navigationType === 'books' ? 'shadow' : ''}`}
                  style={{
                    backgroundColor: navigationType === 'books' ? 'var(--bg-color)' : 'transparent',
                    color: 'var(--text-color)'
                  }}
                >
                  Kitap
                </button>
                {!hasSingleSurah && (
                  <button
                    onClick={() => onChangeNavigationType('surah')}
                    className={`flex-1 flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${navigationType === 'surah' ? 'shadow' : ''}`}
                    style={{
                      backgroundColor: navigationType === 'surah' ? 'var(--bg-color)' : 'transparent',
                      color: 'var(--text-color)'
                    }}
                  >
                    Sure
                  </button>
                )}
                <button
                  onClick={() => onChangeNavigationType('verse')}
                  className={`flex-1 flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${navigationType === 'verse' ? 'shadow' : ''}`}
                  style={{
                    backgroundColor: navigationType === 'verse' ? 'var(--bg-color)' : 'transparent',
                    color: 'var(--text-color)'
                  }}
                >
                  Ayet
                </button>
              </>
            )}
          </div>
          <button
            onClick={onClose}
            className={`w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)] ${isRtl ? 'mr-2' : 'ml-2'}`}
          >
            <X size={18} />
          </button>
        </div>

        {/* Content */}
        <div className="px-4 pt-3 pb-1 overflow-y-auto max-h-[calc(80vh-4rem)] md:max-h-[calc(70vh-6rem)] mt-2">
          {(navigationType === 'books' || showOnlyBooksTab) && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={bookSearch}
                  onChange={(e) => onChangeBookSearch(e.target.value)}
                  placeholder="Kitap adı ara..."
                  className={`w-full px-4 py-2.5 rounded-lg text-sm text-[var(--text-color)]/90 placeholder:text-[var(--text-color)]/30 focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors duration-200 ${isRtl ? 'pr-9' : 'pl-9'}`}
                  style={{ 
                    backgroundColor: listAreaBgColor
                  }}
                />
                <Search
                  size={16}
                  className={`absolute top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50 ${isRtl ? 'right-3' : 'left-3'}`}
                />
              </div>
              <div 
                className="space-y-1 p-2 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                {publicAndFilteredBooks.map((book) => {
                  const isCurrentBook = book.id.toString() === currentBookId;
                    
                  return (
                    <div
                      key={book.id}
                      onClick={() => onSelectBook(book.id, true)}
                      className={`w-full px-3 py-2.5 text-left rounded-lg text-sm flex items-center justify-between relative transition-colors duration-200 text-[var(--text-color)] cursor-pointer ${
                        isCurrentBook ? 'bg-[var(--bg-color)] shadow' : 'hover:bg-[var(--text-color)]/5'
                      }`}
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <span 
                          className={`truncate ${isCurrentBook ? 'font-bold' : 'font-medium'}`}
                        >
                          {book.title}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 pl-2">
                        <button
                          type="button"
                          className="p-1.5 rounded-full hover:bg-[var(--text-color)]/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            selectBookAndShowSurahs(book.id);
                          }}
                          title={`${book.title} Sureleri`}
                        >
                          {isRtl ? <ChevronLeft size={14} style={{ color: 'var(--text-color)' }} /> : <ChevronRight size={14} style={{ color: 'var(--text-color)' }} />}
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {navigationType === 'surah' && !hasSingleSurah && !showOnlyBooksTab && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={surahSearch}
                  onChange={(e) => onChangeSurahSearch(e.target.value)}
                  placeholder="Sure adı veya numarası ara..."
                  className={`w-full px-4 py-2.5 rounded-lg text-sm text-[var(--text-color)]/90 placeholder:text-[var(--text-color)]/30 focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors duration-200 ${isRtl ? 'pr-9' : 'pl-9'}`}
                  style={{ 
                    backgroundColor: listAreaBgColor
                  }}
                />
                <Search
                  size={16}
                  className={`absolute top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50 ${isRtl ? 'right-3' : 'left-3'}`}
                />
              </div>
              
              <div className="mb-2 py-2 text-sm font-medium">
                <span className="opacity-60">Kitap: </span>
                <span className="font-bold">{currentBook?.title || `Kitap ${currentBookId}`}</span>
              </div>

              <div 
                className="space-y-1 p-2 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                {isLoadingSurahs ? (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 size={24} className="animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  filteredSurahs.map(surah => {
                    const isCurrent = surah.id === currentSurahId;

                    return (
                      <div
                        key={surah.id}
                        onClick={() => {
                          if (onSelectSurah) {
                            onSelectSurah(surah.id.toString());
                          }
                          onClose();
                        }}
                        className={`w-full px-3 py-2.5 text-left rounded-lg text-sm flex items-center justify-between relative transition-colors duration-200 text-[var(--text-color)] cursor-pointer ${
                          isCurrent ? 'bg-[var(--bg-color)] shadow' : 'hover:bg-[var(--text-color)]/5'
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <span className="w-6 text-right opacity-80">{surah.id}.</span>
                          <span 
                            className={`truncate ${isCurrent ? 'font-bold' : 'font-medium'}`}
                          >
                            {surah.name}
                          </span>
                          <span className="text-xs opacity-60">
                            {capitalizeFirstLetter(surah.revelation_place || '')}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-xs opacity-60">{surah.verse_count} Ayet</span>
                          <button
                            type="button"
                            className="p-1.5 rounded-full hover:bg-[var(--text-color)]/10"
                            onClick={(e) => {
                              e.stopPropagation();
                              selectSurahAndShowVerses(surah.id.toString());
                            }}
                            title={`${surah.name} Suresi Ayetleri`}
                          >
                            {isRtl ? <ChevronLeft size={14} style={{ color: 'var(--text-color)' }} /> : <ChevronRight size={14} style={{ color: 'var(--text-color)' }} />}
                          </button>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          )}

          {navigationType === 'verse' && !showOnlyBooksTab && (
            <div className="space-y-2.5">
              <div className="relative">
                <input
                  type="text"
                  value={verseSearch}
                  onChange={(e) => onChangeVerseSearch(e.target.value)}
                  placeholder="Ayet numarası ara..."
                  className={`w-full px-4 py-2.5 rounded-lg text-sm text-[var(--text-color)]/90 placeholder:text-[var(--text-color)]/30 focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors duration-200 ${isRtl ? 'pr-9' : 'pl-9'}`}
                  style={{ 
                    backgroundColor: listAreaBgColor
                  }}
                />
                <Search
                  size={16}
                  className={`absolute top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50 ${isRtl ? 'right-3' : 'left-3'}`}
                />
              </div>

              <div className="mb-2 py-2 text-sm font-medium">
                <span className="opacity-60">Sure: </span>
                <span className="font-bold">{currentSurah?.name || `Sure ${currentSurahId}`}</span>
              </div>

              <div
                className="grid grid-cols-5 gap-2 p-2 rounded-xl"
                style={{ backgroundColor: listAreaBgColor }}
              >
                {currentSurahVerses.map((verseNumber: number) => (
                  <button
                    key={verseNumber}
                    onClick={() => {
                      onSelectVerse(verseNumber);
                      onClose();
                    }}
                    className="aspect-square flex items-center justify-center rounded-lg text-sm bg-[var(--bg-color)] shadow hover:bg-[var(--text-color)]/5 transition-colors duration-200"
                  >
                    {verseNumber}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

const capitalizeFirstLetter = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export default NavigationSheet;
