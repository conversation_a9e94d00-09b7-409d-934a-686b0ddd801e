import React from 'react';
import { ProcessedTafsirFootnote } from '@reader/hooks/tafsir/useTafsirProcess';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { ChevronDown, ChevronUp, FileText } from 'lucide-react';

interface TafsirFootnotesProps {
  footnotes: ProcessedTafsirFootnote[];
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const TafsirFootnotes: React.FC<TafsirFootnotesProps> = ({ footnotes, isOpen, setIsOpen }) => {
  const textColor = useAutoOverlay(95, 'var(--text-color)');
  const bgColor = useAutoOverlay(8, 'var(--bg-color)');
  const borderColor = useAutoOverlay(15, 'var(--bg-color)');

  if (footnotes.length === 0) {
    return null;
  }

  return (
    <div
      className="rounded-lg border shadow-sm hover:shadow-md transition-all duration-200"
      style={{
        backgroundColor: bgColor,
        borderColor: borderColor,
        borderWidth: '1px',
        borderStyle: 'solid'
      }}
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-6 py-4 text-left hover:opacity-80 transition-all duration-200"
        style={{ color: textColor }}
      >
        <div className="flex items-center gap-3">
          <FileText size={18} style={{ color: 'var(--color-primary)' }} />
          <span className="font-semibold text-base">Tefsir Dipnotları</span>
          <span
            className="text-sm px-2 py-1 rounded-full font-medium"
            style={{
              backgroundColor: 'var(--color-primary)',
              color: 'white'
            }}
          >
            {footnotes.length}
          </span>
        </div>
        <div className="flex items-center">
          {isOpen ? (
            <ChevronUp size={18} style={{ color: textColor }} />
          ) : (
            <ChevronDown size={18} style={{ color: textColor }} />
          )}
        </div>
      </button>

      {isOpen && (
        <div
          className="border-t px-6 py-4"
          style={{ borderColor: borderColor }}
        >
          <div className="space-y-4 max-h-64 overflow-y-auto">
            {footnotes.map((footnote, index) => (
              <div
                key={footnote.id}
                className="flex gap-4 p-3 rounded-md hover:bg-opacity-50 transition-colors duration-200"
                style={{ backgroundColor: 'color-mix(in srgb, var(--bg-color) 95%, var(--text-color) 5%)' }}
              >
                <div className="flex-shrink-0">
                  <span
                    className="inline-flex items-center justify-center w-6 h-6 text-xs font-bold rounded-full shadow-sm"
                    style={{
                      backgroundColor: 'var(--color-primary)',
                      color: 'white'
                    }}
                  >
                    {index + 1}
                  </span>
                </div>
                <div className="flex-1">
                  <p
                    className="text-sm leading-relaxed"
                    style={{ color: textColor }}
                  >
                    {footnote.text}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TafsirFootnotes;
