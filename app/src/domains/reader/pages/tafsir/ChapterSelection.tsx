import { useState, useMemo, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  PageLayout,
  SearchBar,
  ContentCard,
  LoadingState,
  EmptyState
} from '../../../../shared/components';
import { useTafsirBook } from '@reader/hooks/tafsir/useTafsirBook';
import { TafsirSurahDef } from '@reader/models/types';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { usePageLayout, useErrorHandling } from '@reader/hooks';
import { CONTAINER_SIZES, SPACING, GRID_LAYOUTS } from '@reader/constants/layout';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';

const ChapterSelectionPage = () => {
  const { bookId } = useParams<{ bookId: string }>();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const isMobile = useIsMobile();
  const { renderErrorState } = useErrorHandling();

  // Load data using the hook
  const { data: bookData, loading, error } = useTafsirBook(bookId);

  // Page layout config - use correct title after book data is loaded
  const pageTitle = bookData?.metadata?.title || (bookId ? `Tafsir (${bookId})` : 'Tafsir');
  const pageLayoutConfig = usePageLayout({
    title: pageTitle,
    isMobile
  });

  // Search filter (based on data from hook)
  const filteredSurahs = useMemo(() => {
    if (!bookData?.structure?.surahs) return [];
    return filterByNormalizedQuery(bookData.structure.surahs, searchTerm, (surah) => [
      surah.name,
      surah.arabic_name || ''
    ]);
  }, [bookData?.structure?.surahs, searchTerm]);

  // If there's only one surah, redirect directly to reading page
  useEffect(() => {
    if (bookData?.structure?.surahs && 
        bookData.structure.surahs.length === 1) {
      const singleSurah = bookData.structure.surahs[0];
      navigate(`/tafsir/${bookId}/${singleSurah.id}/0`, { replace: true });
    }
  }, [bookData, bookId, navigate]);

  // Yükleniyor durumu
  if (loading) {
    return (
      <PageLayout {...pageLayoutConfig}>
        <LoadingState message="Tefsir sureleri yükleniyor..." />
      </PageLayout>
    );
  }

  // Hata durumu
  if (error) {
    return (
      <PageLayout {...pageLayoutConfig}>
        {renderErrorState({ error }, 'Tefsir sure listesi')}
      </PageLayout>
    );
  }

  // Ana render
  return (
    <PageLayout {...pageLayoutConfig}>
      {/* SearchBar */}
      <div className={`mt-4 mb-4 ${CONTAINER_SIZES.default} mx-auto ${SPACING.container}`}>
        <SearchBar
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Sure ara..."
        />
      </div>

      <div className={`${CONTAINER_SIZES.default} mx-auto ${SPACING.container} ${SPACING.section}`}>
        {/* Sure kartları */}
        {filteredSurahs.length > 0 ? (
          <div className={`grid ${GRID_LAYOUTS.chapters} ${SPACING.card}`}>
            {filteredSurahs.map((surah: TafsirSurahDef) => (
              <div key={surah.id} className="h-full">
                <ContentCard
                  title={`${surah.id}. ${surah.name}`}
                  subtitle={surah.arabic_name}
                  metadata={
                    <div className="w-full flex justify-between">
                      <span>{surah.revelation_place}</span>
                      <span>{surah.verse_count} Ayet</span>
                    </div>
                  }
                  onClick={() => navigate(`/tafsir/${bookId}/${surah.id}/0`)}
                />
              </div>
            ))}
          </div>
        ) : (
          // Sure bulunamadı veya arama sonucu yok
          <EmptyState message={searchTerm ? "Arama sonucu bulunamadı" : "Sure bulunamadı"} />
        )}
      </div>
    </PageLayout>
  );
};

export default ChapterSelectionPage;
