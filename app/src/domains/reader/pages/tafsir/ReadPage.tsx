import { useState, useRef, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { EnhancedErrorState, LoadingState, Tooltip, NavigationFooter } from '@shared/components';
import { useTafsirSurah, useTafsirProcess } from '@reader/hooks/tafsir';
import { useReaderInteractivity } from '@reader/hooks';
import { ChevronDown } from 'lucide-react';
import NavigationSheet from './Components/NavigationSheet';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import TafsirContent from './Components/TafsirContent';
import TafsirIntroduction from './Components/TafsirIntroduction';
import TafsirFootnotes from './Components/TafsirFootnotes';
import { useTafsirNavigationSheet } from '@reader/hooks/tafsir/useTafsirNavigationSheet';
import { TafsirPageLayout } from '@/domains/reader/pages/tafsir/Components/TafsirPageLayout';
import { TafsirSentence, TafsirVerse } from '@reader/models/types';

const ReadPage = () => {
  const { bookId, surahId, verseNumber: verseNumberParam } = useParams<{ bookId: string; surahId: string; verseNumber?: string }>();
  const navigate = useNavigate();
  const [isFootnotesOpen, setIsFootnotesOpen] = useState(false);
  const isMobile = useIsMobile();
  
  const contentRef = useRef<HTMLDivElement>(null);
  const actionMenuRef = useRef<HTMLDivElement>(null);

  const { data: surahData, loading: isLoadingContent, error } = useTafsirSurah(bookId, surahId);
  const [activeVerse, setActiveVerse] = useState<number>(1);
  const [navigationSurahId, setNavigationSurahId] = useState<string>(surahId || '');

  useEffect(() => {
    if (!surahData || isLoadingContent) return;

    if (verseNumberParam === 'last') {
      const lastVerse = surahData.content.verses.length;
      setActiveVerse(lastVerse);
      navigate(`/tefsir/${bookId}/${surahId}/${lastVerse}`, { replace: true });
    } else {
      const verseNum = parseInt(verseNumberParam || '1', 10);
      const hasIntroduction = (surahData.content.introduction?.length || 0) > 0;

      if (verseNum === 0 && !hasIntroduction) {
        // No introduction, redirect to verse 1
        setActiveVerse(1);
        navigate(`/tefsir/${bookId}/${surahId}/1`, { replace: true });
      } else {
        setActiveVerse(verseNum);
      }
    }
  }, [verseNumberParam, surahData, isLoadingContent, bookId, surahId, navigate]);

  const handleVerseChange = (newVerseNumber: number) => {
    const currentTotalVerseCount = surahData?.content.verses.length || 0;
    const hasIntroduction = (surahData?.content.introduction?.length || 0) > 0;
    const minVerse = hasIntroduction ? 0 : 1;

    if (newVerseNumber >= minVerse && newVerseNumber <= currentTotalVerseCount) {
      setActiveVerse(newVerseNumber);
      navigate(`/tefsir/${bookId}/${surahId}/${newVerseNumber}`, { replace: true });
    }
  };
  
  const {
    isOpen,
    setIsOpen,
    navigationType,
    setNavigationType,
    surahSearch,
    setSurahSearch,
    bookSearch,
    setBookSearch,
    verseSearch,
    setVerseSearch,
    handleSurahNavigation,
    handleVerseNavigation,
    switchBookInSheet,
    filteredSurahs,
    filteredVerses,
    openNavigationSheet,
    isLoadingSurahs,
    books,
    selectedBookId,
  } = useTafsirNavigationSheet({
    currentBookId: bookId,
    currentSurahId: navigationSurahId,
    initialBookTitle: surahData?.metadata?.title,
    totalVerseCount: surahData?.content.verses.length || 0,
    hasIntroduction: (surahData?.content.introduction?.length || 0) > 0,
  });


  const {
    isTooltipVisible,
    tooltipPosition,
    tooltipContent,
    hideTooltip,
  } = useReaderInteractivity({
    contentRef,
    actionMenuRef,
    sectionData: surahData as any,
  });

  const dataForProcessing: { sentences: TafsirSentence[] } | TafsirVerse | null = activeVerse === 0
    ? { sentences: surahData?.content.introduction || [] }
    : surahData?.content.verses.find(v => v.verse_no === activeVerse) || null;

  const { processedSentences } = useTafsirProcess(dataForProcessing);

  if (isLoadingContent) return <LoadingState message="İçerik yükleniyor..." />;
  if (error) return <EnhancedErrorState error={error} onRetry={() => window.location.reload()} />;
  if (!surahData || !surahData.structure) return <LoadingState message="Bölüm bilgisi bulunamadı..." />;

  const { structure, content, title: surahTitle, book } = surahData;

  const hasSingleSurah = structure.surahs.length === 1;

  const currentSurahIndex = structure.surahs.findIndex(s => String(s.id) === surahId);
  const prevSurah = currentSurahIndex > 0 ? structure.surahs[currentSurahIndex - 1] : undefined;
  const nextSurah = currentSurahIndex < structure.surahs.length - 1 ? structure.surahs[currentSurahIndex + 1] : undefined;

  const breadcrumbContent = (
    <div className="w-full overflow-x-auto whitespace-nowrap text-center py-1">
      <div className="inline-flex items-center justify-center min-w-max">
        <button
          onClick={() => openNavigationSheet('books')}
          className="flex items-center gap-0.5 px-2 py-1 rounded-lg hover:bg-[var(--text-color)]/5 transition-colors"
          title={book?.title || 'Kitap Seç'}
        >
          <span className="text-sm font-medium">{book?.title || 'Kitap'}</span>
          <ChevronDown size={14} className="opacity-80" />
        </button>
        {!hasSingleSurah && (
          <>
            <span className="mx-1 text-sm opacity-50">/</span>
            <button
              onClick={() => openNavigationSheet('surah')}
              className="flex items-center gap-0.5 px-2 py-1 rounded-lg hover:bg-[var(--text-color)]/5 transition-colors"
              title={surahTitle || 'Sure Seç'}
            >
              <span className="text-sm font-medium">{surahTitle || 'Sure'}</span>
              <ChevronDown size={14} className="ml-1 opacity-80" />
            </button>
          </>
        )}
        <span className="mx-1 text-sm opacity-50">/</span>
        <button
          onClick={() => openNavigationSheet('verse')}
          className="flex items-center gap-0.5 px-2 py-1 rounded-lg hover:bg-[var(--text-color)]/5 transition-colors"
          title="Ayet Seç"
        >
          <span className="text-sm font-medium">{activeVerse}</span>
          <ChevronDown size={14} className="ml-1 opacity-80" />
        </button>
      </div>
    </div>
  );

  return (
    <TafsirPageLayout
      navbarCenterContent={isMobile ? null : breadcrumbContent}
      secondRowContent={isMobile ? breadcrumbContent : null}
      navbarTwoRows={isMobile}
    >
      <NavigationSheet
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        navigationType={navigationType}
        onChangeNavigationType={setNavigationType}
        currentBookId={bookId || ''}
        selectedBookId={selectedBookId}
        books={books}
        surahSearch={surahSearch}
        onChangeSurahSearch={setSurahSearch}
        bookSearch={bookSearch}
        onChangeBookSearch={setBookSearch}
        verseSearch={verseSearch}
        onChangeVerseSearch={setVerseSearch}
        currentSurahId={navigationSurahId}
        filteredSurahs={filteredSurahs}
        filteredVerses={filteredVerses}
        onSelectSurah={(surahId, navigateToPage = true) => {
          if (navigateToPage) {
            handleSurahNavigation(surahId, true);
          } else {
            // Just update the navigation surah without navigation
            setNavigationSurahId(surahId);
          }
        }}
        onSelectVerse={(verseNumber) => handleVerseNavigation(verseNumber)}
        onSelectBook={(bookId, navigateToPage = true) => {
          if (navigateToPage) {
            navigate(`/tafsir/${bookId}`);
            setIsOpen(false);
          } else {
            switchBookInSheet(bookId);
          }
        }}
        isLoadingSurahs={isLoadingSurahs}
        hasSingleSurah={hasSingleSurah}
        isRtl={false}
      />
      <main className="tafsir-read-page max-w-3xl mx-auto px-4 py-8 space-y-6">
        {activeVerse === 0 ? (
          <TafsirIntroduction sentences={processedSentences} />
        ) : (
          <TafsirContent ref={contentRef} sentences={processedSentences} />
        )}
        <TafsirFootnotes
          footnotes={content.verses.flatMap(v => v.footnotes || []).filter(f => f && typeof f.number !== 'undefined' && f.content).map(f => ({ id: String(f.number), text: String(f.content) }))}
          isOpen={isFootnotesOpen}
          setIsOpen={setIsFootnotesOpen}
        />
        <NavigationFooter
          prevSection={
            activeVerse > (surahData?.content.introduction ? 0 : 1) ? {
              id: String(activeVerse - 1),
              title: activeVerse === 1 ? 'Giriş' : `Ayet ${activeVerse - 1}`,
              onClick: () => handleVerseChange(activeVerse - 1)
            } : prevSurah ? {
              id: String(prevSurah.id),
              title: prevSurah.name,
              onClick: () => navigate(`/tefsir/${bookId}/${prevSurah.id}/last`)
            } : {
              id: 'start-of-book',
              title: 'Başlangıç',
              onClick: () => {},
              disabled: true
            }
          }
          nextSection={
            activeVerse === 0 ? {
              id: '1',
              title: 'Ayet 1',
              onClick: () => handleVerseChange(1)
            } : activeVerse < content.verses.length ? {
              id: String(activeVerse + 1),
              title: `Ayet ${activeVerse + 1}`,
              onClick: () => handleVerseChange(activeVerse + 1)
            } : nextSurah ? {
              id: String(nextSurah.id),
              title: nextSurah.name,
              onClick: () => handleSurahNavigation(String(nextSurah.id))
            } : {
              id: 'end-of-book',
              title: 'Son',
              onClick: () => {},
              disabled: true
            }
          }
        />
        {!nextSurah && activeVerse === content.verses.length && (
          <div className="text-center mt-4">
            <button
              onClick={() => navigate('/tefsir')}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Kitaplığa Geri Dön
            </button>
          </div>
        )}
      </main>
      {isTooltipVisible && tooltipPosition && (
        <Tooltip
          content={tooltipContent}
          position={tooltipPosition}
          isVisible={isTooltipVisible}
          onClose={hideTooltip}
        />
      )}
    </TafsirPageLayout>
  );
};

export default ReadPage;
