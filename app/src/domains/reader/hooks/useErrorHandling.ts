import React, { ReactNode } from 'react';
import { EnhancedErrorState } from '@shared/components';

type ErrorType = 'network' | 'content' | 'auth' | 'permission' | 'general';

interface UseErrorHandlingProps {
  error: Error;
  onRetry?: () => void;
  showHomeButton?: boolean;
}

export const useErrorHandling = () => {
  const getErrorType = (error: Error): ErrorType => {
    if (error.message.includes('network') ||
        error.message.includes('fetch') ||
        error.message.includes('Failed to fetch')) {
      return 'network';
    } else if (error.message.includes('not found') ||
               error.message.includes('404') ||
               error.message.includes('parse')) {
      return 'content';
    }
    return 'general';
  };

  const getErrorMessage = (errorType: ErrorType, context: string): string => {
    switch (errorType) {
      case 'network':
        return `${context} yüklenirken bağlantı sorunu oluştu. Lütfen internet bağlantınızı kontrol edip tekrar deneyin.`;
      case 'content':
        return `${context} yüklenirken bir sorun oluştu. Tekrar denemek için aşağıdaki butona tıklayabilirsiniz.`;
      default:
        return `${context} yüklenirken bir sorun oluştu. Tekrar denemek için aşağıdaki butona tıklayabilirsiniz.`;
    }
  };

  const renderErrorState = ({ error, onRetry, showHomeButton = true }: UseErrorHandlingProps, context: string): ReactNode => {
    const errorType = getErrorType(error);
    const customMessage = getErrorMessage(errorType, context);

    return React.createElement(EnhancedErrorState, {
      error,
      errorType,
      onRetry: onRetry || (() => window.location.reload()),
      customMessage,
      showHomeButton
    });
  };

  return {
    getErrorType,
    getErrorMessage,
    renderErrorState
  };
};
