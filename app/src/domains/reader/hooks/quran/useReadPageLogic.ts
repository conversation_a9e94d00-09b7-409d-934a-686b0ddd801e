import { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { useSurahs } from './useSurahs';
import { useVerseLoader } from './useVerseLoader';
import { useContentMode, ContentMode } from './useContentMode';
import { useVerseScroll } from './useVerseScroll';
import { useVisibleVerse } from './useVisibleVerse';
import { Surah, CombinedVerseData } from '@reader/models/types';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { useReaderStore } from '@reader/store/readerstore';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';

function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
}

export const useReadPageLogic = () => {
  const { surahId: surahIdParam } = useParams<{ surahId: string }>();
  const location = useLocation();
  const navigate = useNavigate();

  // Zustand Store'dan state ve action'ları al
  const readingSettings = useReaderStore(state => state.readingSettings);
  const updateReadingSettings = useReaderStore(state => state.updateReadingSettings);
  const openVerseActionSheet = useReaderStore(state => state.openVerseActionSheet);
  const { isNavigationSheetOpen, navigationType } = useReaderStore(state => state.uiState);
  const openNavigationSheet = useReaderStore(state => state.openNavigationSheet);
  const closeNavigationSheet = useReaderStore(state => state.closeNavigationSheet);
  const setNavigationType = useReaderStore(state => state.setNavigationType);
  const selectedTranslators = readingSettings.selectedTranslators || [];

  // Diğer hook'lar
  const { data: surahs, loading: surahsLoading, error: surahsError } = useSurahs();
  const { isContentModeOpen, setIsContentModeOpen, changeContentMode } = useContentMode();
  const isMobile = useIsMobile();
  const { width: windowWidth } = useWindowSize();

  // URL'den contentMode'u senkron olarak belirle (race condition'ı önlemek için)
  const contentMode = useMemo(() => {
    const path = location.pathname;
    const modeMap: { [key: string]: ContentMode } = {
      '/kelime-mealli-kuran': '3',
      '/mealli-kuran': '2',
      '/kuran-meali': '4',
      '/kuran': '1',
    };
    const sortedRoutes = Object.keys(modeMap).sort((a, b) => b.length - a.length);
    for (const route of sortedRoutes) {
      if (path.includes(route)) {
        return modeMap[route];
      }
    }
    return '1'; // Default
  }, [location.pathname]);

  const surahIdNum = surahIdParam ? parseInt(surahIdParam, 10) : null;

  const currentSurahInfo = useMemo(() => {
    if (!surahs || !surahIdNum) return null;
    return surahs.find(s => s.id === surahIdNum);
  }, [surahs, surahIdNum]);

  const totalVerseCount = useMemo(() => currentSurahInfo?.verse_count || 0, [currentSurahInfo]);

  const {
    verses,
    loading: versesLoading,
    error: versesError,
    availableTranslators,
    translationsLoading
  } = useVerseLoader(surahIdParam, contentMode, selectedTranslators);

  const prevVersesRef = useRef<CombinedVerseData[] | null>(null);

  // Clear previous verses when surah changes to prevent flicker
  useEffect(() => {
    prevVersesRef.current = null;
  }, [surahIdParam]);

  useEffect(() => {
    if (verses && verses.length > 0) {
      prevVersesRef.current = verses;
    }
  }, [verses]);
  
  const displayVerses = verses || prevVersesRef.current || [];

  const [calculatedOffsetTop, setCalculatedOffsetTop] = useState(100);
  useEffect(() => {
    let totalHeight = 0;
    const mainNavbarElement = document.getElementById('page-layout-navbar');
    if (mainNavbarElement) totalHeight += mainNavbarElement.offsetHeight;
    if (isMobile) {
      const secondRowNavbarElement = document.getElementById('page-layout-second-row');
      if (secondRowNavbarElement) totalHeight += secondRowNavbarElement.offsetHeight;
    }
    if (totalHeight === 0) totalHeight = isMobile ? 120 : 80;
    const buffer = 20;
    setCalculatedOffsetTop(totalHeight + buffer);
  }, [isMobile, windowWidth, surahsLoading, currentSurahInfo?.id]);

  const showButtonTexts = useMemo(() => windowWidth > 1300, [windowWidth]);
  const highlightColor = useAutoOverlay(11, 'var(--bg-color)');
  const translatorTitleColor = useAutoOverlay(11, 'var(--text-color)');
  const footnoteColor = useAutoOverlay(11, 'var(--text-color)');
  const sectionLeftBorderColor = useAutoOverlay(11, 'var(--text-color)');
  const verseBorderColor = useAutoOverlay(15, 'var(--bg-color)');

  const [surahSearch, setSurahSearch] = useState('');
  const [verseSearch, setVerseSearch] = useState('');

  const filteredSurahs = useMemo(() => {
    if (!surahs) return [];
    return filterByNormalizedQuery(surahs, surahSearch, (surah) => [
      surah.name,
      surah.id.toString()
    ]);
  }, [surahs, surahSearch]);

  const scrollToVerse = useVerseScroll({
    currentSurahId: currentSurahInfo?.id ?? null,
    behavior: 'smooth',
    offsetCorrection: calculatedOffsetTop
  });

  const { currentVerse } = useVisibleVerse({
    totalVerses: totalVerseCount,
    currentSurahId: currentSurahInfo?.id ?? null,
    offsetTop: calculatedOffsetTop,
    verseIdPrefix: 'verse-',
  });

  const [isTranslationSheetOpen, setIsTranslationSheetOpen] = useState(false);
  const [translatorSearch, setTranslatorSearch] = useState('');

  const handleVerseMenuClick = useCallback((verseKey: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const surahId = currentSurahInfo?.id || surahIdNum || 1;
    const fullVerseKey = `${surahId}-${verseKey}`;
    // Tıklanan butonu (anchorEl) ikinci parametre olarak gönderiyoruz.
    openVerseActionSheet(fullVerseKey, event.currentTarget as HTMLElement);
  }, [currentSurahInfo?.id, surahIdNum, openVerseActionSheet]);

  const handleVerseNavigation = useCallback((direction: 'next' | 'prev') => {
    const newVerse = direction === 'next' ? (currentVerse || 0) + 1 : (currentVerse || 0) - 1;
    if (newVerse > 0 && newVerse <= totalVerseCount) {
      scrollToVerse(newVerse);
    }
  }, [currentVerse, totalVerseCount, scrollToVerse]);

  const activeVerses = useMemo(() => {
    const targetSurahId = currentSurahInfo?.id;
    if (!targetSurahId || !surahs) return [];
    const targetSurah = surahs.find((s: Surah) => s.id === targetSurahId);
    if (!targetSurah) return [];
    const totalVerses = targetSurah.verse_count;
    return Array.from({ length: totalVerses }, (_, i) => i + 1)
      .filter(verseNo => verseNo.toString().includes(verseSearch));
  }, [currentSurahInfo?.id, surahs, verseSearch]);
  
  const handleNavigation = useCallback((surahId: number, verseNo?: number) => {
    const baseUrl = location.pathname.split('/').slice(0, 2).join('/');
    if (verseNo !== undefined) {
      navigate(`${baseUrl}/${surahId}`, { state: { verse: verseNo } });
    } else {
      // verseNo belirtilmemişse, sayfanın en tepesine gitmesi için state ayarla
      navigate(`${baseUrl}/${surahId}`, { state: { scrollToTop: true } });
    }
    closeNavigationSheet();
  }, [location.pathname, navigate, closeNavigationSheet]);

  const handleTranslatorSelectionChange = useCallback((newSelection: string[]) => {
    updateReadingSettings(1, { selectedTranslators: newSelection });
  }, [updateReadingSettings]);

  const prevContentModeRef = useRef(contentMode);
  useEffect(() => {
    const previousContentMode = prevContentModeRef.current;
    if (contentMode !== previousContentMode && currentVerse && !versesLoading) {
      setTimeout(() => {
        scrollToVerse(currentVerse);
        setTimeout(() => scrollToVerse(currentVerse), 500);
      }, 650);
    }
    prevContentModeRef.current = contentMode;
  }, [contentMode, currentVerse, versesLoading, scrollToVerse]);

  useEffect(() => {
    const verseFromState = location.state?.verse;
    const scrollToTop = location.state?.scrollToTop;
    const params = new URLSearchParams(location.search);
    const verseFromUrl = params.get('verse');
    
    const verseToScroll = verseFromState || (verseFromUrl ? parseInt(verseFromUrl, 10) : null);

    if (scrollToTop) {
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'auto' });
        // State'i temizle ki sayfa yenilendiğinde tekrar aynı yere kaymasın
        navigate(location.pathname, { replace: true, state: {} });
      }, 100);
    } else if (verseToScroll && !versesLoading && verses && verses.length > 0) {
      // Geçerli ayet kontrolü ekle
      if (verseToScroll > 0 && verseToScroll <= totalVerseCount) {
        setTimeout(() => {
          scrollToVerse(verseToScroll);
          // State'i temizle ki sayfa yenilendiğinde tekrar aynı yere kaymasın
          if (verseFromState) {
            navigate(location.pathname, { replace: true, state: {} });
          }
        }, 500);
      }
    }
  }, [location.state, location.search, versesLoading, verses, scrollToVerse, contentMode, navigate, location.pathname, totalVerseCount]);

  const displayedSurahInfo = currentSurahInfo || (surahs ? surahs[0] : null);

  return {
    surahs,
    surahsLoading,
    surahsError,
    verses,
    versesLoading,
    versesError,
    displayVerses,
    currentSurahInfo,
    currentVerse,
    availableTranslators,
    translationsLoading,
    navFilteredSurahs: filteredSurahs,
    activeVerses,
    displayedSurahInfo,
    contentMode,
    isContentModeOpen,
    setIsContentModeOpen,
    selectedTranslators,
    isTranslationSheetOpen,
    setIsTranslationSheetOpen,
    translatorSearch,
    setTranslatorSearch,
    isNavigationSheetOpen,
    navigationType,
    surahSearch,
    setSurahSearch,
    verseSearch,
    setVerseSearch,
    changeContentMode,
    handleNavigation,
    handleTranslatorSelectionChange,
    handleVerseMenuClick,
    handleVerseNavigation,
    openNavigationSheet,
    setNavigationType,
    closeNavigationSheet,
    scrollToVerse,
    isMobile,
    showButtonTexts,
    navigate,
    location,
    translatorTitleColor,
    footnoteColor,
    sectionLeftBorderColor,
    verseBorderColor,
  };
};
