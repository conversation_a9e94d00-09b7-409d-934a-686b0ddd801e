import { useCallback, useEffect, useRef } from 'react';
import { useIsMobile } from '@shared/hooks/useIsMobile';

interface UseVerseScrollOptions {
  currentSurahId: number | null;
  selectedVerseNo?: number;
  behavior?: ScrollBehavior;
  delay?: number;
  offsetCorrection?: number;
}

export function useVerseScroll({
  currentSurahId,
  selectedVerseNo,
  behavior = 'smooth',
  delay = 100,
  offsetCorrection = 60,
}: UseVerseScrollOptions) {
  const lastSelectedVerseRef = useRef<number | undefined>(selectedVerseNo);
  const offsetRef = useRef(offsetCorrection);
  const isMobile = useIsMobile();

  // offsetCorrection prop'u her değiştiğinde ref'i güncelle
  useEffect(() => {
    offsetRef.current = offsetCorrection;
  }, [offsetCorrection]);

  const scrollToVerse = useCallback(
    (verseNo: number) => {
      if (!currentSurahId) return;

      const verseElement = document.getElementById(`verse-${verseNo}`);

      if (verseElement) {
        const elementPosition = verseElement.getBoundingClientRect().top;
        // Mobil için 37px, masaüstü için 11px ek kaydırma yap
        const additionalScroll = isMobile ? 37 : 11;
        // Her zaman en güncel ofset değeri için ref'i kullan
        const offsetPosition =
          elementPosition +
          window.pageYOffset -
          offsetRef.current +
          additionalScroll;

        window.scrollTo({
          top: offsetPosition,
          behavior,
        });
      }
    },
    [currentSurahId, behavior, isMobile]
  );
  
  useEffect(() => {
    if (selectedVerseNo && selectedVerseNo !== lastSelectedVerseRef.current) {
      const timeoutId = setTimeout(() => {
        scrollToVerse(selectedVerseNo);
      }, delay);
      
      lastSelectedVerseRef.current = selectedVerseNo;
      
      return () => clearTimeout(timeoutId);
    }
  }, [selectedVerseNo, scrollToVerse, delay]);
  
  return scrollToVerse;
}

export default useVerseScroll;
