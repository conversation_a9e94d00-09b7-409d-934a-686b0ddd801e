// Simple and Fast Verse Loader - FIXED INFINITE LOOP
import { useState, useEffect } from 'react';
import { useTranslationLoader } from './useTranslationLoader';
import { ContentMode } from './useContentMode';
import { useSurahs } from './useSurahs';
import { getFilesForSurah } from '@shared/utils/quranFileMapping';
import { fetchJsonFromR2 } from '@shared/utils/r2Client';

// Types
export interface CombinedVerseData {
  verse_no: number;
  arabic_text: string | null;
  words: any[] | null;
  translations: Record<string, any> | null;
}

export interface UseVerseLoaderReturn {
  verses: CombinedVerseData[] | null;
  loading: boolean;
  error: string | null;
  availableTranslators: any[];
  totalVerseCount: number;
  surahInfo: any;
  translationsLoading: boolean;
}

// Simple cache with size limit
const cache = new Map<string, any>();
const MAX_CACHE_SIZE = 20; // Max 20 surahs in cache

function getFromCache<T>(key: string): T | null {
  return cache.get(key) || null;
}

function saveToCache<T>(key: string, data: T): void {
  // Clear old entries if cache is full
  if (cache.size >= MAX_CACHE_SIZE) {
    const firstKey = cache.keys().next().value;
    if (firstKey) cache.delete(firstKey);
  }
  cache.set(key, data);
}

// Load words data for a surah - OPTIMIZED
async function loadWordsData(surahId: number): Promise<any[]> {
  const cacheKey = `words_${surahId}`;
  const cached = getFromCache<any[]>(cacheKey);
  if (cached) {
    console.log(`[loadWordsData] Cache hit for surah ${surahId}`);
    return cached;
  }

  console.log(`[loadWordsData] Loading words for surah ${surahId}`);
  const filesMappings = getFilesForSurah(surahId);
  const allData: any[] = [];

  // Load files in parallel for better performance
  const loadPromises = filesMappings.map(async (mapping) => {
    try {
      const path = `quran/words/${mapping.fileName}`;
      const fileData = await fetchJsonFromR2<any[]>(path);
      if (!Array.isArray(fileData)) return [];

      // Find surah data in file
      const surahData = fileData.find((item: any) => item.surah_no === surahId);
      if (surahData && surahData.verses) {
        // Filter verses for this mapping
        return surahData.verses.filter((verse: any) =>
          verse.verse_no >= mapping.startVerse &&
          verse.verse_no <= mapping.endVerse
        );
      }
      return [];
    } catch (error) {
      console.warn(`Failed to load ${mapping.fileName}:`, error);
      return [];
    }
  });

  // Wait for all files to load
  const results = await Promise.all(loadPromises);
  results.forEach(verses => allData.push(...verses));

  // Sort by verse number
  allData.sort((a, b) => a.verse_no - b.verse_no);

  saveToCache(cacheKey, allData);
  console.log(`[loadWordsData] Loaded ${allData.length} verses for surah ${surahId}`);
  return allData;
}

export function useVerseLoader(
  surahId: string,
  contentMode: ContentMode,
  selectedTranslators: string[],
): UseVerseLoaderReturn {
  const [state, setState] = useState<{
    verses: CombinedVerseData[] | null;
    loading: boolean;
    error: string | null;
  }>({
    verses: null,
    loading: true,
    error: null,
  });

  const surahIdNum = parseInt(surahId, 10);

  const { data: surahs } = useSurahs();
  const { translations, loading: translationsLoading, availableTranslators } = useTranslationLoader(
    surahIdNum,
    contentMode,
    selectedTranslators,
  );

  const currentSurahInfo = surahs?.find((s: any) => s.id === surahIdNum);

  useEffect(() => {
    // Debounce to prevent rapid re-renders
    const timeoutId = setTimeout(() => {
      const loadData = async () => {
        if (!surahIdNum || isNaN(surahIdNum) || surahIdNum < 1 || surahIdNum > 114) {
          setState({ verses: null, loading: false, error: 'Geçersiz sure numarası' });
          return;
        }

        if (!currentSurahInfo) {
          setState(prev => ({ ...prev, loading: true }));
          return;
        }

        // Use a combined key for caching that includes contentMode
        const cacheKey = `verses_${surahIdNum}_${contentMode}`;
        const cached = getFromCache<CombinedVerseData[]>(cacheKey);

        if (cached) {
          console.log(`[useVerseLoader] Using cached data for surah ${surahIdNum} with contentMode ${contentMode}`);
          // Still need to merge with latest translations
          const versesWithTranslations = cached.map(verse => ({
            ...verse,
            translations: translations?.[String(verse.verse_no)] || null
          }));
          setState({ verses: versesWithTranslations, loading: false, error: null });
          return;
        }

        try {
          setState(prev => ({ ...prev, loading: true, error: null }));

          const wordsData = await loadWordsData(surahIdNum);

          const verses: CombinedVerseData[] = wordsData.map(verse => {
            const arabicText = verse.words?.map((word: any) => word.arabic).join(' ') || '';
            let words = verse.words || [];
            if (contentMode === '1' || contentMode === '4') {
              words = null;
            }

            return {
              verse_no: verse.verse_no,
              arabic_text: arabicText,
              words: words,
              translations: translations?.[String(verse.verse_no)] || null,
            };
          });

          // Cache the combined data
          saveToCache(cacheKey, verses);
          setState({ verses, loading: false, error: null });
        } catch (err) {
          console.error('[useVerseLoader] Error:', err);
          setState({
            verses: null,
            loading: false,
            error: err instanceof Error ? err.message : 'Veri yüklenirken hata oluştu'
          });
        }
      };

      loadData();
    }, 100); // 100ms debounce

    return () => clearTimeout(timeoutId);
  }, [surahIdNum, currentSurahInfo?.id, contentMode, translations]); // Add translations back

  // No need for a separate useMemo, as we now handle it inside useEffect
  return {
    verses: state.verses,
    loading: state.loading || translationsLoading, // Combine loading states
    error: state.error,
    availableTranslators: availableTranslators || [],
    totalVerseCount: currentSurahInfo?.verse_count || 0,
    surahInfo: currentSurahInfo,
    translationsLoading
  };
}