import { useState, useEffect, useRef } from 'react';
import { Surah, UseSurahsReturn } from '@reader/models/types';
import { fetchData } from '@shared/utils/dataFetcher.ts';

// Path to the surahs data in the R2 bucket
const SURAHS_PATH = 'quran/surahs.json';

/**
 * Custom hook to fetch the list of all surahs.
 * Relies on the central caching mechanism in `fetchData`.
 */
export function useSurahs(): UseSurahsReturn {
  const [data, setData] = useState<Surah[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef<boolean>(true);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    const fetchSurahs = async () => {
      // Reset state for the fetch operation
      setData(null);
      setError(null);
      setLoading(true);

      try {
        const fetchedData = await fetchData<Surah[]>(SURAHS_PATH);
        if (isMountedRef.current) {
          setData(fetchedData);
        }
      } catch (err) {
        console.error("[useSurahs] Error fetching surahs:", err);
        if (isMountedRef.current) {
          setError(err instanceof Error ? err : new Error('An unknown error occurred while fetching surah data.'));
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    };

    fetchSurahs();
    
  }, []); // Fetch only once on component mount

  return { data, loading, error };
} 