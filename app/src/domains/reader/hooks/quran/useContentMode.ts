import { useState, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

// Content mode types
export type ContentMode = '1' | '2' | '3' | '4';

/**
 * Hook to manage the content mode of the Quran reader
 * - 1: Just Arabic
 * - 2: Arabic with translations
 * - 3: Word by word analysis
 * - 4: Only translations
 */
export function useContentMode() {
  // Default to just Arabic (mode 1)
  const [contentMode, setContentMode] = useState<ContentMode>('1');
  const [isContentModeOpen, setIsContentModeOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  // Change content mode and close menu
  const changeContentMode = useCallback((mode: ContentMode) => {
    setContentMode(mode);
    setIsContentModeOpen(false);
    
    // Yeni URL'yi oluştur (Content Mode değiştir, ancak ayet ve sure aynı kalsın)
    const currentPath = location.pathname;
    const pathParts = currentPath.split('/');
    
    // Mevcut surah ID'sini bul
    let surahId = '';
    if (pathParts.length >= 2) {
      const lastPart = pathParts[pathParts.length - 1];
      if (/^\d+$/.test(lastPart)) {
        surahId = lastPart;
      }
    }
    
    // Yeni base path'i oluştur (farklı görünüm modları için farklı base path'ler)
    let basePath = '/kuran';
    switch (mode) {
      case '2':
        basePath = '/mealli-kuran';
        break;
      case '3':
        basePath = '/kelime-mealli-kuran';
        break;
      case '4':
        basePath = '/kuran-meali';
        break;
    }
    
    // Yeni URL'yi oluştur
    let finalPath = basePath;
    if (surahId) {
      finalPath += `/${surahId}`;
      // Eğer mevcut URL'de verse parametresi varsa, onu koru
      if (location.search) {
        finalPath += location.search;
      }
    }
    
    // Yönlendirme yap
    navigate(finalPath);
  }, [location.pathname, location.search, navigate]);
  
  // Toggle the dropdown open state
  const toggleContentModeMenu = useCallback(() => {
    setIsContentModeOpen(prev => !prev);
  }, []);
  
  return {
    contentMode,
    setContentMode,
    isContentModeOpen,
    setIsContentModeOpen,
    changeContentMode,
    toggleContentModeMenu
  };
}

export default useContentMode; 