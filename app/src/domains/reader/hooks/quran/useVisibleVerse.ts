import { useState, useEffect, useCallback, useRef } from 'react';

interface UseVisibleVerseOptions {
  // Toplam ayet sayısı
  totalVerses: number;
  // Mevcut surah ID'si (değiştiğinde ayet no sıfırlanacak)
  currentSurahId: number | null;
  // Gözlem için offset değeri (navbar yüksekliği vb.)
  offsetTop?: number;
  // Gözlem için kullanılacak element ID ön eki
  verseIdPrefix?: string;
}

/**
 * Viewport'ta görünür olan ayetleri tespit eden ve en üstteki ayeti döndüren hook
 * 
 * @example
 * const { currentVerse, totalVerses } = useVisibleVerse({
 *   totalVerses: 7,
 *   currentSurahId: 1,
 *   offsetTop: 60
 * });
 */
export function useVisibleVerse({
  totalVerses,
  currentSurahId,
  offsetTop = 80,
  verseIdPrefix = 'verse-',
}: UseVisibleVerseOptions) {
  const [currentVerse, setCurrentVerse] = useState<number>(1);
  const prevSurahIdRef = useRef<number | null>(currentSurahId);
  
  // Scroll event handler
  const handleScroll = useCallback(() => {
    const isMobile = window.innerWidth < 850;

    // Mobilde offset'i azalt (geç geçiş için)
    const adjustedOffsetTop = isMobile ? offsetTop - 27 : offsetTop;

    // Debug sadece mobilde göster
    if (isMobile) {
      console.log(`\n=== SCROLL DEBUG (MOBİL) ===`);
      console.log(`offsetTop: ${offsetTop}px → adjustedOffsetTop: ${adjustedOffsetTop}px`);
      console.log(`windowHeight: ${window.innerHeight}px`);
      console.log(`🎯 27px azaltıldı (geç geçiş için)`);
    }

    // Görünür ayeti bulmak için her ayeti kontrol et
    for (let i = 1; i <= totalVerses; i++) {
      const verseElement = document.getElementById(`${verseIdPrefix}${i}`);

      if (verseElement) {
        const rect = verseElement.getBoundingClientRect();

        // AYNI ALGORİTMA: Masaüstü ile aynı 3 koşul (mobilde adjusted offset kullan)
        const condition1 = rect.top >= adjustedOffsetTop && rect.top <= window.innerHeight;
        const condition2 = rect.bottom >= adjustedOffsetTop && rect.bottom <= window.innerHeight;
        const condition3 = rect.top <= adjustedOffsetTop && rect.bottom >= window.innerHeight;
        const isVisible = condition1 || condition2 || condition3;

        // Mobilde ilk 5 ayet için detaylı log
        if (i <= 5 && isMobile) {
          console.log(`[MOBİL] Ayet ${i}:`);
          console.log(`  top: ${rect.top.toFixed(1)}, bottom: ${rect.bottom.toFixed(1)}`);
          console.log(`  adjustedOffsetTop: ${adjustedOffsetTop}px`);
          console.log(`  Koşul 1 (top görünür): ${condition1}`);
          console.log(`  Koşul 2 (bottom görünür): ${condition2}`);
          console.log(`  Koşul 3 (tam kaplıyor): ${condition3}`);
          console.log(`  SONUÇ: ${isVisible ? 'GÖRÜNÜR ✅' : 'GÖRÜNMEZ ❌'}`);
        }

        // Masaüstü ile AYNI algoritma
        if (isVisible) {
          if (currentVerse !== i) {
            if (isMobile) {
              console.log(`🎯 [MOBİL] Ayet değişti: ${currentVerse} → ${i}`);
            }
            setCurrentVerse(i);
          }
          break; // İlk aktif ayeti bulduk, döngüden çık
        }
      }
    }
  }, [totalVerses, offsetTop, verseIdPrefix, currentVerse]);
  
  // Surah değiştiğinde ayet numarasını sıfırla
  useEffect(() => {
    if (currentSurahId !== prevSurahIdRef.current) {
      setCurrentVerse(1);
      prevSurahIdRef.current = currentSurahId;
    }
  }, [currentSurahId]);
  
  // Scroll event listener ekle
  useEffect(() => {
    // İlk render'da da kontrol et
    handleScroll();
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);
  
  return { currentVerse, totalVerses };
}

export default useVisibleVerse; 