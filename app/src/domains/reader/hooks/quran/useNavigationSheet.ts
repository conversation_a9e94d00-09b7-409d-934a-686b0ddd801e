import { useState, useMemo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Surah } from "@reader/models/types";
import { ContentMode } from "./useContentMode";

interface UseNavigationSheetOptions {
  viewMode: ContentMode;
  allSurahs: Surah[] | null;
}

export function useNavigationSheet({ viewMode, allSurahs }: UseNavigationSheetOptions) {
  const [isNavigationSheetOpen, setIsNavigationSheetOpen] = useState(false);
  const [navigationType, setNavigationType] = useState<'surah' | 'verse'>('surah');
  const [surahSearch, setSurahSearch] = useState('');
  const [verseSearch, setVerseSearch] = useState('');
  const navigate = useNavigate();
  
  // Navigation function
  const handleNavigation = (surahId: number) => {
    let baseUrl = '/kuran';
    switch (viewMode) {
      case '2':
        baseUrl = '/mealli-kuran';
        break;
      case '3':
        baseUrl = '/kelime-mealli-kuran';
        break;
      case '4':
        baseUrl = '/kuran-meali';
        break;
    }
    navigate(`${baseUrl}/${surahId}`);
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setIsNavigationSheetOpen(false);
    setSurahSearch('');
  };
  
  // Sure filtresi (allSurahs kullanımı ve tip eklendi)
  const filteredSurahs = useMemo(() => 
    (allSurahs || []).filter((surah: Surah) => { // allSurahs kullanıldı, :Surah eklendi
      const searchTermLower = surahSearch.toLowerCase();
      return (
        surah.name.toLowerCase().includes(searchTermLower) ||
        surah.arabic_name.includes(surahSearch) ||
        surah.id.toString().includes(surahSearch)
      );
    }),
    [allSurahs, surahSearch]
  );
  
  // Ayet filtresi (allSurahs kullanımı ve tip eklendi)
  const getFilteredVerses = useCallback((currentSurahId: number | null): number[] => {
    if (!currentSurahId || !allSurahs) return [];
    
    const currentSurah = allSurahs.find((s: Surah) => s.id === currentSurahId); // allSurahs kullanıldı, :Surah eklendi
    if (!currentSurah) return [];

    const totalVerses = currentSurah.verse_count;
    return Array.from(
      { length: totalVerses }, 
      (_, i) => i + 1
    ).filter(verseNo => 
      verseNo.toString().includes(verseSearch)
    );
  }, [allSurahs, verseSearch]);
  
  // Açılır sayfayı aç
  const openNavigationSheet = (type: 'surah' | 'verse') => {
    setNavigationType(type);
    setIsNavigationSheetOpen(true);
  };
  
  return {
    isNavigationSheetOpen,
    setIsNavigationSheetOpen,
    navigationType,
    setNavigationType,
    surahSearch,
    setSurahSearch,
    verseSearch,
    setVerseSearch,
    handleNavigation,
    filteredSurahs,
    getFilteredVerses,
    openNavigationSheet
  };
} 