import { useState, useEffect, useRef, useMemo } from 'react';
import { VerseTranslationsMap, ITranslator } from '@reader/models/types';
import { ContentMode } from './useContentMode';
import { getFilesForSurah } from '@shared/utils/quranFileMapping';
import { fetchJsonFromR2 } from '@shared/utils/r2Client';

// Type definitions
interface ITranslationItem {
  verse_no: number;
  text: string;
  footnote?: string;
}

// Cache utilities - using localStorage-based cache system
// Cache yapılandırması
const CACHE_VERSION = 'v1';
const CACHE_EXPIRY_TIME = 7 * 24 * 60 * 60 * 1000; // 1 hafta (milisaniye)
const TRANSLATORS_CACHE_KEY = `quran_translators_${CACHE_VERSION}`;

interface CacheMetadata {
  timestamp: number;
  version: string;
}

interface CachedData<T> {
  data: T;
  metadata: CacheMetadata;
}

// Cache Yardımcı Fonksiyonlar
function saveTranslationToCache<T>(key: string, data: T): void {
  try {
    const cachedData: CachedData<T> = {
      data,
      metadata: {
        timestamp: Date.now(),
        version: CACHE_VERSION
      }
    };

    localStorage.setItem(key, JSON.stringify(cachedData));
    console.log(`[TranslationCache] Saved data to ${key}`);
  } catch (error) {
    console.error(`[TranslationCache] Error saving to cache for ${key}:`, error);
    // localStorage doluysa, cache temizlenmeyi deneyebilir
    try {
      localStorage.removeItem(key);
    } catch {
      // Sessizce görmezden gel
    }
  }
}

function getTranslationFromCache<T>(key: string): T | null {
  try {
    const cachedItem = localStorage.getItem(key);
    if (!cachedItem) return null;
    
    const parsed = JSON.parse(cachedItem) as CachedData<T>;
    
    // Cache versiyonu kontrol et
    if (parsed.metadata.version !== CACHE_VERSION) {
      console.log(`[TranslationCache] Outdated cache version for ${key}, clearing`);
      localStorage.removeItem(key);
      return null;
    }
    
    // Son kullanma tarihini kontrol et
    const now = Date.now();
    if (now - parsed.metadata.timestamp > CACHE_EXPIRY_TIME) {
      console.log(`[TranslationCache] Expired cache for ${key}, clearing`);
      localStorage.removeItem(key);
      return null;
    }
    
    console.log(`[TranslationCache] Hit for ${key}`);
    return parsed.data;
  } catch (error) {
    console.error(`[TranslationCache] Error reading from cache for ${key}:`, error);
    return null;
  }
}

// Return type for the hook
export interface UseTranslationLoaderReturn {
  translations: VerseTranslationsMap;
  loading: boolean;
  error: Error | null;
  availableTranslators: ITranslator[];
  translationsMap: Map<number, Map<string, {
    paragraphs: string[];
    footnotes: string[];
  }>>;
}

// Main hook implementation
export function useTranslationLoader(
  surahId: number | null,
  contentMode: ContentMode,
  selectedTranslators: string[] = []
): UseTranslationLoaderReturn {
  // State definitions
  const [translationsMap, setTranslationsMap] = useState<Map<number, Map<string, { paragraphs: string[]; footnotes: string[]; }>>>(new Map());
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [availableTranslators, setAvailableTranslators] = useState<ITranslator[]>([]);
  
  // Refs for managing component lifecycle and caching
  const isMountedRef = useRef<boolean>(false);
  // OPTIMIZATION: Cache raw file content in RAM to avoid re-fetching and re-parsing JSON
  const fileContentCacheRef = useRef<Map<string, any[]>>(new Map());

  // Load available translators once
  useEffect(() => {
    isMountedRef.current = true;
    setAvailableTranslators([]);
    setError(null);

    // Önce cache'den kontrol et
    const cachedTranslators = getTranslationFromCache<ITranslator[]>(TRANSLATORS_CACHE_KEY);
    if (cachedTranslators && cachedTranslators.length > 0) {
      setAvailableTranslators(cachedTranslators);
      console.log(`[TranslationLoader] ${cachedTranslators.length} translators loaded from cache`);
      return;
    }

    (async () => {
      try {
        const data = await fetchJsonFromR2<{ [id: string]: string }>('quran/translators.json');
        if (!isMountedRef.current) return;

        if (typeof data !== 'object' || data === null || Array.isArray(data)) {
          throw new Error('Translator data has invalid format');
        }

        const translators = Object.entries(data)
          .map(([id, name]) => ({ id, name: String(name) }))
          .sort((a, b) => a.name.localeCompare(b.name, 'tr'));

        setAvailableTranslators(translators);
        console.log(`[TranslationLoader] ${translators.length} translators loaded`);

        // Cache'e kaydet
        saveTranslationToCache(TRANSLATORS_CACHE_KEY, translators);
      } catch (err: any) {
        console.error("[TranslationLoader] Error loading translators:", err);
        if (isMountedRef.current) {
          setError(err instanceof Error ? err : new Error('Failed to load translators'));
        }
      }
    })();

    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Load translations for selected translators - FIXED INFINITE LOOP
  useEffect(() => {
    if (!isMountedRef.current) return;

    // Skip if using Arabic-only mode (1) or invalid surah
    if (contentMode === '1' || !surahId || surahId < 1 || surahId > 114) {
      setLoading(false);
      setTranslationsMap(new Map());
      return;
    }

    // Wait until available translators are loaded before checking selected translators
    if (availableTranslators.length === 0) {
      setLoading(true);
      return;
    }

    // If no translators are selected, stop loading and clear map
    if (selectedTranslators.length === 0) {
      setLoading(false);
      setTranslationsMap(new Map());
      return;
    }

    setLoading(true);
    console.log(`[TranslationLoader] Loading translations for surah ${surahId}, ${selectedTranslators.length} translators selected`);

    const processTranslations = async () => {
      if (!isMountedRef.current || !surahId) return;

      const newTranslationsMap = new Map<number, Map<string, { paragraphs: string[]; footnotes: string[]; }>>();
      const filesToFetchByTranslator: Record<string, string[]> = {};
      const uniqueFileNamesAcrossTranslators = new Set<string>();

      const mappingsForSurah = getFilesForSurah(surahId);
      if (mappingsForSurah.length === 0) {
        console.warn(`[TranslationLoader] No file mappings found for surah ${surahId}`);
        setLoading(false);
        return;
      }

      // 1. Determine which files are needed for each translator
      for (const translatorId of selectedTranslators) {
        for (const mapping of mappingsForSurah) {
          const fileName = mapping.fileName;
          const fileCacheKey = `translation_${translatorId}_${fileName}`;

          // If not in RAM cache, it might need to be fetched
          if (!fileContentCacheRef.current.has(fileCacheKey)) {
            if (!filesToFetchByTranslator[translatorId]) {
              filesToFetchByTranslator[translatorId] = [];
            }
            if (!filesToFetchByTranslator[translatorId].includes(fileName)) {
              filesToFetchByTranslator[translatorId].push(fileName);
              uniqueFileNamesAcrossTranslators.add(fileName);
            }
          }
        }
      }

      // 2. Fetch all missing files in parallel
      const fetchPromises = Object.entries(filesToFetchByTranslator).flatMap(([translatorId, fileNames]) =>
        fileNames.map(async (fileName) => {
          const fileCacheKey = `translation_${translatorId}_${fileName}`;
          const storageCacheKey = `translations_${translatorId}_${fileName}`;
          
          let fileData = getTranslationFromCache<any[]>(storageCacheKey);

          if (!fileData) {
            const path = `quran/translations/${translatorId}/${fileName}`;
            try {
              fileData = await fetchJsonFromR2<any[]>(path);
              if (Array.isArray(fileData)) {
                saveTranslationToCache(storageCacheKey, fileData);
              } else {
                console.warn(`[TranslationLoader] Invalid data format for ${path}`);
                fileData = null; // Mark as invalid
              }
            } catch (error) {
              console.error(`[TranslationLoader] Error fetching ${path}:`, error);
              fileData = null; // Mark as failed
            }
          }

          if (fileData) {
            fileContentCacheRef.current.set(fileCacheKey, fileData);
          }
        })
      );

      await Promise.all(fetchPromises);
      if (!isMountedRef.current) return;


      // 3. Process data from RAM cache for all selected translators
      for (const translatorId of selectedTranslators) {
        for (const mapping of mappingsForSurah) {
          const fileCacheKey = `translation_${translatorId}_${mapping.fileName}`;
          const fileData = fileContentCacheRef.current.get(fileCacheKey);

          if (!fileData) continue;

          const surahsInFile = fileData.filter((item: any) => item.surah_no === surahId);
          for (const surahData of surahsInFile) {
            if (surahData.verses && Array.isArray(surahData.verses)) {
              const filteredVerses = surahData.verses.filter((verse: any) =>
                verse.verse_no >= mapping.startVerse && verse.verse_no <= mapping.endVerse
              );

              for (const verse of filteredVerses) {
                if (!newTranslationsMap.has(verse.verse_no)) {
                  newTranslationsMap.set(verse.verse_no, new Map());
                }
                const verseMap = newTranslationsMap.get(verse.verse_no)!;
                verseMap.set(translatorId, {
                  paragraphs: [verse.text],
                  footnotes: verse.footnote ? [verse.footnote] : [],
                });
              }
            }
          }
        }
      }

      if (isMountedRef.current) {
        setTranslationsMap(newTranslationsMap);
        setLoading(false);
      }
    };

    processTranslations().catch(err => {
      console.error('[TranslationLoader] Error processing translations:', err);
      if (isMountedRef.current) {
        setError(err instanceof Error ? err : new Error('Error loading translations'));
        setLoading(false);
      }
    });

  }, [surahId, contentMode, selectedTranslators.join(','), availableTranslators]); // Use stable string instead of array

  // convert the Map structure to VerseTranslationsMap expected by consumers
  const translations = useMemo(() => {
    const result: VerseTranslationsMap = {};
    
    translationsMap.forEach((translatorMap, verseNo) => {
      const verseKey = String(verseNo);
      result[verseKey] = {};
      
      translatorMap.forEach((data, translatorId) => {
        if (selectedTranslators.includes(translatorId)) {
          result[verseKey][translatorId] = {
            paragraphs: data.paragraphs,
            footnotes: data.footnotes
          };
        }
      });
    });
    
    return result;
  }, [translationsMap, selectedTranslators]);

  return {
    translations,
    loading,
    error,
    availableTranslators,
    translationsMap
  };
} 