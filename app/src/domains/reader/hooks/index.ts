// Quran Specific Hooks
// export * from './quran/useQuranSettings'; // Yok
// export * from './quran/useSelectedTranslations'; // Yok
export * from './quran/useSurahs';
export * from './quran/useVerseLoader';
export * from './quran/useTranslationLoader';
export * from './quran/useContentMode';
// export * from './quran/usePersistentScrollPosition'; // Yok
export * from './quran/useNavigationSheet';
export * from './quran/useVerseScroll';
export * from './quran/useVisibleVerse';
export * from './quran/useVerseHighlight';

// Risale Hooks
export * from './risale/useRisaleBook';
export { useRisaleSection } from './risale/useRisaleSection'; // Sadece hook'u export et
export * from './risale/useNavigationSheet';
export * from './risale/useRisaleProcess';

// Genel Reader Hook'ları (varsa buraya eklenebilir)
// export * from './useSomeGeneralReaderHook';
export * from './useReaderInteractivity';

// Common Layout and Error Handling Hooks
export * from './usePageLayout';
export * from './useErrorHandling';