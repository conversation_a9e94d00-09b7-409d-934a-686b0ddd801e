// Simple Word Analysis Hook
import { useState, useEffect } from 'react';
import { getFilesForSurah } from '@shared/utils/quranFileMapping';

export interface MergedWord {
  id: number;
  arabic: string;
  meaning: string;
  analysis: {
    english_meaning: string;
    arapca_gramer: string;
    zaman: string;
    turkce_gramer: string;
    sarf_bilgisi: string;
    bab: string;
    kok: string;
    kok_id?: number;
  };
}

// Simple cache with size limit
const wordsCache = new Map<string, MergedWord[]>();
const MAX_WORDS_CACHE_SIZE = 50; // Max 50 verses in cache

export const useWordAnalysis = (verseKey: string) => {
  const [data, setData] = useState<MergedWord[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!verseKey || !verseKey.includes('-')) {
      setLoading(false);
      return;
    }

    // Debounce to prevent rapid calls
    const timeoutId = setTimeout(() => {
      const fetchWordData = async () => {
        try {
          const [surah, verse] = verseKey.split('-');
          const surahNum = parseInt(surah, 10);
          const verseNum = parseInt(verse, 10);

          if (isNaN(surahNum) || isNaN(verseNum)) {
            throw new Error('Geçersiz ayet anahtarı');
          }

          // Check cache first
          const cacheKey = `${surahNum}-${verseNum}`;
          if (wordsCache.has(cacheKey)) {
            console.log(`[useWordAnalysis] Cache hit for ${verseKey}`);
            setData(wordsCache.get(cacheKey)!);
            setLoading(false);
            return;
          }

          setLoading(true);
          setError(null);

          const filesMappings = getFilesForSurah(surahNum);
          let foundWords: MergedWord[] | null = null;

          // Try to find the verse in the appropriate file
          for (const mapping of filesMappings) {
            if (verseNum < mapping.startVerse || verseNum > mapping.endVerse) {
              continue;
            }

            try {
              const path = `/data/quran/words/${mapping.fileName}`;
              const response = await fetch(path);
              if (!response.ok) {
                console.warn(`Failed to fetch ${path}: ${response.status}`);
                continue;
              }

              const fileData = await response.json();
              if (!Array.isArray(fileData)) continue;

              const surahData = fileData.find((item: any) => item.surah_no === surahNum);
              if (!surahData || !surahData.verses) continue;

              const verseData = surahData.verses.find((v: any) => v.verse_no === verseNum);
              if (verseData && verseData.words) {
                foundWords = verseData.words;
                break;
              }
            } catch (err) {
              console.warn(`Failed to load ${mapping.fileName}:`, err);
            }
          }

          if (foundWords) {
            // Clear old entries if cache is full
            if (wordsCache.size >= MAX_WORDS_CACHE_SIZE) {
              const firstKey = wordsCache.keys().next().value;
              if (firstKey) wordsCache.delete(firstKey);
            }
            wordsCache.set(cacheKey, foundWords);
            setData(foundWords);
            console.log(`[useWordAnalysis] Loaded ${foundWords.length} words for ${verseKey}`);
          } else {
            throw new Error(`Ayet ${verseKey} bulunamadı`);
          }
        } catch (err) {
          console.error('[useWordAnalysis] Error:', err);
          setError(err instanceof Error ? err : new Error('Bilinmeyen hata'));
        } finally {
          setLoading(false);
        }
      };

      fetchWordData();
    }, 50); // 50ms debounce

    return () => clearTimeout(timeoutId);
  }, [verseKey]);

  return { data, loading, error };
};
