import { useState, useEffect } from 'react';

interface RootDetailData {
  letter: string;
  arabic_root: string;
  latin_transliteration: string;
  content: string;
}

interface MufredatIndexEntry {
  index: number;
  arabic_root: string;
  latin_transliteration: string;
}

// Önbellekleme için basit bir nesne
const mufredatCache: { [key: string]: MufredatIndexEntry[] } = {};

export const useRootWordById = (kokId: number | null) => {
  const [data, setData] = useState<RootDetailData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (kokId === null) {
      setData(null);
      return;
    }

    const fetchRootData = async () => {
      setLoading(true);
      setError(null);
      setData(null);

      try {
        // Mufredat index'i yükle (önbellek kontrolü ile)
        if (Object.keys(mufredatCache).length === 0) {
          const indexResponse = await fetch('/data/quran/root/mufredat_index.json');
          if (!indexResponse.ok) {
            throw new Error('Mufredat index dosyası yüklenemedi.');
          }
          const indexData = await indexResponse.json();
          // Veriyi yeniden yapılandırarak önbelleğe al
          for (const letter in indexData) {
            indexData[letter].forEach((entry: MufredatIndexEntry) => {
                if (!mufredatCache[letter]) {
                    mufredatCache[letter] = [];
                }
                mufredatCache[letter].push(entry);
            });
          }
        }

        // Kök ID'sine göre doğru kökü bul
        let foundRoot: MufredatIndexEntry | null = null;
        let rootLetter: string | null = null;

        for (const letter in mufredatCache) {
            const entry = mufredatCache[letter].find(e => e.index === kokId);
            if (entry) {
                foundRoot = entry;
                rootLetter = letter;
                break;
            }
        }

        if (!foundRoot || !rootLetter) {
          // Belki de burada bir hata fırlatmak yerine null ayarlamak daha iyidir.
          // throw new Error(`Kök ID'si ${kokId} bulunamadı.`);
          setData(null);
          setLoading(false);
          return;
        }

        const normalizedRoot = foundRoot.arabic_root.replace(/أ/g, 'أ');
        const cleanRoot = normalizedRoot.replace(/\s/g, '_');
        const fileName = `${cleanRoot}.json`;
        const detailResponse = await fetch(`/data/quran/root/${rootLetter}/${fileName}`);

        if (!detailResponse.ok) {
          setData(null);
          setLoading(false);
          return;
        }

        const rootDetailData: RootDetailData = await detailResponse.json();
        setData(rootDetailData);

      } catch (err) {
        if (err instanceof Error) {
          setError(err);
        } else {
          setError(new Error('Bilinmeyen bir hata oluştu.'));
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRootData();
  }, [kokId]);

  return { data, loading, error };
};
