import { useState, useEffect, useRef } from 'react';
import {
  SectionStateData,
  BookIndexModule,
  RisaleSectionDef,
  RisaleContentData
} from '../../models/types';
import { fetchData } from '@shared/utils/dataFetcher';

// Interface for the raw structure of the index.json file from R2
interface RawBookIndexData {
  id: string;
  title: string;
  author?: string;
  sections?: Array<{ id: string; title: string; [key: string]: any }>;
  subsections?: Array<{ id: string; title: string; [key: string]: any }>; // For backward compatibility
  [key: string]: any;
}

/**
 * Custom hook to fetch the content and structure for a specific section of a Risale-i Nur book.
 * @param bookId The ID of the book.
 * @param sectionId The ID of the section to fetch.
 */
export function useRisaleSection(bookId: string | undefined, sectionId: string | undefined) {
  const [data, setData] = useState<SectionStateData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef<boolean>(true);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    const loadSectionData = async () => {
      if (!bookId || !sectionId) {
        setLoading(false);
        return;
      }

      // Reset state for the new request
      setData(null);
      setError(null);
      setLoading(true);

      try {
        const indexPath = `risalei_nur/${bookId}/index.json`;
        const contentPath = `risalei_nur/${bookId}/content/${sectionId}.json`;

        // Step 1: Fetch index and content data in parallel
        const [rawIndexData, sectionContentData] = await Promise.all([
          fetchData<RawBookIndexData>(indexPath),
          fetchData<RisaleContentData>(contentPath)
        ]);

        if (!isMountedRef.current) return;

        // Step 2: Validate and process index data
        if (!rawIndexData || !rawIndexData.id || !rawIndexData.title) {
          throw new Error(`Invalid or incomplete index.json structure for book ${bookId}.`);
        }
        const sectionsArray = rawIndexData.sections || rawIndexData.subsections || [];
        if (!Array.isArray(sectionsArray)) {
          throw new Error(`The 'sections' or 'subsections' field is missing or not an array for book ${bookId}.`);
        }
        const transformedIndexData: BookIndexModule = {
          metadata: { id: rawIndexData.id, title: rawIndexData.title, author: rawIndexData.author },
          structure: {
            sections: sectionsArray.map(s => ({ id: String(s.id), title: String(s.title) })) as RisaleSectionDef[],
          }
        };

        // Step 3: Validate content data
        if (!sectionContentData || !Array.isArray(sectionContentData.sentences)) {
          throw new Error(`Content for section ${sectionId} is invalid or missing a 'sentences' array.`);
        }

        // Step 4: Find the specific section and its neighbors
        const sectionDefinition = transformedIndexData.structure.sections.find(s => s.id === sectionId);
        if (!sectionDefinition) {
          throw new Error(`Section ${sectionId} not found in the index for book ${bookId}.`);
        }

        const currentIndex = transformedIndexData.structure.sections.findIndex(s => s.id === sectionId);
        const prevSection = currentIndex > 0 ? transformedIndexData.structure.sections[currentIndex - 1] : null;
        const nextSection = currentIndex < transformedIndexData.structure.sections.length - 1 ? transformedIndexData.structure.sections[currentIndex + 1] : null;

        // Step 5: Set the final state
        setData({
          metadata: transformedIndexData.metadata,
          title: sectionDefinition.title,
          content: sectionContentData,
          structure: {
            ...transformedIndexData.structure,
            prevSection,
            nextSection,
          }
        });

      } catch (err) {
        console.error(`[useRisaleSection] Error loading Risale section ${bookId}/${sectionId}:`, err);
        if (isMountedRef.current) {
          setError(err instanceof Error ? err : new Error("An unknown error occurred while loading the section."));
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    };

    loadSectionData();
    
  }, [bookId, sectionId]);

  return { data, loading, error };
}