import { useState, useMemo, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { RisaleSectionDef, Book } from "@reader/models/types";
import { fetchData } from '@shared/utils/dataFetcher';

// Local interface for this hook to handle both sections and subsections
interface RawBookIndexData {
  id: string;
  title: string;
  author?: string;
  sections?: Array<{ id: string; title: string; [key: string]: any }>;
  subsections?: Array<{ id: string; title: string; [key: string]: any }>;
  [key: string]: any;
}

interface UseNavigationSheetOptions {
  currentBookId: string | undefined;
  initialBookTitle: string | undefined;
}

export function useRisaleNavigationSheet({ currentBookId, initialBookTitle }: UseNavigationSheetOptions) {
  const [isOpen, setIsOpen] = useState(false);
  const [navigationType, setNavigationType] = useState<'section' | 'books'>('section');
  const [sectionSearch, setSectionSearch] = useState('');
  const [bookSearch, setBookSearch] = useState('');
  const [allBooks, setAllBooks] = useState<Book[]>([]);
  
  const [selectedBookId, setSelectedBookId] = useState<string | null>(currentBookId || null);
  const [selectedBookTitle, setSelectedBookTitle] = useState<string | null>(initialBookTitle || null);
  const [selectedBookSections, setSelectedBookSections] = useState<RisaleSectionDef[]>([]);
  const [isLoadingSections, setIsLoadingSections] = useState(false);
  const [currentBookCategory, setCurrentBookCategory] = useState<number | null>(null);
  
  const navigate = useNavigate();

  useEffect(() => {
    const fetchBooks = async () => {
      try {
        const booksData = await fetchData<Book[]>('/library/books.json');
        const risaleBooks = booksData.filter(book => book.category_id === 2 || book.category_id === 3);
        setAllBooks(risaleBooks);
        
        // Mevcut kitabın kategorisini belirle
        if (currentBookId) {
          const currentBook = booksData.find(book => book.id === parseInt(currentBookId));
          if (currentBook && currentBook.category_id) {
            setCurrentBookCategory(currentBook.category_id);
          }
        }
      } catch (error) {
        console.error("Failed to fetch books:", error);
      }
    };
    fetchBooks();
  }, [currentBookId]);
  
  useEffect(() => {
    // When the page's own book ID changes (e.g., URL navigation), reset the sheet's state.
    setSelectedBookId(currentBookId || null);
    setSelectedBookTitle(initialBookTitle || null);
  }, [currentBookId, initialBookTitle]);
  
  useEffect(() => {
    if (!selectedBookId) {
      setSelectedBookSections([]);
      setSelectedBookTitle(initialBookTitle || null); // Reset title when no book is selected
      return;
    }
    
    const loadSelectedBookSections = async () => {
      setIsLoadingSections(true);
      const indexPath = `/risalei_nur/${selectedBookId}/index.json`;
      try {
        const rawIndexData = await fetchData<RawBookIndexData>(indexPath);
        
        // Set title from the definitive source
        setSelectedBookTitle(rawIndexData.title);
        
        // Handle both 'sections' and 'subsections' fields for backward compatibility
        const sectionsArray = rawIndexData.sections || (rawIndexData as any).subsections || [];
        
        const transformedSections = sectionsArray.map((s: { id: string; title: string; }) => ({
          id: String(s.id),
          title: String(s.title),
        })) as RisaleSectionDef[];

        setSelectedBookSections(transformedSections);

      } catch (err) {
        console.error(`Error loading sections for book ${selectedBookId}:`, err);
        setSelectedBookSections([]);
      } finally {
        setIsLoadingSections(false);
      }
    };

    loadSelectedBookSections();
  }, [selectedBookId, initialBookTitle]);
  
  const handleNavigation = (sectionId: string) => {
    if (!selectedBookId) return;
    navigate(`/risale/${selectedBookId}/${sectionId}`);
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setIsOpen(false);
  };
  
  const switchBookInSheet = (bookId: number) => {
    setSelectedBookId(String(bookId));
    // Title will be set by the useEffect when data is fetched.
    // Navigation type'ı değiştirmeye gerek yok, kullanıcı hangi tab'daysa orada kalsın
  };
  
  const filteredSections = useMemo(() => {
    return selectedBookSections.filter((section: RisaleSectionDef) => {
      const searchTermLower = sectionSearch.toLowerCase();
      return (
        section.title.toLowerCase().includes(searchTermLower) ||
        section.id.toString().includes(searchTermLower)
      );
    });
  }, [selectedBookSections, sectionSearch]);

  const openNavigationSheet = (type: 'section' | 'books' = 'section') => {
    setNavigationType(type);
    setIsOpen(true);
  };
  
  // Kategoriye göre filtrelenmiş kitaplar
  const filteredBooks = useMemo(() => {
    if (!currentBookCategory) return allBooks;
    return allBooks.filter(book => book.category_id === currentBookCategory);
  }, [allBooks, currentBookCategory]);

  return {
    isOpen,
    setIsOpen,
    navigationType,
    setNavigationType,
    sectionSearch,
    setSectionSearch,
    bookSearch,
    setBookSearch,
    handleNavigation,
    switchBookInSheet,
    filteredSections,
    isLoadingSections,
    openNavigationSheet,
    bookTitle: selectedBookTitle,
    selectedBookId,
    books: filteredBooks,
  };
}
