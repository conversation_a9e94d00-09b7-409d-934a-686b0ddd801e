import { useMemo, useCallback } from 'react';
import {
  SectionStateData,
  RisaleArabicPhrase,
  RisaleDictionaryItem,
  RisaleSentence,
  RisaleFootnote,
} from '@reader/models/types';

// #region Helper Functions
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\\]/g, '\\$&');
}

function normalizeArabic(text: string): string {
  if (!text) return '';
  return text
    .replace(/[ً-ْ]/g, '') // Diacritics
    .replace(/ـ/g, '')       // Tatweel
    .replace(/[آأإ]/g, 'ا') // Alef variants
    .replace(/ؤ/g, 'و')       // Waw with Hamza
    .replace(/ئ/g, 'ي')       // Yeh with Hamza
    .replace(/ى/g, 'ي')       // Alef Maqsurah
    .replace(/ة/g, 'ه');      // Teh Marbuta
}
// #endregion

// #region Match Finding
enum MatchType {
  ARABIC_INTERACTIVE = 'arabic_interactive',
  ARABIC_GENERIC = 'arabic_generic',
  DICTIONARY = 'dict',
  FOOTNOTE = 'footnote',
  VERSE_BREAK = 'verse_break',
  CENTER_ALIGNMENT = 'center_alignment',
}

type Match<T> = {
  start: number;
  end: number;
  type: MatchType;
  data: T;
  length: number;
};

function findAllArabicMatches(
  text: string,
  arabicPhrases: RisaleArabicPhrase[]
): Match<RisaleArabicPhrase | null>[] {
  const matches: Match<RisaleArabicPhrase | null>[] = [];
  if (!text) return matches;

  // First, find all interactive Arabic phrases (exact matches)
  const interactiveMatches: Array<{start: number, end: number, phrase: RisaleArabicPhrase}> = [];
  
  if (arabicPhrases && arabicPhrases.length > 0) {
    const normalizedText = normalizeArabic(text);

    arabicPhrases.forEach(phrase => {
      const normalizedTerm = normalizeArabic(phrase.arabic);
      if (!normalizedTerm) return;

      let searchIndex = 0;
      while (searchIndex < normalizedText.length) {
        const matchIndex = normalizedText.indexOf(normalizedTerm, searchIndex);
        if (matchIndex === -1) break;

        let originalStartIndex = -1,
          originalEndIndex = -1,
          nonDiacriticIndex = -1;
        for (let i = 0; i < text.length; i++) {
          if (!/[\u064B-\u0652\u0640]/.test(text[i])) nonDiacriticIndex++;
          if (nonDiacriticIndex === matchIndex) {
            originalStartIndex = i;
            break;
          }
        }

        if (originalStartIndex !== -1) {
          let normalizedMatchLength = 0;
          for (let i = originalStartIndex; i < text.length; i++) {
            if (!/[\u064B-\u0652\u0640]/.test(text[i])) normalizedMatchLength++;
            if (normalizedMatchLength === normalizedTerm.length) {
              originalEndIndex = i + 1;
              break;
            }
          }
        }

        if (originalStartIndex !== -1 && originalEndIndex !== -1) {
          interactiveMatches.push({
            start: originalStartIndex,
            end: originalEndIndex,
            phrase: phrase
          });
          
          // Add the interactive match to results
          matches.push({
            start: originalStartIndex,
            end: originalEndIndex,
            type: MatchType.ARABIC_INTERACTIVE,
            data: phrase,
            length: originalEndIndex - originalStartIndex,
          });
        }
        searchIndex = matchIndex + 1;
      }
    });
  }

  // Now find remaining Arabic text segments that are not covered by interactive matches
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+/g;
  let regexMatch;
  
  while ((regexMatch = arabicRegex.exec(text)) !== null) {
    const matchText = regexMatch[0];
    if (matchText.trim().length > 0) {
      const start = regexMatch.index;
      const end = regexMatch.index + matchText.length;
      
      // Check if this Arabic text overlaps with any interactive match
      const hasOverlap = interactiveMatches.some(im => 
        !(end <= im.start || start >= im.end)
      );
      
      if (!hasOverlap) {
        // This is generic Arabic text - not covered by interactive matches
        matches.push({
          start,
          end,
          type: MatchType.ARABIC_GENERIC,
          data: null,
          length: end - start,
        });
      }
    }
  }
  
  return matches;
}

function findDictionaryMatches(
  text: string,
  dictionary: RisaleDictionaryItem[]
): Match<RisaleDictionaryItem>[] {
  const matches: Match<RisaleDictionaryItem>[] = [];
  dictionary.forEach(item => {
    try {
      const regex = new RegExp(
        escapeRegExp(item.word).replace(/\s+/g, '\\s+'),
        'g'
      );
      let match;
      while ((match = regex.exec(text)) !== null) {
        matches.push({
          start: match.index,
          end: match.index + match[0].length,
          type: MatchType.DICTIONARY,
          data: item,
          length: match[0].length,
        });
      }
    } catch (e) {
      console.error('Regex error for dictionary term:', item.word, e);
    }
  });
  return matches;
}

function findFootnoteMatches(
  text: string,
  footnotes: RisaleFootnote[]
): Match<RisaleFootnote>[] {
  const matches: Match<RisaleFootnote>[] = [];
  footnotes.forEach(item => {
    const term = `<sup><b>${item.number}</b></sup>`;
    try {
      const regex = new RegExp(escapeRegExp(term), 'g');
      let match;
      while ((match = regex.exec(text)) !== null) {
        matches.push({
          start: match.index,
          end: match.index + match[0].length,
          type: MatchType.FOOTNOTE,
          data: item,
          length: match[0].length,
        });
      }
    } catch (e) {
      console.error('Regex error for footnote term:', term, e);
    }
  });
  return matches;
}

function findVerseBreakMatches(text: string): Match<null>[] {
  const matches: Match<null>[] = [];
  const verseBreakRegex = /<verse_break>/g;
  let match;
  while ((match = verseBreakRegex.exec(text)) !== null) {
    matches.push({
      start: match.index,
      end: match.index + match[0].length,
      type: MatchType.VERSE_BREAK,
      data: null,
      length: match[0].length,
    });
  }
  return matches;
}

function findCenterAlignmentMatches(text: string): Match<string>[] {
  const matches: Match<string>[] = [];
  const caRegex = /<ca>(.*?)<\/ca>/gs;
  let match;
  while ((match = caRegex.exec(text)) !== null) {
    matches.push({
      start: match.index,
      end: match.index + match[0].length,
      type: MatchType.CENTER_ALIGNMENT,
      data: match[1], // Store the inner content
      length: match[0].length,
    });
  }
  return matches;
}
// #endregion

// #region HTML Generation
function generateInteractiveHtml(
  originalText: string,
  type: MatchType,
  data: any,
  skipInteractive: boolean = false
): string {
  switch (type) {
    case MatchType.DICTIONARY:
      const dictItem = data as RisaleDictionaryItem;
      return `<span class="interactive-word" data-dict-word="${dictItem.word}">${originalText}</span>`;
    case MatchType.ARABIC_INTERACTIVE:
      const phraseItem = data as RisaleArabicPhrase;
      return `<span class="arabic-text interactive-arabic-phrase" data-arabic-text="${encodeURIComponent(
        phraseItem.arabic
      )}" data-arabic-translation="${encodeURIComponent(
        phraseItem.translation
      )}">${originalText}</span>`;
    case MatchType.ARABIC_GENERIC:
      return `<span class="arabic-text">${originalText}</span>`;
    case MatchType.FOOTNOTE:
      return originalText; // Already HTML
    case MatchType.VERSE_BREAK:
      return `<span class="verse-separator inline-block w-3 h-3 rounded-full bg-current opacity-40 mx-2 align-middle" title="Ayet Sonu"></span>`;
    case MatchType.CENTER_ALIGNMENT:
      // Process the inner content of <ca> tags and re-wrap with <ca>
      const innerContent = data as string;
      const processedInner = processText(innerContent, [], [], [], skipInteractive);
      return `<ca>${processedInner}</ca>`;
    default:
      return originalText;
  }
}

function buildResultString(
  text: string,
  matches: Match<any>[],
  skipInteractive: boolean = false
): string {
  if (matches.length === 0) return text;

  let result = '';
  let currentIndex = 0;
  matches.forEach(match => {
    result += text.substring(currentIndex, match.start);
    const originalMatchText = text.substring(match.start, match.end);
    result += generateInteractiveHtml(
      originalMatchText,
      match.type,
      match.data,
      skipInteractive
    );
    currentIndex = match.end;
  });
  result += text.substring(currentIndex);
  return result;
}
// #endregion

// #region Core Processing Logic
function processText(
  text: string,
  dictionary: RisaleDictionaryItem[],
  arabicPhrases: RisaleArabicPhrase[],
  footnotes: RisaleFootnote[],
  skipInteractive: boolean = false
): string {
  if (!text?.trim()) return text;

  let processedText = text;

  // Stage 1: Process center alignment tags first (if they exist)
  const centerAlignmentMatches = findCenterAlignmentMatches(processedText);
  if (centerAlignmentMatches.length > 0) {
    processedText = buildResultString(processedText, centerAlignmentMatches, skipInteractive);
  }

  // Stage 2: Process verse breaks (highest priority after center alignment)
  const verseBreakMatches = findVerseBreakMatches(processedText);
  if (verseBreakMatches.length > 0) {
    processedText = buildResultString(processedText, verseBreakMatches, skipInteractive);
  }

  // Stage 3: Process all other matches in the transformed text
  const allMatches: Match<any>[] = [
    ...findAllArabicMatches(processedText, skipInteractive ? [] : arabicPhrases),
    ...(skipInteractive ? [] : findDictionaryMatches(processedText, dictionary)),
    ...findFootnoteMatches(processedText, footnotes),
  ];

  // No need for complex priority system anymore, just sort by position
  allMatches.sort((a, b) => {
    if (a.start !== b.start) return a.start - b.start;
    return b.length - a.length;
  });

  const finalMatches: Match<any>[] = [];
  let lastEnd = -1;
  for (const match of allMatches) {
    if (match.start >= lastEnd) {
      finalMatches.push(match);
      lastEnd = match.end;
    }
  }

  return buildResultString(processedText, finalMatches, skipInteractive);
}
// #endregion

// The Hook
export function useRisaleProcess(
  sectionData: SectionStateData | null
) {
  const { dictionary, footnotes, arabic_phrases, sentences } =
    sectionData?.content || {};

  const processTextSinglePass = useCallback(
    (text: string, skipInteractive: boolean = false): string => {
      return processText(
        text,
        dictionary || [],
        arabic_phrases || [],
        footnotes || [],
        skipInteractive
      );
    },
    [dictionary, arabic_phrases, footnotes]
  );

  const processedSentences: RisaleSentence[] = useMemo(() => {
    if (!sentences) return [];
    return sentences.map(sentence => ({
      ...sentence,
      text: processTextSinglePass(sentence.text || '', sentence.type === 'title'),
    }));
  }, [sentences, processTextSinglePass]);

  return {
    processedSentences,
    dictionary: dictionary || [],
    footnotes: footnotes || [],
  };
}
