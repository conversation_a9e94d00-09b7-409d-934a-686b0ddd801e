/// <reference types="vite/client" />

import { useState, useEffect, useRef } from 'react';
import { BookIndexModule, RisaleSectionDef } from '../../models/types';
import { fetchData } from '@shared/utils/dataFetcher';

// Interface for the raw structure of the index.json file from R2
interface RawBookIndexData {
  id: string;
  title: string;
  author?: string;
  sections?: Array<{ id: string; title: string; [key: string]: any }>;
  subsections?: Array<{ id: string; title: string; [key: string]: any }>; // For backward compatibility
  [key: string]: any;
}

/**
 * Custom hook to fetch the index data for a Risale-i Nur book from R2.
 * @param bookId The ID of the book to fetch.
 */
export function useRisaleBook(bookId: string | undefined) {
  const [data, setData] = useState<BookIndexModule | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef<boolean>(true);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (!bookId) {
      setData(null);
      setLoading(false);
      return;
    }

    const loadBookIndex = async () => {
      // Reset state for the new request
      setData(null);
      setError(null);
      setLoading(true);

      try {
        const bookIndexPath = `risalei_nur/${bookId}/index.json`;
        console.log(`[useRisaleBook] Fetching Risale book index from R2: ${bookIndexPath}`);

        const rawBookData = await fetchData<RawBookIndexData>(bookIndexPath);

        if (isMountedRef.current) {
          // Validate and transform the raw data
          if (!rawBookData || typeof rawBookData.id !== 'string' || typeof rawBookData.title !== 'string') {
            throw new Error(`Invalid or incomplete index.json structure for book ${bookId}.`);
          }

          const sectionsArray = rawBookData.sections || rawBookData.subsections || [];
          if (!Array.isArray(sectionsArray)) {
            throw new Error(`The 'sections' or 'subsections' field is missing or not an array for book ${bookId}.`);
          }

          const transformedData: BookIndexModule = {
            metadata: {
              id: rawBookData.id,
              title: rawBookData.title,
              author: rawBookData.author,
            },
            structure: {
              sections: sectionsArray.map(s => ({
                id: String(s.id),
                title: String(s.title),
              })) as RisaleSectionDef[],
            }
          };
          
          setData(transformedData);
        }
      } catch (err) {
        console.error(`[useRisaleBook] Error loading index for book ${bookId}:`, err);
        if (isMountedRef.current) {
          setError(err instanceof Error ? err : new Error("An unknown error occurred while loading the book index."));
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    };
    
    loadBookIndex();

  }, [bookId]);

  return { data, loading, error };
} 