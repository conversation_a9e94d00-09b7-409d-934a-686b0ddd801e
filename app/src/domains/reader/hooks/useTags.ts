import { useState, useEffect, useMemo } from 'react';

// Tag types
export interface Tag {
  id: number;
  name: string;
  count: number;
  sentences: TagSentence[];
}

export interface TagSentence {
  id: string;
  text: string;
  type?: string;
  alignment?: 'left' | 'center' | 'right';
  paragraph_start?: boolean;
  verse_break_after?: boolean;
  line_break?: boolean;
  divider_after?: boolean;
  role?: string;
}

export interface TagIndex {
  [key: string]: string;
}

// Hook for tag index
export function useTagIndex() {
  const [tagIndex, setTagIndex] = useState<TagIndex>({});
  const [reverseIndex, setReverseIndex] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTagIndex = async () => {
      try {
        setLoading(true);
        
        // Load both indexes
        const [indexResponse, reverseResponse] = await Promise.all([
          fetch('/data/tags/index.json'),
          fetch('/data/tags/reverse-index.json')
        ]);

        if (!indexResponse.ok) {
          throw new Error(`Index dosyası yüklenemedi: ${indexResponse.status} ${indexResponse.statusText}`);
        }

        if (!reverseResponse.ok) {
          throw new Error(`Reverse index dosyası yüklenemedi: ${reverseResponse.status} ${reverseResponse.statusText}`);
        }

        const indexText = await indexResponse.text();
        const reverseText = await reverseResponse.text();

        console.log('Index response first 100 chars:', indexText.substring(0, 100));
        console.log('Reverse response first 100 chars:', reverseText.substring(0, 100));

        const index = JSON.parse(indexText);
        const reverse = JSON.parse(reverseText);

        setTagIndex(index);
        setReverseIndex(reverse);
        setError(null);
      } catch (err) {
        console.error('Tag index loading error:', err);
        setError(err instanceof Error ? err.message : 'Bilinmeyen hata');
      } finally {
        setLoading(false);
      }
    };

    loadTagIndex();
  }, []);

  return { tagIndex, reverseIndex, loading, error };
}

// Hook for specific tag data
export function useTag(tagId: number | null) {
  const [tag, setTag] = useState<Tag | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!tagId) {
      setTag(null);
      return;
    }

    const loadTag = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/data/tags/${tagId}.json`);
        if (!response.ok) {
          throw new Error(`Tag ${tagId} bulunamadı`);
        }

        const tagData = await response.json();
        setTag(tagData);
      } catch (err) {
        console.error('Tag loading error:', err);
        setError(err instanceof Error ? err.message : 'Tag yüklenemedi');
        setTag(null);
      } finally {
        setLoading(false);
      }
    };

    loadTag();
  }, [tagId]);

  return { tag, loading, error };
}

// Hook for sentence tags
export function useSentenceTags(sentenceId: string | null, sentenceTags?: number[]) {
  const { tagIndex } = useTagIndex();
  
  const tags = useMemo(() => {
    if (!sentenceId || !sentenceTags || sentenceTags.length === 0 || Object.keys(tagIndex).length === 0) {
      return [];
    }

    return sentenceTags
      .map(tagId => ({
        id: tagId,
        name: tagIndex[tagId.toString()] || `Tag ${tagId}`
      }))
      .filter(tag => tag.name !== `Tag ${tag.id}`); // Filter out missing tags
  }, [sentenceId, sentenceTags, tagIndex]);

  return { tags };
}

// Hook for tag search
export function useTagSearch() {
  const { tagIndex } = useTagIndex();
  
  const searchTags = useMemo(() => {
    return (query: string) => {
      if (!query.trim() || Object.keys(tagIndex).length === 0) {
        return [];
      }

      const searchTerm = query.toLowerCase().trim();
      const results: Array<{ id: number; name: string }> = [];

      Object.entries(tagIndex).forEach(([id, name]) => {
        if (name.toLowerCase().includes(searchTerm)) {
          results.push({
            id: parseInt(id),
            name
          });
        }
      });

      // Sort by relevance (exact matches first, then starts with, then contains)
      return results.sort((a, b) => {
        const aLower = a.name.toLowerCase();
        const bLower = b.name.toLowerCase();
        
        if (aLower === searchTerm) return -1;
        if (bLower === searchTerm) return 1;
        if (aLower.startsWith(searchTerm)) return -1;
        if (bLower.startsWith(searchTerm)) return 1;
        return a.name.localeCompare(b.name, 'tr');
      });
    };
  }, [tagIndex]);

  return { searchTags };
}

// Hook for popular tags
export function usePopularTags(limit: number = 20) {
  const [popularTags, setPopularTags] = useState<Array<{ id: number; name: string; count: number }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPopularTags = async () => {
      try {
        setLoading(true);
        
        // Load a sample of tags to get their counts
        const sampleTagIds = Array.from({ length: Math.min(limit * 3, 100) }, (_, i) => i + 1);
        
        const tagPromises = sampleTagIds.map(async (id) => {
          try {
            const response = await fetch(`/data/tags/${id}.json`);
            if (response.ok) {
              const tag = await response.json();
              return { id: tag.id, name: tag.name, count: tag.count };
            }
            return null;
          } catch {
            return null;
          }
        });

        const tags = await Promise.all(tagPromises);
        const validTags = tags.filter(Boolean) as Array<{ id: number; name: string; count: number }>;
        
        // Sort by count and take top results
        const sorted = validTags
          .sort((a, b) => b.count - a.count)
          .slice(0, limit);

        setPopularTags(sorted);
        setError(null);
      } catch (err) {
        console.error('Popular tags loading error:', err);
        setError(err instanceof Error ? err.message : 'Popüler etiketler yüklenemedi');
      } finally {
        setLoading(false);
      }
    };

    loadPopularTags();
  }, [limit]);

  return { popularTags, loading, error };
}
