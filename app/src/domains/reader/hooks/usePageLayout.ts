import React, { ReactNode } from 'react';

interface UsePageLayoutProps {
  title: string;
  isMobile: boolean;
}

interface PageLayoutConfig {
  navbarCenterContent: ReactNode | null;
  secondRowContent: ReactNode | null;
  navbarTwoRows: boolean;
  showBackButton: boolean;
  showLogo: boolean;
  showThemeToggle: boolean;
  showLoginButton: boolean;
  showMoreOptions: boolean;
}

export const usePageLayout = ({ title, isMobile }: UsePageLayoutProps): PageLayoutConfig => {
  const titleNode: ReactNode = React.createElement('h1', {
    className: "text-base font-medium truncate px-2 m-0 leading-none",
    style: { color: 'var(--text-color)' }
  }, title);

  return {
    navbarCenterContent: isMobile ? null : titleNode,
    secondRowContent: isMobile ? titleNode : null,
    navbarTwoRows: isMobile,
    showBackButton: true,
    showLogo: true,
    showThemeToggle: true,
    showLoginButton: !isMobile,
    showMoreOptions: true
  };
};
