import React, { useState, useEffect, useCallback, RefObject } from 'react';
import { SectionStateData, RisaleFootnote, RisaleDictionaryItem } from '@reader/models/types';

interface UseReaderInteractivityProps {
  contentRef: RefObject<HTMLDivElement>;
  actionMenuRef: RefObject<HTMLDivElement>;
  sectionData: SectionStateData | null;
}

/**
 * Custom hook to manage interactivity within the reader content area,
 * including tooltips for dictionary words/footnotes and the text selection action menu.
 */
export function useReaderInteractivity({
  contentRef,
  actionMenuRef,
  sectionData,
}: UseReaderInteractivityProps) {
  // --- Tooltip State ---
  const [tooltipContent, setTooltipContent] = useState<React.ReactNode | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState<{ top: number; left: number; triggerTop?: number; triggerBottom?: number } | null>(null);
  const [isTooltipVisible, setIsTooltipVisible] = useState(false);

  // --- Action Menu State ---
  const [isActionMenuVisible, setIsActionMenuVisible] = useState(false);
  const [actionMenuPosition, setActionMenuPosition] = useState<{ top: number; left: number } | null>(null);
  const [selectedText, setSelectedText] = useState<string | null>(null);
  const [selectionRange, setSelectionRange] = useState<Range | null>(null);

  // --- Tooltip Callbacks ---
  const showTooltip = useCallback((content: React.ReactNode, targetElement: EventTarget) => {
    if (!(targetElement instanceof HTMLElement)) return;
    const rect = targetElement.getBoundingClientRect();
    const scrollY = window.scrollY || window.pageYOffset;
    const scrollX = window.scrollX || window.pageXOffset;
    if (targetElement.closest('.interactive-word') || targetElement.closest('.interactive-footnote') || targetElement.closest('.interactive-arabic-phrase')) {
      setIsActionMenuVisible(false);
    }
    setTooltipContent(content);
    setTooltipPosition({
      // Provide both trigger anchors for smarter placement
      top: rect.top + scrollY,
      left: rect.left + scrollX + rect.width / 2,
      triggerTop: rect.top + scrollY,
      triggerBottom: rect.bottom + scrollY,
    });
    setIsTooltipVisible(true);
  }, [setIsActionMenuVisible]);

  const hideTooltip = useCallback(() => {
    if (!isTooltipVisible) return;
    setIsTooltipVisible(false);
    setTooltipContent(null);
    setTooltipPosition(null);
  }, [isTooltipVisible]);

  // --- Tooltip Event Listener Effect ---
  useEffect(() => {
    const container = contentRef.current;
    if (!container) return;

    // Capture-phase guard to mark icon clicks so bubble handlers can skip tooltip
    const markIconClickCapture = (event: MouseEvent) => {
      const t = event.target as HTMLElement | null;
      if (t && t.closest && t.closest('svg.annotation-icon')) {
        (event as any).__annotationIconClicked = true;
      }
    };

    const handleClick = (event: MouseEvent) => {
      let clickedElement: HTMLElement | null = null;
      if (event.target instanceof HTMLElement) {
        clickedElement = event.target;
      } else if (event.target instanceof Node && (event.target as Node).parentElement) {
        // If target is a Node (e.g., text node), use its parent element
        clickedElement = (event.target as Node).parentElement;
      }

      if (!clickedElement) {
        // If we couldn't identify a valid HTML element that was clicked, 
        // hide tooltip if visible and do nothing further.
        if (isTooltipVisible) hideTooltip();
        return;
      }

      // 0. If click originated on an annotation icon, never show tooltip
      if ((event as any).__annotationIconClicked || clickedElement.closest('svg.annotation-icon')) {
        return;
      }

      // 1. Check for Arabic Phrase first
      const arabicPhraseElement = clickedElement.closest('.interactive-arabic-phrase') as HTMLElement | null;
      if (arabicPhraseElement) {
        event.stopPropagation();
        const rawTranslation = arabicPhraseElement.dataset.arabicTranslation;
        const translation = rawTranslation ? decodeURIComponent(rawTranslation) : "[Çeviri bulunamadı]";
        const tooltipDisplayContent = React.createElement('p', { className: "text-[var(--tooltip-foreground)]" }, translation);
        showTooltip(tooltipDisplayContent, arabicPhraseElement);
        return;
      }

      // 2. Check for Dictionary Word (only if not an Arabic Phrase context from above)
      const wordElement = clickedElement.closest('.interactive-word') as HTMLElement | null;
      if (wordElement) {
        const dictWord = wordElement.dataset.dictWord;
        if (dictWord) {
            event.stopPropagation();
            const dictionary = Array.isArray(sectionData?.content?.dictionary) ? sectionData.content.dictionary : [];
            const dictEntry = dictionary.find((item: RisaleDictionaryItem) => item.word === dictWord);
            if (dictEntry) {
              showTooltip(dictEntry.meaning, wordElement);
              return;
            }
        }
      }

      // 3. Check for Footnote
      const footnoteElement = clickedElement.closest('.interactive-footnote') as HTMLElement | null;
      if (footnoteElement) {
        const numStr = footnoteElement.dataset.footnoteNum;
        if (numStr) {
            event.stopPropagation();
            const footnotes = Array.isArray(sectionData?.content?.footnotes) ? sectionData.content.footnotes : [];
            const footnoteEntry = footnotes.find((item: RisaleFootnote) => String(item.number) === numStr);
            if (footnoteEntry) {
              showTooltip(footnoteEntry.content, footnoteElement);
              return;
            }
        }
      }

      // If nothing interactive was clicked (i.e., none of the above returned), and tooltip is visible, hide it.
      if (isTooltipVisible && !clickedElement.closest('.interactive-arabic-phrase, .interactive-word, .interactive-footnote')) {
         hideTooltip();
      }
    };

    const handleDocumentClickForTooltip = (event: MouseEvent) => {
        if (isTooltipVisible && contentRef.current && !contentRef.current.contains(event.target as Node)) {
             hideTooltip();
        }
    };

    container.addEventListener('click', markIconClickCapture, true);
    container.addEventListener('click', handleClick);
    document.addEventListener('click', handleDocumentClickForTooltip, true);

    return () => {
      container.removeEventListener('click', handleClick);
      container.removeEventListener('click', markIconClickCapture, true);
      document.removeEventListener('click', handleDocumentClickForTooltip, true);
    };
  }, [showTooltip, hideTooltip, sectionData, contentRef, isTooltipVisible]);

  // --- Action Menu Selection Handling Effect ---
  useEffect(() => {
    const handleMouseUp = (event: MouseEvent) => {
      // Allow time for potential double click to trigger tooltip
      setTimeout(() => {
        // If tooltip became visible during this timeout, don't show action menu
        if (isTooltipVisible) return;
        
        // Skip if clicked on an action menu button
        if (actionMenuRef.current?.contains(event.target as Node)) return;

        const selection = window.getSelection();
        if (selection && selection.toString().trim().length > 0 && contentRef.current && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const selectedTextContent = selection.toString().trim();

          // Check if the selection is within the content area
          const contentNode = contentRef.current;
          const rangeIsInContent = contentNode.contains(range.startContainer) && contentNode.contains(range.endContainer);

          if (rangeIsInContent) {
            const rect = range.getBoundingClientRect();
            
            // Don't show menu for very small selections (likely accidental clicks)
            if (rect.width < 5 && rect.height < 5) {
                // Ensure menu is hidden if selection is too small
                if (isActionMenuVisible) setIsActionMenuVisible(false);
                return;
            }

            const scrollX = window.scrollX || window.pageXOffset;
            const scrollY = window.scrollY || window.pageYOffset;

            // Calculate position (centered above selection)
            const menuTop = rect.top + scrollY; // Position above selection
            const menuLeft = rect.left + scrollX + rect.width / 2; // Center horizontally

            setSelectedText(selectedTextContent);
            setSelectionRange(range);
            setActionMenuPosition({ top: menuTop, left: menuLeft });
            setIsActionMenuVisible(true);
            
            // Ensure tooltip is hidden if menu appears
            hideTooltip(); 

          } else {
            // Selection ended outside the content area
            if (!actionMenuRef.current?.contains(event.target as Node)) {
              setIsActionMenuVisible(false);
            }
          }
        } else {
          // No text selected or selection cleared by click
          // Check if the focus is not inside the action menu before hiding
          if (actionMenuRef.current && !actionMenuRef.current.contains(document.activeElement) && 
              !actionMenuRef.current.contains(event.target as Node)) {
            setIsActionMenuVisible(false);
          }
        }
      }, 50); // Delay slightly
    };

    const handleMouseDown = (event: MouseEvent) => {
      // Don't hide the menu if clicking on a button inside it
      if (actionMenuRef.current?.contains(event.target as Node)) {
        return;
      }
      
      // Hide menu if clicking outside the menu itself AND outside the content area
      if (isActionMenuVisible && 
          actionMenuRef.current && 
          !actionMenuRef.current.contains(event.target as Node) &&
          contentRef.current && 
          !contentRef.current.contains(event.target as Node)
         ) {
         setIsActionMenuVisible(false);
      }
      // Hide tooltip on any mousedown - tooltip logic handles clicks on triggers
      if (isTooltipVisible) {
         hideTooltip();
      }
    };

    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mousedown', handleMouseDown); 

    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [isActionMenuVisible, isTooltipVisible, hideTooltip, contentRef, actionMenuRef]);

  // --- Return Values ---
  return {
    // Tooltip states
    isTooltipVisible,
    tooltipPosition,
    tooltipContent,
    hideTooltip, // Expose hideTooltip if needed externally (e.g., close button)
    
    // Action Menu states
    isActionMenuVisible,
    actionMenuPosition,
    selectedText,
    selectionRange,
    // Expose setter only if really needed externally
    setIsActionMenuVisible 
  };
} 