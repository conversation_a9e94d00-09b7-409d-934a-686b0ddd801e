import { useState, useEffect, useRef } from 'react';
import { fetchData } from '@shared/utils/dataFetcher';
import { TafsirBookIndexModule, Surah, Book } from '@reader/models/types';

/**
 * Custom hook to fetch and manage the index data for a Tafsir book.
 * Relies on the central caching mechanism provided by `fetchData`.
 * @param {string | undefined} bookId - ID of the Tafsir book
 */
export function useTafsirBook(bookId: string | undefined) {
  const [data, setData] = useState<TafsirBookIndexModule | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef<boolean>(true);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (!bookId) {
      setData(null);
      setError(null);
      setLoading(false);
      return;
    }

    const loadBookData = async () => {
      // Reset state
      setData(null);
      setError(null);
      setLoading(true);

      try {
        console.log(`[useTafsirBook] Loading data for book: ${bookId}`);
        // Load book metadata and surahs data in parallel
        const [libraryBooks, surahs] = await Promise.all([
          fetchData<Book[]>('library/books.json'),
          fetchData<Surah[]>('quran/surahs.json')
        ]);

        if (!isMountedRef.current) return;

        // Find the requested book
        const bookData = libraryBooks.find(book => String(book.id) === bookId && book.category_id === 4);
        if (!bookData) {
          throw new Error(`Tafsir book with ID ${bookId} not found.`);
        }

        // Transform surahs into tafsir surah definitions
        const tafsirSurahs = surahs.map(surah => ({
          id: String(surah.id),
          name: surah.name,
          arabic_name: surah.arabic_name,
          verse_count: surah.verse_count,
          revelation_place: surah.revelation_place
        }));

        // Construct the final book index structure
        const transformedBookData: TafsirBookIndexModule = {
          metadata: {
            id: String(bookData.id),
            title: bookData.title,
            author: bookData.author,
            description: '' // books.json doesn't have a description
          },
          structure: {
            surahs: tafsirSurahs
          }
        };

        console.log(`[useTafsirBook] Successfully loaded book data for ${bookId}`);
        if (isMountedRef.current) {
          setData(transformedBookData);
        }

      } catch (err) {
        console.error(`[useTafsirBook] Error loading tafsir book data for ${bookId}:`, err);
        if (isMountedRef.current) {
          setError(err instanceof Error ? err : new Error('An unknown error occurred while loading the book data.'));
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    };

    loadBookData();

  }, [bookId]); // Re-fetch when bookId changes

  return { data, loading, error };
}
