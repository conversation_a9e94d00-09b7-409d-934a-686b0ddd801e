import { useMemo } from 'react';
import { TafsirSentence } from '@reader/models/types';

// Processed sentence type for rendering - similar to RisaleSentence
export type ProcessedTafsirSentence = TafsirSentence & {
  processedText?: string;
  verseNumber?: number;
};

export type ProcessedTafsirFootnote = {
  id: string;
  text: string;
};

// A more generic type that can represent either a verse or just a collection of sentences (like an introduction)
type ProcessableTafsirContent = {
  sentences: TafsirSentence[];
  verse_no?: number;
  footnotes?: { number: string; content: string }[];
};


/**
 * Process Tafsir text for rendering
 * Unlike Risale processing, Tafsir has simpler structure with Arabic text and commentary
 */
function processText(arabicText?: string, text?: string): string {
  let processedText = '';
  
  // Add Arabic text if available
  if (arabicText) {
    processedText += arabicText;
  }
  
  // Add Turkish commentary/explanation if available  
  if (text) {
    if (processedText) {
      processedText += ' ';
    }
    processedText += text;
  }
  
  return processedText || '';
}

/**
 * Main hook for processing a single Tafsir verse for rendering
 */
export function useTafsirProcess(contentData: ProcessableTafsirContent | null) {
  const processedContent = useMemo(() => {
    if (!contentData) {
      return {
        sentences: [],
        footnotes: [],
      };
    }

    const sentences: ProcessedTafsirSentence[] = contentData.sentences.map((sentence) => ({
      ...sentence,
      processedText: processText(sentence.arabic_text, sentence.text),
      verseNumber: contentData.verse_no, // Will be undefined for introduction
    }));

    const footnotes: ProcessedTafsirFootnote[] = (contentData.footnotes || []).map((footnote) => ({
      id: `${contentData.verse_no || 0}-${footnote.number}`,
      text: footnote.content,
    }));
    
    return { sentences, footnotes };
  }, [contentData]);

  return { 
    processedSentences: processedContent.sentences,
    processedFootnotes: processedContent.footnotes,
  };
}
