import { useState, useEffect, useRef, useMemo } from 'react';
import { fetchData } from '@shared/utils/dataFetcher';
import { TafsirSurahStateData, TafsirContentData, Surah, Book } from '@reader/models/types';
import { getTafsirMappingsForBook } from '@shared/utils/tafsirFileMapping';

/**
 * Custom hook to fetch and manage Tafsir surah data.
 * The hook relies on the central caching mechanism provided by `fetchData`.
 * @param {string | undefined} bookId - ID of the Tafsir book
 * @param {string | undefined} surahId - ID of the Surah
 * @param {boolean} enabled - Flag to enable or disable the hook's execution
 */
export function useTafsirSurah(bookId?: string, surahId?: string, enabled: boolean = true) {
  const [data, setData] = useState<TafsirSurahStateData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const isMountedRef = useRef(true);

  // Effect to handle component mount and unmount status
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (!bookId || !surahId || !enabled) {
      setLoading(false);
      return;
    }

    const loadSurahData = async () => {
      // Reset state on new load
      setData(null);
      setError(null);
      setLoading(true);

      try {
        const dataPath = `tafsir_${bookId}_${surahId}`;
        console.log(`[useTafsirSurah] Loading data for ${dataPath}`);

        // Step 1: Fetch book metadata and surahs list in parallel
        const [libraryBooks, allSurahs] = await Promise.all([
          fetchData<Book[]>('library/books.json'),
          fetchData<Surah[]>('quran/surahs.json')
        ]);

        // Step 2: Find the correct file(s) using the dynamic mapping system
        const fileMappings = await getTafsirMappingsForBook(bookId);
        const surahMappings = fileMappings.filter(mapping => mapping.surahId === parseInt(surahId, 10));

        if (surahMappings.length === 0) {
          throw new Error(`No file mapping found for Surah ${surahId} in Book ${bookId}.`);
        }

        const accumulatedVerses: any[] = [];
        const introductionSentences: any[] = [];

        // Load content from all mapped files for the surah
        const contentPromises = surahMappings.map(async (mapping) => {
          try {
            const contentPath = `tafsirs/${bookId}/${mapping.fileName}`;
            const fileData = await fetchData<any[]>(contentPath);
            const surahData = fileData.find((surah: any) => surah.surah_no === mapping.surahId);

            if (surahData?.verses) {
              const introVerse = surahData.verses.find((v: any) => v.verse_no === 0);
              if (introVerse?.sentences) {
                introductionSentences.push(...introVerse.sentences);
              }
              const surahVerses = surahData.verses.filter((verse: any) =>
                verse.verse_no >= mapping.startVerse && verse.verse_no <= mapping.endVerse && verse.verse_no !== 0
              );
              accumulatedVerses.push(...surahVerses);
            }
          } catch (error) {
            console.warn(`[useTafsirSurah] Failed to load or process ${mapping.fileName}:`, error);
          }
        });

        await Promise.all(contentPromises);
        
        if (accumulatedVerses.length === 0 && introductionSentences.length === 0) {
          throw new Error(`Tafsir content for Surah ${surahId} could not be found in any of the specified files.`);
        }

        accumulatedVerses.sort((a, b) => a.verse_no - b.verse_no);
        const surahContentData: TafsirContentData = {
          introduction: introductionSentences.length > 0 ? introductionSentences : undefined,
          verses: accumulatedVerses
        };

        // Step 3: Find the book data
        const bookData = libraryBooks.find(book => String(book.id) === bookId);
        if (!bookData) throw new Error(`Tafsir book with ID ${bookId} not found.`);

        // Step 4: Map surahs and find navigation data (current, prev, next)
        const tafsirSurahs = allSurahs.map(surah => ({
          id: String(surah.id),
          name: surah.name,
          arabic_name: surah.arabic_name,
          verse_count: surah.verse_count,
          revelation_place: surah.revelation_place,
        }));

        const currentIndex = tafsirSurahs.findIndex(s => s.id === surahId);
        if (currentIndex === -1) throw new Error(`Surah ID ${surahId} not found in the surah list.`);

        const currentSurah = tafsirSurahs[currentIndex];
        const prevSurah = currentIndex > 0 ? tafsirSurahs[currentIndex - 1] : null;
        const nextSurah = currentIndex < tafsirSurahs.length - 1 ? tafsirSurahs[currentIndex + 1] : null;

        const resultData: TafsirSurahStateData = {
          structure: {
            surahs: tafsirSurahs,
            current: currentSurah,
            prevSurah,
            nextSurah,
          },
          content: surahContentData,
          book: bookData,
          title: currentSurah.name,
        };

        if (isMountedRef.current) {
          setData(resultData);
        }

      } catch (err) {
        console.error(`[useTafsirSurah] Error loading surah data for book ${bookId}, surah ${surahId}:`, err);
        if (isMountedRef.current) {
          setError(err instanceof Error ? err : new Error('An unknown error occurred'));
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    };

    loadSurahData();

  }, [bookId, surahId, enabled]);

  return { data, loading, error };
}
