import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { fetchData } from '@shared/utils/dataFetcher';
import { TafsirSurahDef, Book, Surah } from '@reader/models/types';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';

interface UseTafsirNavigationSheetOptions {
  currentBookId: string | undefined;
  currentSurahId: string | undefined;
  initialBookTitle: string | undefined;
  totalVerseCount?: number;
  hasIntroduction?: boolean;
}

export function useTafsirNavigationSheet({
  currentBookId,
  currentSurahId,
  initialBookTitle,
  totalVerseCount = 0,
  hasIntroduction = false
}: UseTafsirNavigationSheetOptions) {
  const [isOpen, setIsOpen] = useState(false);
  const [navigationType, setNavigationType] = useState<'surah' | 'books' | 'verse'>('surah');
  const [surahSearch, setSurahSearch] = useState('');
  const [bookSearch, setBookSearch] = useState('');
  const [verseSearch, setVerseSearch] = useState('');
  const [allBooks, setAllBooks] = useState<Book[]>([]);
  
  const [selectedBookId, setSelectedBookId] = useState<string | null>(currentBookId || null);
  const [selectedBookSurahs, setSelectedBookSurahs] = useState<TafsirSurahDef[]>([]);
  const [isLoadingSurahs, setIsLoadingSurahs] = useState(false);
  
  const navigate = useNavigate();


  useEffect(() => {
    const fetchBooks = async () => {
      try {
        const allBooksData = await fetchData<Book[]>('/library/books.json');
        
        // Filter for tafsir books (category_id: 4)
        const tafsirBooks = allBooksData.filter(book => book.category_id === 4);
        
        setAllBooks(tafsirBooks);
      } catch (error) {
        console.error("Failed to fetch library books:", error);
      }
    };
    fetchBooks();
  }, [currentBookId]);
  
  useEffect(() => {
    // When the page's own book ID changes (e.g., URL navigation), reset the sheet's state.
    setSelectedBookId(currentBookId || null);
  }, [currentBookId, initialBookTitle]);
  
  useEffect(() => {
    if (!selectedBookId) {
      setSelectedBookSurahs([]);
      return;
    }
    
    const loadSelectedBookSurahs = async () => {
      setIsLoadingSurahs(true);
      try {
        // Load surahs data
        const surahs = await fetchData<Surah[]>('/quran/surahs.json');
        
        // Find the book data from the already filtered list
        const bookData = allBooks.find(book => String(book.id) === selectedBookId);
        if (bookData) {
          
          // Transform surahs to tafsir surah format
          const transformedSurahs = surahs.map(surah => ({
            id: String(surah.id),
            name: surah.name,
            arabic_name: surah.arabic_name,
            verse_count: surah.verse_count,
            revelation_place: surah.revelation_place,
          })) as TafsirSurahDef[];

          setSelectedBookSurahs(transformedSurahs);
        } else {
          // This might happen on initial load if `allBooks` isn't ready.
          // The effect will re-run when `allBooks` is populated.
        }

      } catch (err) {
        console.error(`Error loading surahs for tafsir book ${selectedBookId}:`, err);
        setSelectedBookSurahs([]);
      } finally {
        setIsLoadingSurahs(false);
      }
    };

    if (allBooks.length > 0) {
      loadSelectedBookSurahs();
    }
  }, [selectedBookId, allBooks, initialBookTitle]);
  
  const handleSurahNavigation = (surahId: string, navigateToPage: boolean = true) => {
    if (!selectedBookId) {
      console.error('selectedBookId is null');
      return;
    }

    if (navigateToPage) {
      // Navigate to the page
      const url = `/tefsir/${selectedBookId}/${surahId}/0`;
      navigate(url);
      window.scrollTo({ top: 0, behavior: 'smooth' });
      setIsOpen(false);
    }
    // For non-navigation case, the caller (ReadPage.tsx) will handle the state update
  };

  const handleVerseNavigation = (verseNumber: number) => {
    if (!currentBookId || !currentSurahId) {
      console.error('Missing currentBookId or currentSurahId:', { currentBookId, currentSurahId });
      return;
    }
    const url = `/tefsir/${currentBookId}/${currentSurahId}/${verseNumber}`;
    navigate(url);
    setIsOpen(false);
  };
  
  const switchBookInSheet = (bookId: number) => {
    setSelectedBookId(String(bookId));
    // Title will be set by the useEffect when data is fetched.
    // Navigation type doesn't need to be changed, user stays on current tab
  };
  
  const filteredSurahs = useMemo(() => {
    return filterByNormalizedQuery(selectedBookSurahs, surahSearch, (surah) => [
      surah.name,
      surah.arabic_name || ''
    ]);
  }, [selectedBookSurahs, surahSearch]);

  const filteredVerses = useMemo(() => {
    // This is kept for compatibility but not used in NavigationSheet anymore
    // NavigationSheet now calculates its own verses based on currentSurah data

    let verseCount = totalVerseCount;
    let shouldHaveIntroduction = hasIntroduction;

    if (currentSurahId) {
      const surahNum = parseInt(currentSurahId);

      // If we don't have accurate data, use estimates
      if (verseCount === 0) {
        // Estimate verse count based on surah ID
        if (surahNum <= 2) verseCount = surahNum === 1 ? 7 : 286; // Fatiha: 7, Bakara: 286
        else if (surahNum <= 4) verseCount = 200; // Roughly 200 for early surahs
        else verseCount = 50; // Roughly 50 for later surahs
      }

      // Estimate introduction based on surah number (some tafsirs have introductions)
      if (surahNum <= 5) {
        shouldHaveIntroduction = true; // Early surahs often have introductions
      }
    }

    const verseList = Array.from({ length: Math.max(verseCount, 1) }, (_, i) => i + 1);
    if (shouldHaveIntroduction) {
      verseList.unshift(0); // Add introduction as verse 0
    }

    const searchLower = verseSearch.toLowerCase();
    if (!searchLower) {
      return verseList;
    }

    return verseList.filter(v => String(v).includes(searchLower) || (v === 0 && 'giriş'.includes(searchLower)));
  }, [totalVerseCount, verseSearch, hasIntroduction, currentSurahId]);


  const openNavigationSheet = (type: 'surah' | 'books' | 'verse' = 'surah') => {
    setNavigationType(type);
    setIsOpen(true);
  };

  return {
    isOpen,
    setIsOpen,
    navigationType,
    setNavigationType,
    surahSearch,
    setSurahSearch,
    bookSearch,
    setBookSearch,
    verseSearch,
    setVerseSearch,
    handleSurahNavigation,
    handleVerseNavigation,
    switchBookInSheet,
    filteredSurahs,
    filteredVerses,
    openNavigationSheet,
    isLoadingSurahs,
    books: allBooks,
    selectedBookId,
  };
}
