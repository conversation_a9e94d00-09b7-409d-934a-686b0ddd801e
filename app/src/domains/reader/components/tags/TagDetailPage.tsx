import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import parse from 'html-react-parser';
import { Tag as TagIcon } from 'lucide-react';
import { useTag, TagSentence } from '../../hooks/useTags';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { PageLayout, EmptyState, LoadingState } from '@shared/components';
import { SearchBar } from '@shared/components/atoms/Input/SearchBar';
import { useBookService } from '@domains/library/hooks/useBookService';
import { fetchData } from '@shared/utils/dataFetcher';

interface TagDetailPageProps {
  tagId: number;
  onBack: () => void;
  onSentenceClick?: (sentence: TagSentence) => void;
}

export const TagDetailPage: React.FC<TagDetailPageProps> = ({
  tagId,
  onBack,
  onSentenceClick
}) => {
  const { tag, loading, error } = useTag(tagId);
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  // AutoOverlay renkler
  const cardBg = useAutoOverlay(4, 'var(--bg-color)');
  const borderColor = useAutoOverlay(15, 'var(--bg-color)');
  const sentenceBg = useAutoOverlay(8, 'var(--bg-color)');
  const dividerColor = useAutoOverlay(14, 'var(--bg-color)');

  // Books and sections mapping
  const { books } = useBookService();
  const bookTitleMap = useMemo(() => {
    const map: Record<string, string> = {};
    (books || []).forEach((b: any) => { map[String(b.id)] = b.title; });
    return map;
  }, [books]);
  const [sectionTitleMap, setSectionTitleMap] = useState<Record<string, Record<string, string>>>({});
  const [loadedBookIds, setLoadedBookIds] = useState<Set<string>>(new Set());

  // Parse sentence id like "12_19_23" => { bookId: '12', sectionId: '19' }
  const parseSentenceKey = (sid?: string): { bookId?: string; sectionId?: string } => {
    if (!sid) return {};
    const parts = sid.split('_');
    if (parts.length < 2) return {};
    const [bookId, sectionId] = parts;
    return { bookId, sectionId };
  };

  // Prefetch section titles for all involved books
  useEffect(() => {
    const sentences = tag?.sentences || [];
    const uniqueBookIds = Array.from(new Set(sentences.map(s => parseSentenceKey(s.id).bookId).filter(Boolean))) as string[];
    if (uniqueBookIds.length === 0) return;

    const toLoad = uniqueBookIds.filter(bid => !loadedBookIds.has(bid));
    if (toLoad.length === 0) return;

    let isActive = true;
  const loadForBook = async (bookId: string) => {
      try {
    const raw = await fetchData<any>(`risalei_nur/${bookId}/index.json`);
        const sections = raw?.sections || raw?.subsections || [];
        const map: Record<string, string> = {};
        sections.forEach((s: any) => { if (s?.id != null) map[String(s.id)] = String(s.title ?? s.name ?? s.id); });
        if (!isActive) return;
        setSectionTitleMap(prev => ({ ...prev, [bookId]: map }));
        setLoadedBookIds(prev => new Set(prev).add(bookId));
      } catch {
        /* noop */
      }
    };

    toLoad.forEach(loadForBook);
    return () => { isActive = false; };
  }, [tag?.sentences, loadedBookIds]);

  // Pretty printing for section titles when index.json titles are missing/only numeric
  const inferUnitForBook = (bookTitle: string) => {
    const t = bookTitle.toLowerCase();
    if (t.includes('lem')) return "Lem'a";
    if (t.includes('söz')) return 'Söz';
    if (t.includes('mektub') || t.includes('mektup')) return 'Mektup';
    if (t.includes('şua') || t.includes('sua')) return 'Şua';
    return '';
  };

  const formatSectionTitle = (bookTitle: string | undefined, sectionId: string, provided?: string) => {
    const providedTrim = (provided ?? '').trim();
    // If provided title is non-empty and not purely numeric/same as id, use it
    if (providedTrim && providedTrim !== sectionId && !/^\d+$/.test(providedTrim)) return providedTrim;
    if (!bookTitle) return sectionId;
    const unit = inferUnitForBook(bookTitle);
    return unit ? `${sectionId}. ${unit}` : sectionId;
  };

  const getLocationParts = (sid: string | undefined): { bookId?: string; sectionId?: string; bookTitle?: string; sectionTitle?: string } => {
    if (!sid) return {};
    const { bookId, sectionId } = parseSentenceKey(sid);
    if (!bookId || !sectionId) return {};
    const bookTitle = bookTitleMap[bookId] || String(bookId);
    const fullSectionId = `${bookId}_${sectionId}`;
    const provided = sectionTitleMap[bookId]?.[fullSectionId];
    return {
      bookId,
      sectionId: fullSectionId,
      bookTitle,
      sectionTitle: formatSectionTitle(bookTitle, sectionId, provided)
    };
  };
  const navigateToReference = (sid: string | undefined) => {
    const { bookId, sectionId } = parseSentenceKey(sid || '');
    if (!bookId || !sectionId || !sid) return;
    const fullSectionId = `${bookId}_${sectionId}`;
    // Close the overlay first so navigation feels correct
    try { onBack(); } catch {}
    navigate(`/risale/${bookId}/${fullSectionId}?sentence=${encodeURIComponent(sid)}`);
  };

  // Arabic enhancer: wrap Arabic character runs with span.arabic-text
  const renderArabicEnhanced = (html: string) => {
    const arabicRegex = /([\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]+)/gu;

    return parse(html, {
      replace: (domNode: any) => {
        if (domNode && domNode.type === 'text' && typeof domNode.data === 'string') {
          const text = domNode.data;
          if (!arabicRegex.test(text)) return undefined;

          const parts = text.split(arabicRegex);
          if (parts.length <= 1) return undefined;

          return (
            <>
              {parts.map((part: string, idx: number) => {
                if (!part) return null;
                if (arabicRegex.test(part)) {
                  return (
                    <span key={idx} className="arabic-text">
                      {part}
                    </span>
                  );
                }
                return <span key={idx}>{part}</span>;
              })}
            </>
          );
        }
        return undefined;
      }
    });
  };

  // Filter and sort sentences
  const filteredSentences = useMemo(() => {
    if (!tag?.sentences) return [];

    let filtered = tag.sentences;

    // Search filter only
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(sentence => sentence.text.toLowerCase().includes(query));
    }

    return filtered;
  }, [tag?.sentences, searchQuery]);

  const handleSentenceClick = (sentence: TagSentence) => {
    if (onSentenceClick) {
      onSentenceClick(sentence);
    }
  };

  if (loading) {
    return (
      <PageLayout onBackClick={onBack}>
        <LoadingState message="Etiket yükleniyor..." />
      </PageLayout>
    );
  }

  if (error || !tag) {
    return (
      <PageLayout onBackClick={onBack}>
        <div className="flex items-center justify-center h-[60vh] px-4">
          <EmptyState 
            message={typeof error === 'string' ? error : 'Etiket bulunamadı'}
          />
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout showNavbar={false}>
      <div className="px-4 pt-4 pb-8 space-y-4 max-w-5xl mx-auto">
        {/* Header */}
        <div className="flex flex-col gap-3 items-center text-center">
          <div className="flex items-center gap-2">
            <TagIcon size={20} className="opacity-90" style={{ color: 'var(--text-color)' }} />
            <h1 className="m-0 text-2xl md:text-3xl font-bold tracking-tight" style={{ color: 'var(--text-color)' }}>
              {tag.name}
            </h1>
            <span
              className="px-2 py-0.5 text-[11px] rounded-full"
              style={{ backgroundColor: cardBg, color: 'var(--text-color)', opacity: 0.85 }}
            >
              {tag.count}
            </span>
          </div>
          <div className="flex items-center gap-2 w-full max-w-2xl">
            <SearchBar
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="Metinlerde ara..."
              className="flex-1"
            />
          </div>
        </div>
        <div className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
          {searchQuery ? (
            <>
              <span className="font-medium">{filteredSentences.length}</span> sonuç
              {filteredSentences.length !== tag.count && (
                <span className="ml-1">(toplam {tag.count})</span>
              )}
            </>
          ) : (
            <>Toplam <span className="font-medium">{tag.count}</span> metin</>
          )}
        </div>

        {/* Sentences List */}
        {filteredSentences.length > 0 ? (
          <div className="space-y-3 risale-content">
            {filteredSentences.map((sentence) => (
              <div
                key={sentence.id}
                className="p-4 rounded-xl border cursor-pointer transition-colors hover:bg-[var(--text-color)]/5"
                style={{
                  backgroundColor: sentenceBg,
                  borderColor: borderColor
                }}
                onClick={() => handleSentenceClick(sentence)}
              >
                <div className="flex-1 min-w-0">
                  <div className="leading-relaxed mb-2">{renderArabicEnhanced(sentence.text)}</div>
                  {/* Footer: source link like SavedContentPage */}
                  {(() => {
                    const parts = getLocationParts(sentence.id);
                    if (!parts.bookId || !parts.sectionId) return null;
                    const title = `${parts.bookTitle} / ${parts.sectionTitle}`;
                    return (
                      <div className="flex items-center mt-3 pt-2 relative">
                        <div
                          aria-hidden="true"
                          className="absolute left-0 right-0 top-0 h-px"
                          style={{ backgroundColor: dividerColor }}
                        />
                        <button
                          onClick={(e) => { e.stopPropagation(); navigateToReference(sentence.id); }}
                          className="ml-auto pl-3 text-xs text-[var(--text-color)]/70 text-right leading-snug hover:text-[var(--color-primary)] transition-colors truncate underline-offset-2 hover:underline inline-flex items-center gap-1 relative"
                          title={title}
                        >
                          <span className="truncate max-w-[14rem]">{title}</span>
                          <svg width="14" height="14" viewBox="0 0 24 24" className="opacity-70 group-hover:opacity-100 transition-opacity" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round"><path d="M7 17L17 7" /><path d="M7 7h10v10"/></svg>
                        </button>
                      </div>
                    );
                  })()}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center justify-center h-[50vh]">
            <EmptyState 
              message={searchQuery ? 'Sonuç bulunamadı' : 'Bu etiket için metin bulunamadı'}
              action={searchQuery ? (
                <button
                  onClick={() => setSearchQuery('')}
                  className="px-4 py-2 rounded"
                  style={{ backgroundColor: cardBg, color: 'var(--text-color)' }}
                >
                  Aramayı temizle
                </button>
              ) : undefined}
            />
          </div>
        )}
      </div>
    </PageLayout>
  );
};
