import React from 'react';
import { Tag } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface TagBadgeProps {
  id: number;
  name: string;
  size?: 'sm' | 'md' | 'lg';
  onClick?: (tagId: number, tagName: string) => void;
  className?: string;
}

export const TagBadge: React.FC<TagBadgeProps> = ({
  id,
  name,
  size = 'sm',
  onClick,
  className = ''
}) => {
  // Mevcut tasarım sistemine uygun renkler
  const bgColor = useAutoOverlay(8, 'var(--bg-color)');
  const hoverBgColor = useAutoOverlay(12, 'var(--bg-color)');
  const borderColor = useAutoOverlay(15, 'var(--bg-color)');

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-xs gap-1.5',
    md: 'px-3 py-2 text-sm gap-2',
    lg: 'px-4 py-2.5 text-base gap-2'
  };

  const iconSizes = {
    sm: 12,
    md: 14,
    lg: 16
  };

  const handleClick = () => {
    if (onClick) {
      onClick(id, name);
    }
  };

  return (
    <button
      onClick={handleClick}
      className={`
        inline-flex items-center rounded-lg font-medium
        transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20
        ${sizeClasses[size]}
        ${onClick ? 'cursor-pointer hover:scale-[1.02] active:scale-[0.98]' : 'cursor-default'}
        ${className}
      `}
      style={{
        backgroundColor: bgColor,
        borderColor: borderColor,
        color: 'var(--text-color)',
        border: `1px solid ${borderColor}`
      }}
      onMouseEnter={(e) => {
        if (onClick) {
          e.currentTarget.style.backgroundColor = hoverBgColor;
          e.currentTarget.style.transform = 'scale(1.02)';
        }
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = bgColor;
        e.currentTarget.style.transform = 'scale(1)';
      }}
      disabled={!onClick}
      title={`Etiket: ${name}`}
    >
      <Tag size={iconSizes[size]} className="flex-shrink-0" />
      <span className="truncate max-w-[140px]">{name}</span>
    </button>
  );
};
