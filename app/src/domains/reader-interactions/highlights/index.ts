/**
 * 🚀 Highlights Domain - Basit ve Etkili
 * Üstte olan uygulansın!
 */

// 🎯 CURRENT SYSTEM
export { default as TextHighlighter, TextHighlighterStyles } from './components/TextHighlighter';

// 📝 Types
export type { Annotation } from './types/highlight.types';

// 🔄 LEGACY SUPPORT - Backward compatibility
export { AnnotationHighlighter } from './components/AnnotationHighlighter';
export { AnnotationVisualizer } from './components/AnnotationVisualizer';
export { default as ColorPicker } from './components/ColorPicker';
export { AnnotationHighlighterStyles } from './components/AnnotationHighlighter';

// Legacy types (re-export from shared)
export type {
  VisualStyle,
  AnnotationType
} from '../shared/types';
