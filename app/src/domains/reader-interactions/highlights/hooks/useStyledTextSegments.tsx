/**
 * 🚀 useStyledTextSegments Hook - Text Highlighter için tüm karmaşık mantığı yönetir
 * Sorumluluk: Ham metin ve annotation'ları alıp, render'a hazır, stillendirilmiş JSX segmentleri döndürür.
 */
import React, { useMemo, useEffect, useState } from 'react';
import type { Annotation } from '../types/highlight.types';
import { useTheme } from '@shared/context/ThemeContext';
import { resolveColorIdentifier } from '@shared/color/hooks/colorIdentifiers';

// Bu hook içinde kullanılan, genişletilmiş geçici bir tip.
// Multi-sentence annotation'ların orijinal bilgilerini taşır.
type AdjustedAnnotation = Annotation & {
  _original_sentence_ids?: string[];
  _original_selection_start?: number;
  _original_selection_end?: number;
  _original_selected_text?: string;
};

function getIconSvgString(annotation: AdjustedAnnotation, color: string, isAutoOverlay: boolean = false): string {
  const type = annotation.annotation_type;
  const id = annotation.id;
  const strokeWidth = isAutoOverlay ? '3' : '2'; // AUTO_OVERLAY'de kalın çizgi
  // Accessible label
  const ariaLabel = type === 'note'
    ? 'Not detayı'
    : type === 'sherh'
      ? 'Şerh detayı'
      : type === 'highlight'
        ? 'Vurgu detayı'
        : type === 'bookmark'
          ? 'Yer imi detayı'
          : 'Detayı aç';
  // Add data-annotation-id directly to the SVG element with a larger tap target and a11y attrs
  const baseProps = `xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="${strokeWidth}" stroke-linecap="round" stroke-linejoin="round" class="annotation-icon" role="button" tabindex="0" aria-label="${ariaLabel}" title="${ariaLabel}" style="box-shadow: none; vertical-align: middle; display: inline-block; position: relative; top: -1px; cursor: pointer; line-height: 0; padding: 4px; margin: -4px 2px -4px 3px; border-radius: 6px; background: transparent;" data-annotation-id="${id}"`;

  switch (type) {
    case 'note':
      return `<svg ${baseProps}><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><line x1="10" y1="9" x2="8" y2="9"/></svg>`;
    case 'sherh':
      return `<svg ${baseProps}><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>`;
    case 'bookmark':
      return `<svg ${baseProps}><path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/></svg>`;
    case 'highlight':
      // Kalem/pencil tarzı ikon (stroke tabanlı), metne girmeyecek hizalama ve marjin
      const hlProps = `xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="annotation-icon" style="vertical-align: middle; margin-left: 6px; margin-right: 2px; display: inline-block; position: relative; top: 0; cursor: pointer; line-height: 0; padding: 4px; margin: -4px 2px -4px 6px; border-radius: 6px; background: transparent;" data-annotation-id="${id}"`;
      // Lucide tarzı "edit-3" kalem formuna benzer: bir ana path ve alt çizgi
      return `<svg ${hlProps}><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"/><path d="M12 20h9"/></svg>`;
    default:
      return '';
  }
}

/**
 * Background highlight için optimized opacity ekleme
 */
function applyBackgroundOpacity(color: string): string {
  if (color.startsWith('hsl(') && color.endsWith(')')) {
    // HSL'i HSLA'ya çevir: hsl(213, 72%, 35%) -> hsla(213, 72%, 35%, 0.4)
    const hslContent = color.slice(4, -1);
    return `hsla(${hslContent}, 0.4)`;
  } else if (color.startsWith('#')) {
    // HEX color için hex opacity ekle (40% = 66 in hex)
    return color + '66';
  }
  return color;
}

// Debug mode - production'da kapalı
const DEBUG_MODE = process.env.NODE_ENV !== 'production';

/**
 * 🛡️ Sentence ID validation - geçersiz ID'leri filtreler
 */
function isValidSentenceId(sentenceId: string): boolean {
  if (!sentenceId || typeof sentenceId !== 'string') {
    return false;
  }

  // Geçersiz pattern'leri kontrol et
  const invalidPatterns = [
    /^invalid-sentence-/,  // "invalid-sentence-" ile başlayanlar
    /^temp-/,              // "temp-" ile başlayanlar
    /^placeholder-/,       // "placeholder-" ile başlayanlar
    /^error-/,             // "error-" ile başlayanlar
  ];

  // Herhangi bir geçersiz pattern eşleşirse false döndür
  for (const pattern of invalidPatterns) {
    if (pattern.test(sentenceId)) {
      return false;
    }
  }

  // Minimum uzunluk kontrolü
  if (sentenceId.length < 3) {
    return false;
  }

  // Maksimum uzunluk kontrolü (çok uzun ID'ler de şüpheli)
  if (sentenceId.length > 50) {
    return false;
  }

  // Geçerli format kontrolü - örnek: "9_1_13" formatı
  const validFormatPattern = /^\d+_\d+_\d+$/;
  if (validFormatPattern.test(sentenceId)) {
    return true;
  }

  // Diğer geçerli formatlar için ek kontroller eklenebilir
  // Şimdilik sadece sayı_sayı_sayı formatını kabul ediyoruz
  return false;
}

/**
 * 🔍 Arapça phrase detection - CSS class'larını tespit eder
 */
function detectArabicPhrases(htmlText: string): Array<{
  plainStart: number;
  plainEnd: number;
  fontSize: string;
  textDecoration: string;
  textDecorationStyle: string;
  textDecorationColor: string;
  textUnderlineOffset: string;
  translation: string;
}> {
  const arabicPhrases: Array<{
    plainStart: number;
    plainEnd: number;
    fontSize: string;
    textDecoration: string;
    textDecorationStyle: string;
    textDecorationColor: string;
    textUnderlineOffset: string;
    translation: string;
  }> = [];

  // interactive-arabic-phrase class'ını içeren span'ları bul
  const arabicRegex = /<span[^>]*class="[^"]*interactive-arabic-phrase[^"]*"[^>]*data-arabic-translation="([^"]*)"[^>]*>(.*?)<\/span>/gi;
  let match;

  // Önce pozisyon mapping'i oluştur
  const tempMap = createPositionMapSimple(htmlText);

  while ((match = arabicRegex.exec(htmlText)) !== null) {
    const fullMatch = match[0];
    const translation = match[1]; // data-arabic-translation değeri
    const content = match[2]; // span içeriği
    const htmlStartIndex = match.index;

    // Span tag'inin içindeki content'in başlangıç pozisyonunu bul
    const contentStartInHtml = htmlStartIndex + fullMatch.indexOf(content);
    const contentEndInHtml = contentStartInHtml + content.length;

    // HTML pozisyonlarını plain text pozisyonlarına çevir
    const plainStart = tempMap.htmlToPlain[contentStartInHtml] || 0;
    const plainEnd = tempMap.htmlToPlain[contentEndInHtml - 1] + 1 || tempMap.plainText.length;

    arabicPhrases.push({
      plainStart,
      plainEnd,
      fontSize: '170%', // CSS'teki değer (font-size: 170%)
      textDecoration: 'underline',
      textDecorationStyle: 'dotted',
      textDecorationColor: 'var(--text-color)',
      textUnderlineOffset: '6px',
      translation: decodeURIComponent(translation) // 🎯 Translation'ı decode et
    });
  }

  return arabicPhrases;
}

/**
 * 🔧 Basit pozisyon mapping (detection için) - BR tag'leri newline olarak işlenir
 */
function createPositionMapSimple(htmlText: string): {
  htmlToPlain: number[];
  plainText: string;
} {
  const htmlToPlain: number[] = [];
  let htmlIndex = 0;
  let plainIndex = 0;
  let plainText = '';

  while (htmlIndex < htmlText.length) {
    const char = htmlText[htmlIndex];

    if (char === '<') {
      const tagEnd = htmlText.indexOf('>', htmlIndex);
      if (tagEnd !== -1) {
        const tagContent = htmlText.substring(htmlIndex, tagEnd + 1);

        // 🔧 BR tag'i için özel işlem - newline karakteri ekle
        if (tagContent.toLowerCase().includes('<br')) {
          // BR tag'ini plain text'te newline olarak temsil et
          htmlToPlain[htmlIndex] = plainIndex;
          plainText += '\n'; // BR tag'ini newline karakteri olarak ekle
          plainIndex++;

          // Tag'in geri kalanını aynı pozisyona map'le
          for (let i = htmlIndex + 1; i <= tagEnd; i++) {
            htmlToPlain[i] = plainIndex - 1;
          }
        } else {
          // Diğer tag'ler için normal işlem
          for (let i = htmlIndex; i <= tagEnd; i++) {
            htmlToPlain[i] = plainIndex;
          }
        }
        htmlIndex = tagEnd + 1;
      } else {
        htmlIndex++;
      }
    } else {
      htmlToPlain[htmlIndex] = plainIndex;
      plainText += char;
      plainIndex++;
      htmlIndex++;
    }
  }

  return { htmlToPlain, plainText };
}

/**
 * 🎯 HTML-aware pozisyon hesaplama - v2 (Daha Sağlam)
 * Bu versiyon, HTML tag'lerini düzgün bir şekilde ele alarak,
 * plain text pozisyonlarının orijinal HTML'deki doğru başlangıç noktalarına
 * (tag'ler dahil) map edilmesini sağlar.
 */
function createPositionMap(htmlText: string): {
  htmlToPlain: number[];
  plainToHtml: number[];
  plainText: string;
  arabicPhrases: Array<{
    plainStart: number;
    plainEnd: number;
    fontSize: string;
    textDecoration: string;
    textDecorationStyle: string;
    textDecorationColor: string;
    textUnderlineOffset: string;
    translation: string;
  }>;
} {
  const htmlToPlain: number[] = new Array(htmlText.length);
  const plainToHtml: number[] = [];
  let plainText = '';
  let htmlIndex = 0;
  let lastHtmlIndex = 0;

  while (htmlIndex < htmlText.length) {
    const char = htmlText[htmlIndex];

    if (char === '<') {
      const tagEnd = htmlText.indexOf('>', htmlIndex);
      if (tagEnd !== -1) {
        // BR tag'i için özel işlem
        if (htmlText.substring(htmlIndex, tagEnd + 1).toLowerCase().includes('<br')) {
            const plainIndex = plainText.length;
            // Önceki metin parçasını map'le
            for (let i = lastHtmlIndex; i < htmlIndex; i++) {
                htmlToPlain[i] = plainIndex;
            }
            plainToHtml[plainIndex] = lastHtmlIndex;
            plainText += '\n';
            
            // BR tag'ini de bu newline karakterine map'le
            for (let i = htmlIndex; i <= tagEnd; i++) {
                htmlToPlain[i] = plainIndex;
            }
            
            htmlIndex = tagEnd + 1;
            lastHtmlIndex = htmlIndex;
            continue;
        }
        // Diğer tag'ler için sadece atla
        htmlIndex = tagEnd + 1;
        continue;
      }
    }

    // Düz metin karakteri bulundu
    const plainIndex = plainText.length;
    
    // Bu düz metin karakterinin HTML başlangıcı, son atlanan tag'den hemen sonradır.
    plainToHtml[plainIndex] = lastHtmlIndex;
    
    // lastHtmlIndex'ten mevcut karaktere kadar olan tüm HTML karakterlerini
    // bu düz metin index'ine map'le. Bu, tag'leri de kapsar.
    for (let i = lastHtmlIndex; i <= htmlIndex; i++) {
      htmlToPlain[i] = plainIndex;
    }
    
    plainText += char;
    htmlIndex++;
    lastHtmlIndex = htmlIndex;
  }

  // Arapça phrase'leri tespit et
  const arabicPhrases = detectArabicPhrases(htmlText);

  return { htmlToPlain, plainToHtml, plainText, arabicPhrases };
}

/**
 * 🎯 Hybrid Pozisyon Bulma - 3 Aşamalı Fallback
 */
function findTextPosition(
  fullText: string,
  annotation: Annotation
): { start: number; end: number; confidence: 'high' | 'medium' | 'low' } | null {
  const { plainText, htmlToPlain } = createPositionMap(fullText);
  let plainSelectedText = annotation.selected_text.replace(/<[^>]*>/g, '');

  // 🔧 MIGRATION FIX: BR tag'li annotation'ları newline karakterli metne uyarla
  if (plainText.includes('\n') && !plainSelectedText.includes('\n')) {
    // Annotation'da newline yok ama metinde var - BR tag migration sonrası
    // Annotation'ın gerçek pozisyonunu bul ve newline karakterini doğru yere ekle
    const expectedStart = annotation.selection_start;
    const expectedEnd = annotation.selection_end;

    // Annotation'ın kapsadığı metin aralığında newline var mı?
    const annotationRange = plainText.substring(expectedStart, expectedEnd);
    const newlineIndex = annotationRange.indexOf('\n');

    if (newlineIndex !== -1) {
      // Newline karakteri annotation içinde - doğru pozisyona ekle
      const beforeNL = plainSelectedText.substring(0, newlineIndex);
      const afterNL = plainSelectedText.substring(newlineIndex);
      plainSelectedText = beforeNL + '\n' + afterNL;
    }
  }

  // HTML pozisyonunu plain text pozisyonuna çevir
  const plainExpectedStart = htmlToPlain[annotation.selection_start] || 0;

  // 🚀 AŞAMA 1: Hızlı Pozisyon Kontrolü
  const expectedEnd = plainExpectedStart + plainSelectedText.length;
  if (expectedEnd <= plainText.length) {
    const textAtExpected = plainText.substring(plainExpectedStart, expectedEnd);
    if (textAtExpected === plainSelectedText) {
      return { start: plainExpectedStart, end: expectedEnd, confidence: 'high' };
    }
  }

  // 🎯 AŞAMA 2: Context-Aware Arama
  if (annotation.prefix_text && annotation.suffix_text) {
    const prefix = annotation.prefix_text.replace(/<[^>]*>/g, '');
    const suffix = annotation.suffix_text.replace(/<[^>]*>/g, '');
    const searchPattern = `${prefix}${plainSelectedText}${suffix}`;

    const foundIndex = plainText.indexOf(searchPattern);
    if (foundIndex !== -1) {
      const actualStart = foundIndex + prefix.length;
      const actualEnd = actualStart + plainSelectedText.length;
      return { start: actualStart, end: actualEnd, confidence: 'medium' };
    }

    // Partial context match - sadece prefix ile dene
    const prefixPattern = `${prefix}${plainSelectedText}`;
    const prefixIndex = plainText.indexOf(prefixPattern);
    if (prefixIndex !== -1) {
      const actualStart = prefixIndex + prefix.length;
      const actualEnd = actualStart + plainSelectedText.length;
      return { start: actualStart, end: actualEnd, confidence: 'medium' };
    }

    // Sadece suffix ile dene
    const suffixPattern = `${plainSelectedText}${suffix}`;
    const suffixIndex = plainText.indexOf(suffixPattern);
    if (suffixIndex !== -1) {
      const actualStart = suffixIndex;
      const actualEnd = actualStart + plainSelectedText.length;
      return { start: actualStart, end: actualEnd, confidence: 'medium' };
    }
  }

  // 🔍 AŞAMA 3: Fuzzy Search (Çoklu Eşleşme Kontrolü)
  const allMatches: number[] = [];
  let searchIndex = 0;

  while (searchIndex < plainText.length) {
    const foundIndex = plainText.indexOf(plainSelectedText, searchIndex);
    if (foundIndex === -1) break;

    allMatches.push(foundIndex);
    searchIndex = foundIndex + 1;
  }

  if (allMatches.length === 1) {
    // Tek eşleşme - güvenli
    return {
      start: allMatches[0],
      end: allMatches[0] + plainSelectedText.length,
      confidence: 'low'
    };
  } else if (allMatches.length > 1) {
    // Çoklu eşleşme - en yakınını seç
    const closest = allMatches.reduce((prev, curr) => {
      return Math.abs(curr - plainExpectedStart) < Math.abs(prev - plainExpectedStart) ? curr : prev;
    });

    return {
      start: closest,
      end: closest + plainSelectedText.length,
      confidence: 'low'
    };
  }

  // ❌ AŞAMA 4: Başarısızlık
  console.warn('❌ Annotation bulunamadı:', {
    annotation_id: annotation.id,
    selected_text: annotation.selected_text,
    expected_start: annotation.selection_start,
    has_prefix: !!annotation.prefix_text,
    has_suffix: !!annotation.suffix_text,
    // 🔧 DEBUG: Newline karakteri debug bilgisi
    plainText_preview: plainText.substring(0, 100).replace(/\n/g, '\\n'),
    plainSelectedText: plainSelectedText.replace(/\n/g, '\\n'),
    has_newlines_in_text: plainText.includes('\n'),
    has_newlines_in_selection: plainSelectedText.includes('\n')
  });

  return null;
}

/**
 * 🎯 Multi-sentence annotation'ları bu sentence için işle
 */
function processMultiSentenceAnnotations(
  annotations: Annotation[],
  currentSentenceId: string,
  currentSentenceText: string,
  allSentences: Array<{ id: string; text: string }>
): AdjustedAnnotation[] {
  const processedAnnotations: AdjustedAnnotation[] = [];

  for (const annotation of annotations) {
    const sentenceIds = Array.isArray(annotation.sentence_id)
      ? annotation.sentence_id
      : [annotation.sentence_id];

    // 🛡️ Geçersiz sentence ID'leri filtrele
    const validSentenceIds = sentenceIds.filter(id => isValidSentenceId(id));

    if (validSentenceIds.length === 0) {
      continue; // Geçersiz annotation, sessizce atla
    }

    // Bu annotation bu sentence'ı içeriyor mu?
    if (!validSentenceIds.includes(currentSentenceId)) {
      continue; // Bu sentence için değil, atla
    }

    if (validSentenceIds.length === 1) {
      // Single sentence annotation - direkt ekle
      processedAnnotations.push(annotation);
    } else {
      // Multi-sentence annotation - bu sentence için pozisyonu hesapla
      // 🛡️ Geçerli sentence ID'leri ile çalış
      const adjustedAnnotation = adjustAnnotationForSentence(
        { ...annotation, sentence_id: validSentenceIds }, // Geçerli ID'lerle yeni annotation
        currentSentenceId,
        currentSentenceText,
        allSentences
      );

      if (adjustedAnnotation) {
        processedAnnotations.push(adjustedAnnotation);
      }
    }
  }

  return processedAnnotations;
}

/**
 * 🔧 Multi-sentence annotation'ı bu sentence için ayarla
 */
function adjustAnnotationForSentence(
  annotation: Annotation,
  currentSentenceId: string,
  currentSentenceText: string,
  allSentences: Array<{ id: string; text: string }>
): AdjustedAnnotation | null {
  try {
    const sentenceIds = Array.isArray(annotation.sentence_id)
      ? annotation.sentence_id
      : [annotation.sentence_id];

    // Bu sentence'ın index'ini bul
    const currentSentenceIndex = sentenceIds.indexOf(currentSentenceId);
    if (currentSentenceIndex === -1) {
      return null; // Bu sentence annotation'da yok
    }

    // Tüm sentence'ları BİRLEŞTİRİRKEN ARALARINA BOŞLUK EKLE (orijinal selection'ı yeniden oluştur)
    const fullText = sentenceIds
      .map(id => allSentences.find(s => s.id === id)?.text.replace(/<[^>]*>/g, '') || '')
      .join(' ');

    // Bu sentence'ın full text içindeki pozisyonunu hesapla
    let sentenceStartInFullText = 0;
    for (let i = 0; i < currentSentenceIndex; i++) {
      const prevSentence = allSentences.find(s => s.id === sentenceIds[i]);
      if (prevSentence) {
        // HTML tag'lerini kaldırarak plain text length hesapla
        const plainText = prevSentence.text.replace(/<[^>]*>/g, '');
        sentenceStartInFullText += plainText.length + 1; // +1 for the space
      }
    }

    // Current sentence'ın plain text length'ini hesapla
    const currentSentencePlainText = currentSentenceText.replace(/<[^>]*>/g, '');
    const sentenceEndInFullText = sentenceStartInFullText + currentSentencePlainText.length;

    // Annotation'ın bu sentence ile kesişen kısmını hesapla
    const annotationStart = annotation.selection_start;
    const annotationEnd = annotation.selection_end;

    // Kesişim var mı?
    const intersectionStart = Math.max(annotationStart, sentenceStartInFullText);
    const intersectionEnd = Math.min(annotationEnd, sentenceEndInFullText);

    if (intersectionStart >= intersectionEnd) {
      return null; // Kesişim yok
    }

    // Bu sentence içindeki relative pozisyonları hesapla
    const relativeStart = intersectionStart - sentenceStartInFullText;
    const relativeEnd = intersectionEnd - sentenceStartInFullText;

    // Selected text'i bu sentence için ayarla - Plain text bazında
    const adjustedSelectedText = currentSentencePlainText.substring(relativeStart, relativeEnd);

    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Multi-sentence adjustment:', {
        annotationId: annotation.id,
        currentSentenceId,
        currentSentenceIndex,
        sentenceIds,
        fullText: fullText.substring(0, 100) + '...',
        sentenceStartInFullText,
        sentenceEndInFullText,
        annotationStart,
        annotationEnd,
        intersectionStart,
        intersectionEnd,
        relativeStart,
        relativeEnd,
        currentSentenceText: currentSentenceText.substring(0, 50) + '...',
        currentSentencePlainText: currentSentencePlainText.substring(0, 50) + '...',
        adjustedSelectedText,
        originalSelectedText: annotation.selected_text,
        hasIntersection: intersectionStart < intersectionEnd
      });
    }

    // Yeni annotation oluştur
    return {
      ...annotation,
      selection_start: relativeStart,
      selection_end: relativeEnd,
      selected_text: adjustedSelectedText,
      // Orijinal multi-sentence bilgisini koru
      _original_sentence_ids: sentenceIds,
      _original_selection_start: annotationStart,
      _original_selection_end: annotationEnd,
      _original_selected_text: annotation.selected_text
    } as AdjustedAnnotation;

  } catch (error) {
    console.warn('Multi-sentence annotation adjustment failed:', error);
    return null;
  }
}

interface UseStyledTextSegmentsProps {
  text: string;
  annotations: Annotation[];
  onAnnotationClick?: (annotation: Annotation) => void;
  sentenceId: string;
  allSentences?: Array<{ id: string; text: string }>;
}

export const useStyledTextSegments = ({
  text,
  annotations,
  onAnnotationClick,
  sentenceId,
  allSentences = []
}: UseStyledTextSegmentsProps) => {
  // Theme bağımlılığı - theme değiştiğinde component re-render olsun
  const { currentTheme } = useTheme();
  
  // CSS değişken değişimlerini dinle (özel renk picker için)
  const [cssVariableUpdateTrigger, setCssVariableUpdateTrigger] = useState(0);
  
  useEffect(() => {
    // CSS değişken değişimlerini dinleyen MutationObserver
    let lastTextColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim();
    
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const target = mutation.target as HTMLElement;
          if (target === document.documentElement) {
            // Sadece --text-color değişmişse re-render tetikle
            const currentTextColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim();
            if (currentTextColor !== lastTextColor) {
              if (DEBUG_MODE) {
                console.log('[TextHighlighter] Text color changed:', lastTextColor, '->', currentTextColor);
              }
              lastTextColor = currentTextColor;
              setCssVariableUpdateTrigger(prev => prev + 1);
            }
          }
        }
      });
    });
    
    // DocumentElement'i izle
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });
    
    return () => observer.disconnect();
  }, []);
  
  const output = useMemo(() => {
    if (!text || !annotations || annotations.length === 0) {
      return {
        styledText: <span dangerouslySetInnerHTML={{ __html: text }} style={{ whiteSpace: 'pre-line' }} />,
        trailingSpaceStyle: {}
      };
    }

    // HTML-aware pozisyon mapping
    const { plainText, htmlToPlain, arabicPhrases } = createPositionMap(text);

    // Her karakter için stil hesapla (plain text bazında)
    const charStyles: Array<{
      backgroundColor?: string;
      color?: string;
      underline?: string;
      fontSize?: string;
      textDecoration?: string;
      textDecorationStyle?: string;
      textDecorationColor?: string;
      textUnderlineOffset?: string;
      translation?: string;
      annotations: AdjustedAnnotation[];
    }> = new Array(plainText.length).fill(null).map(() => ({ annotations: [] }));

    // 🎨 Arapça phrase'lerin stillerini ayarla
    arabicPhrases.forEach(phrase => {
      for (let i = phrase.plainStart; i < phrase.plainEnd && i < plainText.length; i++) {
        charStyles[i].fontSize = phrase.fontSize;
        charStyles[i].textDecoration = phrase.textDecoration;
        charStyles[i].textDecorationStyle = phrase.textDecorationStyle;
        charStyles[i].textDecorationColor = phrase.textDecorationColor;
        charStyles[i].textUnderlineOffset = phrase.textUnderlineOffset;
        charStyles[i].translation = phrase.translation; // 🎯 Translation'ı sakla
      }
    });

    // 🎯 MULTI-SENTENCE ANNOTATION PROCESSING
    const processedAnnotations = processMultiSentenceAnnotations(
      annotations,
      sentenceId,
      text,
      allSentences
    );

    // Annotation'ları öncelik sırasına göre sırala - SON YAPILAN KAZANIR
    const sortedAnnotations = [...processedAnnotations].sort((a, b) => {
      // Öncelik sırası: highlight → bookmark → note (note en üstte görünür)
      const priority = { highlight: 1, bookmark: 2, note: 3, sherh: 3 };
      const priorityDiff = priority[a.annotation_type] - priority[b.annotation_type];

      // Aynı türdeyse created_at'e göre sırala
      if (priorityDiff === 0) {
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      }

      return priorityDiff;
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('🎯 TextHighlighter Debug:', {
        sentenceId,
        text: text.substring(0, 50) + '...',
        originalAnnotationsCount: annotations.length,
        processedAnnotationsCount: processedAnnotations.length,
        multiSentenceCount: annotations.filter(a => Array.isArray(a.sentence_id) && a.sentence_id.length > 1).length,
        processedAnnotations: processedAnnotations.map(a => ({
          id: a.id,
          type: a.annotation_type,
          start: a.selection_start,
          end: a.selection_end,
          selected_text: a.selected_text,
          isAdjusted: !!a._original_sentence_ids
        }))
      });
    }

    // Her annotation için karakterleri işaretle - SON YAPILAN ÜZERİNE YAZAR
    sortedAnnotations.forEach(annotation => {
      // 🎯 HYBRID POZİSYON BULMA - 3 Aşamalı Fallback
      const result = findTextPosition(text, annotation);

      if (result === null) {
        return; // Bu annotation'ı atla
      }

      const { start, end } = result;

      for (let i = start; i < end && i < plainText.length; i++) {
        // Annotation'ı listeye ekle
        if (!charStyles[i].annotations.find(a => a.id === annotation.id)) {
          charStyles[i].annotations.push(annotation);
        }

        // Görsel stili uygula (sonraki, öncekinin üzerine yazar)
        switch (annotation.annotation_type) {
          case 'note': {
            let base = '#3b82f6';
            if (annotation.color && annotation.color !== 'NO_COLOR') {
              const bg = resolveColorIdentifier(annotation.color);
              charStyles[i].backgroundColor = applyBackgroundOpacity(bg);
              base = bg;
            } else {
              // Renk yoksa sadece underline, metin rengi ile uyumlu kalsın
              const textColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim();
              base = textColor || base;
            }
            // underline her zaman olsun ve renk ile uyumlu olsun
            charStyles[i].underline = base;
            break; }
          case 'sherh': {
            let base = '#fbbf24';
            if (annotation.color && annotation.color !== 'NO_COLOR') {
              const bg = resolveColorIdentifier(annotation.color);
              charStyles[i].backgroundColor = applyBackgroundOpacity(bg);
              base = bg;
            } else {
              const textColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim();
              base = textColor || base;
            }
            charStyles[i].underline = base;
            break; }
          case 'bookmark': {
            let base = '#10b981';
            if (annotation.color && annotation.color !== 'NO_COLOR') {
              const bg = resolveColorIdentifier(annotation.color);
              charStyles[i].backgroundColor = applyBackgroundOpacity(bg);
              base = bg;
            } else {
              const textColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim();
              base = textColor || base;
            }
            charStyles[i].underline = base;
            break; }
          case 'highlight':
            const finalColor = resolveColorIdentifier(annotation.color);
            if (annotation.highlight_style === 'background') {
              charStyles[i].backgroundColor = applyBackgroundOpacity(finalColor);
            } else {
              charStyles[i].color = finalColor;
            }
            break;
        }
      }
    });

    // 5. Rebuild HTML with styled spans (Robust version)
    let styledHtml = '';
    let isSpanOpen = false;
    let currentAppliedStyle: React.CSSProperties = {};
    let currentAnnotations: AdjustedAnnotation[] = [];
    let currentTranslation: string | undefined = undefined;

    // Helper to compare style objects and annotations
    const areSegmentsEqual = (
      style1: React.CSSProperties,
      style2: React.CSSProperties,
      anns1: AdjustedAnnotation[],
      anns2: AdjustedAnnotation[],
      tr1?: string,
      tr2?: string
    ) => {
      if (tr1 !== tr2) return false;
      if (anns1.length !== anns2.length) return false;
      
      const ann1Ids = anns1.map(a => a.id).sort().join(',');
      const ann2Ids = anns2.map(a => a.id).sort().join(',');
      if (ann1Ids !== ann2Ids) return false;

      const keys1 = Object.keys(style1);
      const keys2 = Object.keys(style2);
      if (keys1.length !== keys2.length) return false;
      for (const key of keys1) {
        if (style1[key as keyof React.CSSProperties] !== style2[key as keyof React.CSSProperties]) return false;
      }
      return true;
    };

    for (let i = 0; i < text.length; i++) {
      const char = text[i];

      // Handle HTML tags by appending them directly
      if (char === '<') {
        const tagEnd = text.indexOf('>', i);
        if (tagEnd !== -1) {
          // Close any open span before writing the tag
          if (isSpanOpen) {
            styledHtml += '</span>';
            isSpanOpen = false;
          }
          styledHtml += text.substring(i, tagEnd + 1);
          i = tagEnd;
          continue;
        }
      }

      // Handle text characters
      const plainIndex = htmlToPlain[i];
      if (plainIndex === undefined) {
        // This character is likely inside a tag that was skipped, but we handle it just in case
        if (isSpanOpen) {
          styledHtml += '</span>';
          isSpanOpen = false;
        }
        styledHtml += char;
        continue;
      }

      const charStyleDef = charStyles[plainIndex] || { annotations: [] };
      const cssStyle: React.CSSProperties = {};
      
      // Build style object for the current character
      if (charStyleDef.backgroundColor) cssStyle.backgroundColor = charStyleDef.backgroundColor;
      if (charStyleDef.color) cssStyle.color = charStyleDef.color;
      if (charStyleDef.underline) cssStyle.boxShadow = `inset 0 -2px 0 0 ${charStyleDef.underline}`;
      if (charStyleDef.fontSize) cssStyle.fontSize = charStyleDef.fontSize;
      if (charStyleDef.textDecoration) cssStyle.textDecoration = charStyleDef.textDecoration;
      if (charStyleDef.textDecorationStyle) cssStyle.textDecorationStyle = charStyleDef.textDecorationStyle as any;
      if (charStyleDef.textDecorationColor) cssStyle.textDecorationColor = charStyleDef.textDecorationColor;
      if (charStyleDef.textUnderlineOffset) cssStyle.textUnderlineOffset = charStyleDef.textUnderlineOffset;
      if (charStyleDef.annotations.length > 0) cssStyle.cursor = 'pointer';

      // Check if style or annotation has changed
      if (!isSpanOpen || !areSegmentsEqual(cssStyle, currentAppliedStyle, charStyleDef.annotations, currentAnnotations, charStyleDef.translation, currentTranslation)) {
        if (isSpanOpen) {
          const endingAnnotations = currentAnnotations.filter(
            ann => !charStyleDef.annotations.find(nextAnn => nextAnn.id === ann.id)
          );
          // Render icons for the ending annotations.
          endingAnnotations.forEach(annotation => {
            // 🎯 İKON RENDER KONTROLÜ: Sadece multi-sentence annotation'ın SON cümlesinde ikonu göster
            let isLastSentence = true; // Varsayılan olarak tekil cümleler için true
            if (annotation._original_sentence_ids && Array.isArray(annotation._original_sentence_ids)) {
              isLastSentence = annotation._original_sentence_ids[annotation._original_sentence_ids.length - 1] === sentenceId;
            }

            if (isLastSentence) {
              let iconColor = 'var(--text-color)';
              
              // Genel AUTO_OVERLAY kontrolü - hem AUTO_OVERLAY_ hem AUTO_BG_OVERLAY_ kontrol et
              let isAutoOverlay = false;
              const highlights = charStyleDef.annotations.filter(a => a.annotation_type === 'highlight');
              if (highlights.length > 0) {
                const bgColor = resolveColorIdentifier(highlights[0].color);
                // AUTO_OVERLAY_ veya AUTO_BG_OVERLAY_ varsa kontrast gerekli
                isAutoOverlay = bgColor.includes('AUTO_OVERLAY_') || bgColor.includes('AUTO_BG_OVERLAY_') ||
                               (bgColor.includes('color-mix') && (highlights[0].color.includes('AUTO_OVERLAY_') || highlights[0].color.includes('AUTO_BG_OVERLAY_')));
              }
              
              // İkon rengini AUTO_OVERLAY'e göre belirle
              if (annotation.annotation_type === 'highlight') {
                const bgColor = resolveColorIdentifier(annotation.color);
                
                // color-mix ile arka planla karışan renkler için kontrast gerekli
                const needsContrast = bgColor.includes('color-mix') && 
                                     (bgColor.includes('var(--bg-color)') || bgColor.includes('var(--text-color)'));
                
                if (needsContrast) {
                  // Background color'ı kullan - highlight üzerinde net görünür
                  const currentBgColor = getComputedStyle(document.documentElement).getPropertyValue('--bg-color').trim();
                  iconColor = currentBgColor;
                } else {
                  iconColor = bgColor;
                }
              } else if (annotation.annotation_type === 'note') {
                // Seçilmiş renk varsa ikonu da aynı taban renge boyayalım; renksiz seçenekte text-color kullan
                if (annotation.color && annotation.color !== 'NO_COLOR') {
                  const base = resolveColorIdentifier(annotation.color);
                  iconColor = base;
                } else {
                  iconColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim() || (isAutoOverlay ? '#1e40af' : '#3b82f6');
                }
              } else if (annotation.annotation_type === 'sherh') {
                if (annotation.color && annotation.color !== 'NO_COLOR') {
                  const base = resolveColorIdentifier(annotation.color);
                  iconColor = base;
                } else {
                  iconColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim() || (isAutoOverlay ? '#d97706' : '#fbbf24');
                }
              } else if (annotation.annotation_type === 'bookmark') {
                if (annotation.color && annotation.color !== 'NO_COLOR') {
                  const base = resolveColorIdentifier(annotation.color);
                  iconColor = base;
                } else {
                  iconColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim() || (isAutoOverlay ? '#047857' : '#10b981');
                }
              }
              
              styledHtml += getIconSvgString(annotation, iconColor, isAutoOverlay);
            }
          });
          styledHtml += '</span>';
        }

        currentAppliedStyle = cssStyle;
        currentAnnotations = charStyleDef.annotations;
        currentTranslation = charStyleDef.translation;

        if (Object.keys(currentAppliedStyle).length > 0) {
          const styleString = Object.entries(currentAppliedStyle)
            .map(([key, value]) => `${key.replace(/([A-Z])/g, m => `-${m.toLowerCase()}`)}:${value}`)
            .join(';');
            
          // No data attributes on the container span; only icons are clickable now
          let dataAttrs = '';
          if (currentTranslation) {
            dataAttrs += ` class="interactive-arabic-phrase" data-arabic-translation="${encodeURIComponent(currentTranslation)}"`;
          }

          styledHtml += `<span style="${styleString}"${dataAttrs}>`;
          isSpanOpen = true;
        } else {
          isSpanOpen = false;
        }
      }
      
      // Append character; text itself is not clickable anymore
      styledHtml += char;
    }

    if (isSpanOpen) {
      currentAnnotations.forEach(annotation => {
        // 🎯 İKON RENDER KONTROLÜ: Sadece multi-sentence annotation'ın SON cümlesinde ikonu göster
        let isLastSentence = true; // Varsayılan olarak tekil cümleler için true
        if (annotation._original_sentence_ids && Array.isArray(annotation._original_sentence_ids)) {
          isLastSentence = annotation._original_sentence_ids[annotation._original_sentence_ids.length - 1] === sentenceId;
        }

        if (isLastSentence) {
          let iconColor = 'var(--text-color)';
          
          // Genel AUTO_OVERLAY kontrolü - sadece gerçek highlight AUTO_OVERLAY'lerini tespit et
          let isAutoOverlay = false;
          const highlights = currentAnnotations.filter(a => a.annotation_type === 'highlight');
          if (highlights.length > 0) {
            const bgColor = resolveColorIdentifier(highlights[0].color);
            // Sadece AUTO_OVERLAY_ ile başlayanları tespit et, AUTO_BG_OVERLAY_ değil
            isAutoOverlay = bgColor.includes('AUTO_OVERLAY_') || 
                           (bgColor.includes('color-mix') && highlights[0].color.includes('AUTO_OVERLAY_'));
          }
          
          // İkon rengini AUTO_OVERLAY'e göre belirle
          if (annotation.annotation_type === 'highlight') {
            const bgColor = resolveColorIdentifier(annotation.color);
            
            // color-mix ile arka planla karışan renkler için kontrast gerekli
            const needsContrast = bgColor.includes('color-mix') && 
                                 (bgColor.includes('var(--bg-color)') || bgColor.includes('var(--text-color)'));
            
            if (needsContrast) {
              // Background color'ı kullan - highlight üzerinde net görünür
              const currentBgColor = getComputedStyle(document.documentElement).getPropertyValue('--bg-color').trim();
              iconColor = currentBgColor;
            } else {
              iconColor = bgColor;
            }
          } else if (annotation.annotation_type === 'note') {
            if (annotation.color && annotation.color !== 'NO_COLOR') {
              iconColor = resolveColorIdentifier(annotation.color);
            } else {
              iconColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim() || (isAutoOverlay ? '#1e40af' : '#3b82f6');
            }
          } else if (annotation.annotation_type === 'sherh') {
            if (annotation.color && annotation.color !== 'NO_COLOR') {
              iconColor = resolveColorIdentifier(annotation.color);
            } else {
              iconColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim() || (isAutoOverlay ? '#d97706' : '#fbbf24');
            }
          } else if (annotation.annotation_type === 'bookmark') {
            if (annotation.color && annotation.color !== 'NO_COLOR') {
              iconColor = resolveColorIdentifier(annotation.color);
            } else {
              iconColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim() || (isAutoOverlay ? '#047857' : '#10b981');
            }
          }
          
          styledHtml += getIconSvgString(annotation, iconColor, isAutoOverlay);
        }
      });
      styledHtml += '</span>';
    }

    // Cümle sonundaki boşluğun stilini hesapla
    const trailingSpaceStyle: React.CSSProperties = {};
    const lastCharStyle = charStyles[charStyles.length - 1];
    if (lastCharStyle && lastCharStyle.annotations.length > 0) {
      // Find the highest priority annotation on the last character
      const priority = { highlight: 1, bookmark: 2, note: 3, sherh: 3 };
      const sortedAnnotations = [...lastCharStyle.annotations].sort((a, b) => {
        const priorityA = priority[a.annotation_type] || 0;
        const priorityB = priority[b.annotation_type] || 0;
        const priorityDiff = priorityA - priorityB;
        if (priorityDiff === 0) {
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        }
        return priorityDiff;
      });
      const annotation = sortedAnnotations[sortedAnnotations.length - 1];

      if (annotation && annotation._original_sentence_ids && Array.isArray(annotation._original_sentence_ids)) {
        const originalIds = annotation._original_sentence_ids;
        const currentIndex = originalIds.indexOf(sentenceId);

        if (currentIndex > -1 && currentIndex < originalIds.length - 1) {
          if (lastCharStyle.backgroundColor) trailingSpaceStyle.backgroundColor = lastCharStyle.backgroundColor;
          if (lastCharStyle.color) trailingSpaceStyle.color = lastCharStyle.color;
          if (lastCharStyle.underline) trailingSpaceStyle.boxShadow = `inset 0 -2px 0 0 ${lastCharStyle.underline}`;
        }
      }
    }

    const styledText = (
      <span
        className="text-highlighter-container"
        style={{ whiteSpace: 'pre-line' }}
        onClick={(e) => {
          const target = e.target as HTMLElement;
          // Only open detail when the icon is clicked
          const clickedIcon = target.closest('svg.annotation-icon') as HTMLElement | null;
          if (!clickedIcon) return;
          // Mark this event so capture-phase guard can detect
          (e as any).__annotationIconClicked = true;
          // Stop tooltip bubbling
          e.stopPropagation();

          const annotationId = clickedIcon.getAttribute('data-annotation-id');
          if (!annotationId) return;

          const clickedAnnotation = annotations.find(a => a.id === annotationId);
          if (clickedAnnotation && onAnnotationClick) {
            onAnnotationClick(clickedAnnotation);
          }
        }}
        dangerouslySetInnerHTML={{ __html: styledHtml }}
      />
    );

    return { styledText, trailingSpaceStyle };

  }, [text, annotations, onAnnotationClick, allSentences, sentenceId, currentTheme, cssVariableUpdateTrigger]);

  return output;
};

// 🔧 CSS stilleri için yardımcı component - newline karakterlerini destekler
export function TextHighlighterStyles() {
  return (
    <style dangerouslySetInnerHTML={{
      __html: `
        /* TextHighlighter için genel stiller */
        .text-highlighter-container {
          white-space: pre-line; /* Newline karakterlerini korur */
          word-wrap: break-word;
          overflow-wrap: break-word;
        }

        /* Annotation'lı span'lar için stiller */
        .text-highlighter-container span {
          white-space: pre-line; /* Newline karakterlerini korur */
        }

        /* Interactive Arabic phrase stiller korunuyor */
        .text-highlighter-container .interactive-arabic-phrase {
          cursor: pointer;
          font-size: 170%;
          text-decoration: underline;
          text-decoration-style: dotted;
          text-decoration-color: var(--text-color);
          text-underline-offset: 6px;
          white-space: pre-line; /* Newline karakterlerini korur */
        }

        /* Annotation hover efektleri */
        .text-highlighter-container span[data-annotation-id]:hover {
          opacity: 0.8;
          transition: opacity 0.2s ease;
        }

        /* Annotation ikonları (panel ikonları) */
        .text-highlighter-container .annotation-icon {
          /* Tutarlı ikon boyutu */
          width: 24px;
          height: 24px;
          vertical-align: middle;
          box-shadow: inherit; /* Underline/highlight inheritance */
        }
      `
    }} />
  );
}
