/**
 * 🎯 Highlight System Type Definitions
 * Yeni highlight sistemi için comprehensive type definitions
 */
import type { Annotation } from '../../shared/types/types';

// Re-export the main Annotation type for consistency within this module
export type { Annotation };

// Highlight engine için genişletilmiş annotation
export interface ExtendedAnnotation extends Annotation {
  priority: number;
  zIndex: number;
  isValid: boolean;
  conflicts: string[]; // Çakıştığı annotation ID'leri

  // 🔧 Multi-sentence processing için internal alanlar
  _original_sentence_ids?: string[];  // Orijinal sentence ID'leri
  _original_selection_start?: number; // Orijinal başlangıç pozisyonu
  _original_selection_end?: number;   // Orijinal bitiş pozisyonu
  _original_selected_text?: string;   // Orijinal seçilen text
}

// Render için hazır segment
export interface HighlightSegment {
  id: string;
  start: number;
  end: number;
  content: string; // HTML content
  isHighlighted: boolean;
  annotations: ExtendedAnnotation[];
  styles: SegmentStyle;
  events: SegmentEvents;
}

// Segment stil tanımları
export interface SegmentStyle {
  // Background styles
  backgroundColor?: string;
  backgroundOpacity?: number;
  
  // Text styles
  textColor?: string;
  fontWeight?: 'normal' | 'bold';
  fontStyle?: 'normal' | 'italic';
  
  // Border & underline styles
  borderBottom?: BorderStyle;
  borderTop?: BorderStyle;
  boxShadow?: string;
  
  // Interactive styles
  cursor: 'pointer' | 'default';
  transition: string;
  
  // Hover states
  hover?: Partial<SegmentStyle>;
  
  // Z-index for layering
  zIndex: number;
  
  // Custom CSS properties
  customProperties?: Record<string, string>;
}

export interface BorderStyle {
  width: number;
  style: 'solid' | 'dashed' | 'dotted';
  color: string;
}

// Segment event handlers
export interface SegmentEvents {
  onClick?: (annotation: ExtendedAnnotation, event: MouseEvent) => void;
  onMouseEnter?: (annotation: ExtendedAnnotation, event: MouseEvent) => void;
  onMouseLeave?: (annotation: ExtendedAnnotation, event: MouseEvent) => void;
  onDoubleClick?: (annotation: ExtendedAnnotation, event: MouseEvent) => void;
}

// Çakışma çözümü için types
export interface ConflictResolution {
  strategy: ConflictStrategy;
  result: ResolvedConflict[];
}

export type ConflictStrategy = 
  | 'merge'        // Aynı tip annotation'ları birleştir
  | 'layer'        // Katmanlar halinde göster
  | 'split'        // Çakışan kısımları böl
  | 'priority'     // Priority'ye göre üsttekini göster
  | 'user_choice'; // Kullanıcıya sor

export interface ResolvedConflict {
  originalAnnotations: ExtendedAnnotation[];
  resolvedSegments: HighlightSegment[];
  strategy: ConflictStrategy;
  confidence: number; // 0-1 arası çözüm güveni
}

// Color picker için types
export interface ColorOption {
  id: string;
  name: string;
  value: string;
  category: 'warm' | 'cool' | 'neutral' | 'custom';
  isDefault?: boolean;
}

export interface ColorPalette {
  background: ColorOption[];
  text: ColorOption[];
  accent: ColorOption[];
  recent?: ColorOption[];
}

// Highlight engine configuration
export interface HighlightEngineConfig {
  // Performance settings
  enableMemoization: boolean;
  maxSegments: number;
  debounceMs: number;
  
  // Conflict resolution
  defaultConflictStrategy: ConflictStrategy;
  maxNestingLevel: number;
  enableSmartMerging: boolean;
  
  // Visual settings
  defaultOpacity: number;
  animationDuration: number;
  enableTransitions: boolean;
  
  // Accessibility
  enableAriaLabels: boolean;
  enableKeyboardNavigation: boolean;
  highContrastMode: boolean;
  
  // Debug
  enableDebugMode: boolean;
  logPerformance: boolean;
}

// Hook return types
export interface UseHighlightEngineReturn {
  segments: HighlightSegment[];
  isProcessing: boolean;
  error: Error | null;
  stats: ProcessingStats;
  
  // Actions
  processText: (text: string, annotations: Annotation[]) => void;
  clearHighlights: () => void;
  updateConfig: (config: Partial<HighlightEngineConfig>) => void;
  
  // Event handlers
  handleSegmentClick: (segmentId: string, annotation: ExtendedAnnotation) => void;
  handleSegmentHover: (segmentId: string, isEntering: boolean) => void;
}

export interface ProcessingStats {
  totalAnnotations: number;
  processedSegments: number;
  conflictsResolved: number;
  processingTimeMs: number;
  memoryUsageMB: number;
}

// Color picker hook
export interface UseColorPickerReturn {
  selectedColor: string;
  selectedStyle: 'background' | 'text';
  palette: ColorPalette;
  isOpen: boolean;
  
  // Actions
  selectColor: (color: string, style: 'background' | 'text') => void;
  togglePicker: () => void;
  addToRecent: (color: ColorOption) => void;
  resetToDefault: () => void;
}

// Performance monitoring
export interface PerformanceMetrics {
  renderTime: number;
  segmentCount: number;
  annotationCount: number;
  conflictCount: number;
  memoryUsage: number;
  timestamp: number;
}

// Error types
export class HighlightError extends Error {
  constructor(
    message: string,
    public code: HighlightErrorCode,
    public context?: any
  ) {
    super(message);
    this.name = 'HighlightError';
  }
}

export enum HighlightErrorCode {
  INVALID_ANNOTATION = 'INVALID_ANNOTATION',
  POSITION_OUT_OF_BOUNDS = 'POSITION_OUT_OF_BOUNDS',
  HTML_PARSING_ERROR = 'HTML_PARSING_ERROR',
  CONFLICT_RESOLUTION_FAILED = 'CONFLICT_RESOLUTION_FAILED',
  PERFORMANCE_THRESHOLD_EXCEEDED = 'PERFORMANCE_THRESHOLD_EXCEEDED',
  MEMORY_LIMIT_EXCEEDED = 'MEMORY_LIMIT_EXCEEDED'
}

// Event system
export interface HighlightEvent {
  type: HighlightEventType;
  payload: any;
  timestamp: number;
  source: 'user' | 'system' | 'api';
}

export enum HighlightEventType {
  ANNOTATION_CREATED = 'ANNOTATION_CREATED',
  ANNOTATION_UPDATED = 'ANNOTATION_UPDATED',
  ANNOTATION_DELETED = 'ANNOTATION_DELETED',
  CONFLICT_DETECTED = 'CONFLICT_DETECTED',
  CONFLICT_RESOLVED = 'CONFLICT_RESOLVED',
  PERFORMANCE_WARNING = 'PERFORMANCE_WARNING',
  ERROR_OCCURRED = 'ERROR_OCCURRED'
}

// Utility types
export type AnnotationPosition = {
  start: number;
  end: number;
  text: string;
};

export type HighlightTheme = {
  colors: {
    note: string;
    bookmark: string;
    highlight: string;
    selection: string;
  };
  opacity: {
    default: number;
    hover: number;
    active: number;
  };
  transitions: {
    duration: string;
    easing: string;
  };
};

// Component props
export interface HighlightRendererProps {
  text: string;
  annotations: Annotation[];
  config?: Partial<HighlightEngineConfig>;
  theme?: Partial<HighlightTheme>;
  onAnnotationClick?: (annotation: ExtendedAnnotation) => void;
  onAnnotationHover?: (annotation: ExtendedAnnotation | null) => void;
  className?: string;
  style?: React.CSSProperties;
}

export interface ColorPickerProps {
  selectedColor?: string;
  selectedStyle?: 'background' | 'text';
  palette?: Partial<ColorPalette>;
  onColorSelect: (color: string, style: 'background' | 'text') => void;
  onClose?: () => void;
  position?: 'above' | 'below' | 'auto';
  compact?: boolean;
  showRecent?: boolean;
  className?: string;
}

// Internal engine types
export interface NormalizedAnnotation extends ExtendedAnnotation {
  start: number;
  end: number;
  priority: number;
}

export interface ConflictGroup {
  annotations: NormalizedAnnotation[];
  type: 'simple_overlap' | 'nested' | 'partial_overlap' | 'complex';
}

export interface ResolvedAnnotation extends NormalizedAnnotation {
  resolved: true;
  styles: CombinedStyle;
  overlays?: NormalizedAnnotation[];
  sourceAnnotations?: NormalizedAnnotation[];
}

export interface CombinedStyle {
  backgroundColor: string | null;
  textColor: string | null;
  underlines: Array<{ color: string; style: string }>;
  borders: Array<{ color: string; style: string }>;
  effects: string[];
}

export interface TextAnalysis {
  originalText: string;
  plainText: string;
  htmlMap: HtmlMap;
  length: number;
}

export interface HtmlMap {
  plainToHtml: number[];
  htmlToPlain: number[];
}
