import { useEffect } from 'react';

interface AnnotationVisualizerProps {
  bookId?: string;
  sectionId?: string;
  isEnabled?: boolean;
}

/**
 * Annotation Visualizer Component
 * Mevcut annotation'ları metinde görsel olarak gösterir
 *
 * NOT: Geçici olarak devre dışı - infinite loop sorunları nedeniyle
 * TODO: Daha güvenli bir implementasyon yapılacak
 */
export function AnnotationVisualizer({
  bookId,
  sectionId,
  isEnabled = false
}: AnnotationVisualizerProps) {
  useEffect(() => {
    // Visualization temporarily disabled to prevent infinite loops
  }, [isEnabled, bookId, sectionId]);

  // Bu component render etmez, sadece DOM manipülasyonu yapar
  return null;
}
