import React from 'react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface ParagraphActionButtonProps {
  sentenceIds: string[];
  paragraphText: string;
  paragraphId: string;
  paragraphIndex: number; // 0-based
  onTrigger: (el: HTMLElement) => void;
  variant?: 'overlay' | 'inline';
}

/**
 * Paragraf satır sonuna (sağ üst) overlay olarak yerleşen üç nokta butonu.
 * Görünürlük: desktop hover, focus & her zaman mobil (sm breakpoint altında).
 */
export const ParagraphActionButton: React.FC<ParagraphActionButtonProps> = ({
  sentenceIds,
  paragraphText,
  paragraphId,
  paragraphIndex,
  onTrigger,
  variant = 'overlay'
}) => {
  // 25 karakterden az olan paragraflar için butonu gizle
  // HTML etiketlerini, boşlukları, diacritik işaretleri ve özel karakterleri çıkararak gerçek uzunluğu hesapla
  const cleanText = paragraphText
    .replace(/<[^>]*>/g, '') // HTML etiketlerini kaldır
    .replace(/\s+/g, '') // Tüm boşlukları kaldır
    .replace(/[\u064B-\u065F\u0670\u06D6-\u06ED]/g, ''); // Arapça diacritik işaretlerini kaldır
  
  if (cleanText.length < 20) {
    return null;
  }

  if (variant === 'inline') {
    // Daha küçük ve daha hafif overlay tonları
  // Eski (daha yumuşak) tonlara geri dönüldü
  const baseBg = useAutoOverlay(10, 'var(--bg-color)');
  const hoverBg = useAutoOverlay(13, 'var(--bg-color)');
  const activeBg = useAutoOverlay(16, 'var(--bg-color)');
    return (
      <button
        type="button"
  className="paragraph-action-btn inline-flex items-center justify-center select-none font-arabic font-medium
       w-7 ml-1 mr-1 h-6 rounded-[0.5rem] text-[11px]
       transition-colors transition-opacity"
        style={{
          backgroundColor: baseBg as string,
          color: 'var(--text-color)',
    opacity: 0.9
        }}
        onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = hoverBg as string; e.currentTarget.style.opacity = '1'; }}
        onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = baseBg as string; e.currentTarget.style.opacity = '0.9'; }}
        onMouseDown={(e) => { e.currentTarget.style.backgroundColor = activeBg as string; }}
        onMouseUp={(e) => { e.currentTarget.style.backgroundColor = hoverBg as string; }}
        aria-label={`Paragraf menüsü #${(paragraphIndex ?? 0) + 1}`}
        data-paragraph-id={paragraphId}
        data-paragraph-index={paragraphIndex}
        data-paragraph-sentences={JSON.stringify(sentenceIds)}
        data-paragraph-text={paragraphText}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onTrigger(e.currentTarget);
        }}
        onFocus={(e) => {
          // Focus ile scroll'u engelle
          e.preventDefault();
          e.currentTarget.blur();
        }}
      >
  <span className="leading-none">⋯</span>
      </button>
    );
  }
  return (
    <button
      type="button"
      className="paragraph-action-btn z-20 w-6 h-6 flex items-center justify-center select-none
                 absolute -right-8 top-1/2 -translate-y-1/2 text-[12px]
                 rounded-full bg-[var(--text-color)]/35 hover:bg-[var(--text-color)]/55
                 border border-[color-mix(in_srgb,var(--text-color)_35%,transparent)]
                 text-[var(--text-color)]/90 hover:text-[var(--text-color)]
                 shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-[color-mix(in_srgb,var(--text-color)_55%,transparent)] focus:ring-offset-1 focus:ring-offset-[var(--bg-primary)]"
      aria-label="Paragraf menüsü"
      data-paragraph-id={paragraphId}
      data-paragraph-index={paragraphIndex}
      data-paragraph-sentences={JSON.stringify(sentenceIds)}
      data-paragraph-text={paragraphText}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onTrigger(e.currentTarget);
      }}
      onFocus={(e) => {
        // Focus ile scroll'u engelle
        e.preventDefault();
        e.currentTarget.blur();
      }}
    >
      <span className="leading-none -mt-px">⋯</span>
    </button>
  );
};
