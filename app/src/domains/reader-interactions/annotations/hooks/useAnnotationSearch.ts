import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@shared/utils/supabaseClient';
import type { Annotation } from '../../shared/types';

interface SearchParams {
  selectedText: string;
  sentenceIds: string[];
  bookId?: string;
  sectionId?: string;
  isPublicOnly?: boolean;
}

export function useAnnotationSearch({
  selectedText,
  sentenceIds,
  bookId,
  sectionId,
  isPublicOnly = true,
}: SearchParams) {
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadAnnotations = useCallback(async () => {
    if (!selectedText || sentenceIds.length === 0) {
      setAnnotations([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // --- EN GÜVENLİ ARAMA MANTIĞI (3. Versiyon) ---
      // Adım 1: B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ilgili TÜM herkese açık şerhleri çek.
      const { data: allAnnotationsInSection, error: fetchError } = await supabase
        .from('text_annotations')
        .select('*')
        .eq('book_id', bookId || '')
        .eq('section_id', sectionId || '')
        .eq('annotation_type', 'sherh') // Sadece şerhleri ara
        .eq('is_public', true);

      if (fetchError) throw new Error(`Annotation fetch failed: ${fetchError.message}`);

      if (!allAnnotationsInSection) {
        setAnnotations([]);
        return;
      }

      // Adım 2: SON ve DOĞRU Filtreleme Mantığı
      const sentenceIdsSet = new Set(sentenceIds);
      
      const matchingAnnotations = allAnnotationsInSection.filter(annotation => {
        // Koşul 1: Cümle ID'si eşleşiyor mu?
        const annotationSentenceIds = Array.isArray(annotation.sentence_id)
          ? annotation.sentence_id
          : [annotation.sentence_id];
        const hasSentenceMatch = annotationSentenceIds.some((id: string) => sentenceIdsSet.has(id));

        if (hasSentenceMatch) {
          return true;
        }

        // Koşul 2: Metin içeriği eşleşiyor mu? (Eğer cümle ID'si eşleşmiyorsa)
        const annotationText = annotation.selected_text.toLowerCase().trim();
        const searchText = selectedText.toLowerCase().trim();

        // İçerme kontrolü (en kapsayıcı yöntem)
        if (annotationText.includes(searchText) || searchText.includes(annotationText)) {
          return true;
        }
        
        return false; // Hiçbir koşul sağlanmadı
      });

      setAnnotations(matchingAnnotations);

    } catch (err: any) {
      console.error('[useAnnotationSearch] Error:', err);
      setError(err.message || 'Şerhler aranırken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [selectedText, sentenceIds, bookId, sectionId, isPublicOnly]);

  useEffect(() => {
    loadAnnotations();
  }, [loadAnnotations]);

  return { annotations, loading, error, reload: loadAnnotations };
}
