import { useState, useEffect, useMemo, useCallback } from 'react';
import { X, Edit3, Trash2, Share2, User, Calendar, Globe, Lock, ChevronLeft, ChevronRight, Palette } from 'lucide-react';
import SherhIcon from '@shared/components/Icons/SherhIcon';
import BookmarkIcon from '@shared/components/Icons/BookmarkIcon';
import NoteIcon from '@shared/components/Icons/NoteIcon';
import HighlightIcon from '@shared/components/Icons/HighlightIcon';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import type { Annotation } from '../../shared/types';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useSettingsStore } from '@domains/settings/store/settingsstore';
import { ConfirmationModal } from '@shared/components/molecules/Feedback/ConfirmationModal';
import { AnnotationToast } from '../../shared/components/AnnotationToast';
import { useCollectionManager } from '../../bookmarks/hooks/useCollectionManager';
import { collectionService } from '../../bookmarks/services/collectionService';

interface AnnotationDetailSheetProps {
  annotations: Annotation[];
  initialAnnotationId: string;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (annotation: Annotation) => void;
  onDelete: (annotationId: string) => Promise<boolean>;
  onChangeColor?: (annotation: Annotation, newColor: string) => Promise<boolean>;
}

export function AnnotationDetailSheet({
  annotations,
  initialAnnotationId,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onChangeColor
}: AnnotationDetailSheetProps) {
  const { user } = useAuthStore();
  const { highlightColors } = useSettingsStore();
  
  // Dinamik renklar için auto overlay hook'ları
  const dynamicColor15 = useAutoOverlay(15, 'var(--bg-color)');
  const dynamicColor25 = useAutoOverlay(25, 'var(--bg-color)');
  
  // Convert settings store colors to array with proper names for color picker
  const colorOptions = Object.keys(highlightColors).map((key, index) => {
    const colorKey = key as keyof typeof highlightColors;
    const colorNumber = key.split('_')[2] || (index + 1).toString();
    const colorValue = highlightColors[colorKey];
    
    // Dinamik renk kontrolü
    let displayColor = colorValue;
    let name = `Vurgu Rengi ${colorNumber}`;
    
    if (colorValue === 'AUTO_OVERLAY_15') {
      displayColor = dynamicColor15;
      name = `Dinamik Renk 15%`;
    } else if (colorValue === 'AUTO_OVERLAY_25') {
      displayColor = dynamicColor25;
      name = `Dinamik Renk 25%`;
    }
    
    return {
      color: displayColor,
      name,
      key: colorKey,
      identifier: `HIGHLIGHT_COLOR_${colorNumber}` // Color identifier for database
    };
  });
  
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDeleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [isColorPickerOpen, setIsColorPickerOpen] = useState(false);
  const [isChangingColor, setIsChangingColor] = useState(false);
  const [toastState, setToastState] = useState({ isVisible: false, message: '', type: 'success' as 'success' | 'error' });
  const { collections, loadCollections } = useCollectionManager();
  const [linkedCollectionIds, setLinkedCollectionIds] = useState<Set<string>>(new Set());
  const [linking, setLinking] = useState<string | null>(null);

  const showToast = useCallback((message: string, type: 'success' | 'error' = 'success') => {
    setToastState({ isVisible: true, message, type });
  }, []);

  const hideToast = useCallback(() => {
    setToastState(prev => ({ ...prev, isVisible: false }));
  }, []);

  useEffect(() => {
    if (isOpen) {
      const initialIndex = annotations.findIndex(a => a.id === initialAnnotationId);
      setCurrentIndex(initialIndex >= 0 ? initialIndex : 0);
    }
  }, [isOpen, initialAnnotationId, annotations]);

  const currentAnnotation = useMemo(() => {
    if (!annotations || annotations.length === 0) return null;
    return annotations[currentIndex];
  }, [currentIndex, annotations]);

  useEffect(() => {
    const loadLinkedCollections = async () => {
      if (!currentAnnotation || currentAnnotation.annotation_type !== 'bookmark') {
        setLinkedCollectionIds(new Set());
        return;
      }
      await loadCollections();
      const res = await collectionService.getCollectionsForAnnotation(currentAnnotation.id);
      if (res.data) setLinkedCollectionIds(new Set(res.data));
      else setLinkedCollectionIds(new Set());
    };
    loadLinkedCollections();
  }, [currentAnnotation, loadCollections]);

  const sheetBgColor = useAutoOverlay(10, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const borderColor = 'color-mix(in srgb, var(--text-color) 20%, transparent)';

  const handleNext = () => setCurrentIndex((prev) => (prev + 1) % annotations.length);
  const handlePrev = () => setCurrentIndex((prev) => (prev - 1 + annotations.length) % annotations.length);
  const handleDelete = () => { if (currentAnnotation) setDeleteConfirmOpen(true); };

  const handleConfirmDelete = async () => {
    if (!currentAnnotation) return;
    setIsDeleting(true);
    const success = await onDelete(currentAnnotation.id);
    setDeleteConfirmOpen(false);
    if (success) {
      showToast('Not başarıyla silindi.', 'success');
      onClose();
    } else {
      showToast('Not silinirken bir hata oluştu.', 'error');
    }
    setIsDeleting(false);
  };
  
  const handleShare = () => {
    if (currentAnnotation?.id) {
      navigator.clipboard.writeText(`${window.location.origin}/shared/annotation/${currentAnnotation.id}`);
      showToast('Paylaşım linki panoya kopyalandı!', 'success');
    }
  };

  const handleColorChange = async (newColor: string) => {
    if (!currentAnnotation || !onChangeColor) return;
    setIsChangingColor(true);
    const success = await onChangeColor(currentAnnotation, newColor);
    setIsColorPickerOpen(false);
    if (success) {
      showToast('Vurgu rengi başarıyla değiştirildi.', 'success');
    } else {
      showToast('Renk değiştirirken bir hata oluştu.', 'error');
    }
    setIsChangingColor(false);
  };

  const getTypeConfig = (type: Annotation['annotation_type']) => {
    switch (type) {
      case 'note': return { Icon: NoteIcon as any, label: 'Not' };
      case 'sherh': return { Icon: SherhIcon as any, label: 'Şerh' };
      case 'highlight': return { Icon: HighlightIcon as any, label: 'Vurgu' };
      case 'bookmark': return { Icon: BookmarkIcon as any, label: 'Yer İmi' };
      default: return { Icon: SherhIcon as any, label: 'Şerh' };
    }
  };

  if (!isOpen || !currentAnnotation) return null;

  const { Icon, label } = getTypeConfig(currentAnnotation.annotation_type);
  const isOwner = user && currentAnnotation.user_id === user.id;

  return (
    <>
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-md z-50 flex items-end md:items-center justify-center"
        onClick={(e) => e.target === e.currentTarget && onClose()}
      >
        <div
          className="relative w-full max-w-[37.8rem] max-h-[90vh] md:max-h-[80vh] rounded-t-2xl md:rounded-2xl shadow-2xl overflow-hidden border flex flex-col"
          style={{ backgroundColor: sheetBgColor, borderColor }}
        >
          {/* Header */}
          <div className="flex items-center justify-between px-4 py-3" style={{ backgroundColor: 'transparent' }}>
            <h2 className="flex items-center gap-3 text-lg font-semibold text-[var(--text-color)]">
              <Icon size={20} className="text-[var(--color-primary)]" />
              <span>{label} Detayı</span>
            </h2>
            <button onClick={onClose} className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/10 text-[var(--text-color)]">
              <X size={18} />
            </button>
          </div>
          
          {/* Scrollable Content */}
          <div className="overflow-y-auto flex-1 px-5 pt-1 pb-5">
            <div className="space-y-3">
              {/* Quoted Text */}
              <div className="px-3 pb-3 pt-0">
                <h3 className="text-xs font-medium text-[var(--text-color)]/60 mb-1">Alıntılanan Metin</h3>
                <div className="pl-4 border-l-4" style={{ borderLeftColor: 'color-mix(in srgb, var(--text-color) 40%, transparent)' }}>
                  <p className="italic leading-relaxed text-[var(--text-color)]/85">“{currentAnnotation.selected_text}”</p>
                </div>
              </div>

              {/* Annotation Content */}
              {currentAnnotation.annotation_content && (
                <div className="p-3 rounded-xl border shadow-sm" style={{ backgroundColor: listAreaBgColor, borderColor }}>
                  <h3 className="text-xs font-medium text-[var(--text-color)]/60 mb-2">İçerik</h3>
                  <p className="whitespace-pre-wrap leading-relaxed text-[var(--text-color)]/90">{currentAnnotation.annotation_content}</p>
                </div>
              )}

              {/* Bookmark Collections - inline toggle list (editless) */}
              {currentAnnotation.annotation_type === 'bookmark' && (
                <div className="p-3 rounded-xl" style={{ backgroundColor: listAreaBgColor }}>
                  <h3 className="text-xs font-medium text-[var(--text-color)]/60 mb-2">Koleksiyonlar</h3>
                  {collections && collections.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {collections.map((col) => {
                        const isLinked = linkedCollectionIds.has(col.id);
                        const isBusy = linking === col.id;
                        return (
                          <button
                            key={col.id}
                            onClick={async () => {
                              if (!currentAnnotation || isBusy) return;
                              try {
                                setLinking(col.id);
                                if (isLinked) {
                                  const res = await collectionService.unlinkAnnotationFromCollections(currentAnnotation.id, [col.id]);
                                  if (!res.error) {
                                    setLinkedCollectionIds(prev => { const next = new Set(prev); next.delete(col.id); return next; });
                                    showToast('Koleksiyon bağlantısı kaldırıldı.');
                                  }
                                } else {
                                  const res = await collectionService.linkAnnotationToCollections(currentAnnotation.id, [col.id]);
                                  if (!res.error) {
                                    setLinkedCollectionIds(prev => new Set(prev).add(col.id));
                                    showToast('Koleksiyon bağlandı.');
                                  }
                                }
                              } finally {
                                setLinking(null);
                              }
                            }}
                            className={`flex items-center justify-between w-full p-3 rounded-lg border transition-colors ${isLinked ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/8'} ${isBusy ? 'opacity-50 cursor-wait' : ''}`}
                            style={{ borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)' }}
                          >
                            <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>{col.name}</span>
                            <div>
                              <BookmarkIcon size={18} color={'var(--text-color)'} filled={isLinked} />
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>Henüz koleksiyonunuz yok.</div>
                  )}
                </div>
              )}

              {/* Highlight Color Info */}
              {currentAnnotation.annotation_type === 'highlight' && (
                <div className="p-3 rounded-xl" style={{ backgroundColor: listAreaBgColor }}>
                  <h3 className="text-xs font-medium text-[var(--text-color)]/60 mb-2">Vurgu Bilgisi</h3>
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full border-2 border-[var(--text-color)]/20" style={{ backgroundColor: currentAnnotation.color }} />
                      <span className="text-sm text-[var(--text-color)]/80">{currentAnnotation.highlight_style === 'text' ? 'Metin Rengi' : 'Arka Plan Rengi'}</span>
                    </div>
                    {isColorPickerOpen && onChangeColor && (
                      <div className="flex items-center gap-1 p-2 rounded-lg" style={{ backgroundColor: listAreaBgColor }}>
                        {colorOptions.map((colorOption) => (
                          <button
                            key={colorOption.key}
                            onClick={() => handleColorChange(colorOption.identifier)}
                            disabled={isChangingColor}
                            className="w-6 h-6 rounded-full border-2 border-[var(--text-color)]/20 hover:scale-110 transition-transform disabled:opacity-50"
                            style={{ backgroundColor: colorOption.color }}
                            title={colorOption.name}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Metadata */}
              <div className="p-3 rounded-xl" style={{ backgroundColor: listAreaBgColor }}>
                <div className="text-xs flex flex-wrap items-center gap-x-4 gap-y-1 text-[var(--text-color)]/70">
                  <div className="flex items-center gap-1.5"><User size={14} /><span>{isOwner ? 'Siz' : 'Diğer Kullanıcı'}</span></div>
                  <div className="flex items-center gap-1.5"><Calendar size={14} /><span>{new Date(currentAnnotation.created_at).toLocaleDateString('tr-TR')}</span></div>
                  {!(currentAnnotation.annotation_type === 'note' && currentAnnotation.book_id !== 'quran') && (
                    <div className="flex items-center gap-1.5">{currentAnnotation.is_public ? <Globe size={14} /> : <Lock size={14} />}<span>{currentAnnotation.is_public ? 'Herkese Açık' : 'Özel'}</span></div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-3 mt-auto" style={{ backgroundColor: sheetBgColor }}>
            <div className="flex items-center gap-2">
              {annotations.length > 1 && (
                <>
                  <button onClick={handlePrev} className="p-2 rounded-full hover:bg-[var(--text-color)]/10 text-[var(--text-color)]" title="Önceki"><ChevronLeft size={18} /></button>
                  <span className="text-sm font-mono text-[var(--text-color)]/80 min-w-[50px] text-center">{currentIndex + 1} / {annotations.length}</span>
                  <button onClick={handleNext} className="p-2 rounded-full hover:bg-[var(--text-color)]/10 text-[var(--text-color)]" title="Sonraki"><ChevronRight size={18} /></button>
                </>
              )}
            </div>
            <div className="flex items-center gap-1">
              <button onClick={handleShare} className="p-2 rounded-full hover:bg-[var(--text-color)]/10 text-[var(--text-color)]" title="Paylaş"><Share2 size={18} /></button>
              {isOwner && (
                <>
                  {/* Highlight için renk değiştirme butonu */}
                  {currentAnnotation.annotation_type === 'highlight' && onChangeColor ? (
                    <button 
                      onClick={() => setIsColorPickerOpen(!isColorPickerOpen)} 
                      disabled={isChangingColor}
                      className="p-2 rounded-full hover:bg-[var(--text-color)]/10 text-[var(--text-color)] disabled:opacity-50" 
                      title="Renk Değiştir"
                    >
                      <Palette size={18} />
                    </button>
                  ) : (currentAnnotation.annotation_type === 'note' || currentAnnotation.annotation_type === 'sherh') ? (
                    <button onClick={() => onEdit(currentAnnotation)} className="p-2 rounded-full hover:bg-[var(--text-color)]/10 text-[var(--text-color)]" title="Düzenle"><Edit3 size={18} /></button>
                  ) : null}
                  <button onClick={handleDelete} disabled={isDeleting} className="p-2 rounded-full hover:bg-[var(--text-color)]/10 text-[var(--text-color)] disabled:opacity-50" title="Sil"><Trash2 size={18} /></button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      <ConfirmationModal
        isOpen={isDeleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
        onConfirm={handleConfirmDelete}
        title={`${label} Sil`}
        message={`Bu ${label.toLowerCase()} notunu kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`}
        confirmText="Evet, Sil"
        cancelText="İptal"
        isLoading={isDeleting}
      />
      <AnnotationToast
        isVisible={toastState.isVisible}
        message={toastState.message}
        type={toastState.type}
        onClose={hideToast}
      />
    </>
  );
}
