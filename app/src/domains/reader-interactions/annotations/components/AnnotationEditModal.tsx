import { useState, useEffect, useRef, useMemo } from 'react';
import { X, Save } from 'lucide-react';
import MessageIcon from '@shared/components/Icons/SherhIcon';
import BookmarkIcon from '@shared/components/Icons/BookmarkIcon';
import NoteIcon from '@shared/components/Icons/NoteIcon';
import HighlightIcon from '@shared/components/Icons/HighlightIcon';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import type { Annotation, UpdateAnnotationInput } from '../../shared/types';
import { useAnnotationManager } from '../hooks/useAnnotationManager';
// Bookmark koleksiyon linkleme/düzenleme bu modaldan kaldırıldı

interface AnnotationEditModalProps {
  annotation: Annotation;
  isOpen: boolean;
  onClose: () => void;
  onUpdated: (updatedAnnotation: Annotation) => void;
}

export function AnnotationEditModal({
  annotation,
  isOpen,
  onClose,
  onUpdated
}: AnnotationEditModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const { updateAnnotation } = useAnnotationManager();
  // Bookmark koleksiyon yönetimi bu modaldan kaldırıldı

  const modalBgColor = useAutoOverlay(6, 'var(--bg-color)');
  const borderColor = useAutoOverlay(15, 'var(--bg-color)');
  const inputBgColor = useAutoOverlay(4, 'var(--bg-color)');

  const [content, setContent] = useState(annotation.annotation_content || '');
  const [isPublic, setIsPublic] = useState(annotation.is_public || false);
  const [isLoading, setIsLoading] = useState(false);

  const typeConfig = useMemo(() => {
    switch (annotation.annotation_type) {
      case 'note':
        return { Icon: NoteIcon as any, title: 'Notu Düzenle', contentLabel: 'Not İçeriği *', placeholder: 'Notunuzu yazın...' };
      case 'sherh':
        return { Icon: MessageIcon as any, title: 'Şerhi Düzenle', contentLabel: 'Şerh İçeriği *', placeholder: 'Şerhinizi yazın...' };
      case 'highlight':
        return { Icon: HighlightIcon as any, title: 'Vurguyu Düzenle', contentLabel: 'Açıklama', placeholder: 'Vurgulama için bir açıklama ekleyin...' };
      case 'bookmark':
        return { Icon: BookmarkIcon as any, title: 'Yer İmini Düzenle', contentLabel: 'Açıklama', placeholder: 'Yer imi için bir açıklama ekleyin...' };
      default:
        return { Icon: MessageSquare, title: 'Düzenle', contentLabel: 'İçerik', placeholder: 'İçeriğinizi yazın...' };
    }
  }, [annotation.annotation_type]);

  useEffect(() => {
    if (isOpen) {
      setContent(annotation.annotation_content || '');
      setIsPublic(annotation.is_public || false);
    }
  }, [isOpen, annotation]);

  useEffect(() => {
    if (!isOpen) return;
    const handleEscape = (e: KeyboardEvent) => e.key === 'Escape' && onClose();
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) onClose();
  };

  const handleSubmit = async () => {
    if (isLoading) return;
    if ((annotation.annotation_type === 'note' || annotation.annotation_type === 'sherh') && !content.trim()) {
      alert(`${typeConfig.contentLabel.replace(' *', '')} boş olamaz`);
      return;
    }

    try {
      setIsLoading(true);
      const nextIsPublic =
        annotation.annotation_type === 'sherh'
          ? isPublic
          : (annotation.annotation_type === 'note'
              ? (annotation.book_id === 'quran' ? isPublic : false)
              : false);

      const updateData: UpdateAnnotationInput = {
        annotation_content: content.trim() || undefined,
        is_public: nextIsPublic,
        metadata: annotation.metadata || {},
        tags: [],
      };

      const updatedAnnotation = await updateAnnotation(annotation.id, updateData);
      if (updatedAnnotation) onUpdated(updatedAnnotation);
      else alert('Güncelleme sırasında bir hata oluştu');
    } catch (error) {
      console.error('[AnnotationEditModal] Update error:', error);
      alert('Güncelleme sırasında bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const { Icon, title, contentLabel, placeholder } = typeConfig;

  return (
    <div className="fixed inset-0 bg-black/40 backdrop-blur-sm flex items-end md:items-center justify-center z-50 p-4" onClick={handleBackdropClick}>
      <div ref={modalRef} className="relative w-full max-w-lg rounded-2xl shadow-2xl transform transition-all duration-300 max-h-[90vh] overflow-hidden border flex flex-col" style={{ backgroundColor: modalBgColor, borderColor: borderColor }} onClick={(e) => e.stopPropagation()}>
        {/* FİNAL VERSİYON: DENGELİ VE HİZALI YAPI */}
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Icon size={20} />
            <h2 className="text-lg font-semibold">{title}</h2>
          </div>
          <button onClick={onClose} className="p-2 rounded-full transition-colors hover:bg-gray-500/10"><X size={20} /></button>
        </div>

        <div className="overflow-y-auto flex-1 min-h-0 p-6 space-y-6" style={{ borderColor: borderColor }}>
          <div>
            <p className="text-sm mb-2 font-medium" style={{ color: 'var(--text-color)', opacity: 0.7 }}>Alıntılanan Metin</p>
            <div className="p-3 rounded-lg text-sm italic" style={{ backgroundColor: inputBgColor, color: 'var(--text-color)', opacity: 0.8 }}>
              "{annotation.selected_text.length > 200 ? `${annotation.selected_text.substring(0, 200)}...` : annotation.selected_text}"
            </div>
          </div>

          {/* Bookmark için koleksiyon düzenleme bu modaldan kaldırıldı */}
          <div>
            <label className="block text-sm font-medium mb-2">{contentLabel}</label>
            <div 
              className="rounded-lg border-2 border-dashed transition-all duration-300"
              style={{
                backgroundColor: inputBgColor,
                borderColor: content.trim() ? 'var(--text-color)' : borderColor
              }}
            >
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={placeholder}
                className="w-full h-32 p-3 bg-transparent resize-none focus:outline-none"
                style={{ color: 'var(--text-color)' }}
                rows={4}
                autoFocus
              />
            </div>
          </div>
          {(
            // Show public toggle only for: sherh (always) or note (only in Quran)
            (annotation.annotation_type === 'sherh') ||
            (annotation.annotation_type === 'note' && annotation.book_id === 'quran')
          ) && (
            <div className="flex items-center justify-between p-4 rounded-lg" style={{ backgroundColor: inputBgColor }}>
              <span className="text-sm font-medium">Diğer kullanıcılarla paylaş</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" checked={isPublic} onChange={(e) => setIsPublic(e.target.checked)} className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:after:bg-[var(--bg-color)]" style={{ backgroundColor: isPublic ? 'var(--text-color)' : '' }}></div>
              </label>
            </div>
          )}
        </div>

        <div className="flex space-x-3 p-4">
          <button onClick={onClose} disabled={isLoading} className="flex-1 py-3 px-4 border rounded-lg disabled:opacity-50 transition-colors font-medium hover:bg-gray-500/10" style={{ borderColor: borderColor, color: 'var(--text-color)' }}>İptal</button>
          <button onClick={handleSubmit} disabled={isLoading || ((annotation.annotation_type === 'note' || annotation.annotation_type === 'sherh') && !content.trim())} className="flex-1 flex items-center justify-center space-x-2 py-3 px-6 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold shadow-md hover:shadow-lg hover:opacity-90" style={{ backgroundColor: 'var(--text-color)', color: 'var(--bg-color)' }}>
            <Save size={18} />
            <span className="text-base">{isLoading ? 'Güncelleniyor...' : 'Güncelle'}</span>
          </button>
        </div>
      </div>
    </div>
  );
}
