import { useState, useMemo, useEffect, useRef } from 'react';
import { X, Clock, MessageSquare, ArrowUpDown, User, RefreshCw, Calendar } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { LoadingState, EmptyState } from '@shared/components';
import { AnnotationErrorBoundary } from '../../shared/components/AnnotationErrorBoundary';
import type { Annotation } from '../../shared/types';
import { useAnnotationSearch } from '../hooks/useAnnotationSearch';
import { supabase } from '@shared/utils/supabaseClient';

// Helper component for truncating text
const TruncatedText = ({ text, maxLength = 350 }: { text: string | null; maxLength?: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text) {
    return null;
  }

  if (text.length <= maxLength) {
    return <p className="text-sm whitespace-pre-wrap text-[var(--text-color)]">{text}</p>;
  }

  return (
    <>
      <p className="text-sm whitespace-pre-wrap text-[var(--text-color)]">
        {isExpanded ? text : `${text.substring(0, maxLength)}...`}
      </p>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="text-sm font-semibold text-[var(--color-primary)] mt-2 hover:underline"
      >
        {isExpanded ? 'Daha az göster' : 'Devamını oku'}
      </button>
    </>
  );
};

const getUserDisplay = (profile: { username?: string; display_name?: string } | undefined, userId: string) => {
  if (!profile) return `...${userId.slice(-6)}`;
  const { display_name, username } = profile;
  // En iyi pratik: `@` işaretini veritabanında değil, arayüzde eklemek.
  if (display_name && username) return `${display_name} @${username}`;
  if (username) return `@${username}`;
  return display_name || `...${userId.slice(-6)}`;
};

const AnnotationCard = ({ annotation, profile, cardBgColor, borderColor, formatDate }: {
  annotation: Annotation;
  profile: { username?: string; display_name?: string } | undefined;
  cardBgColor: string;
  borderColor: string;
  formatDate: (date: string) => string;
}) => {
  const userDisplay = getUserDisplay(profile, annotation.user_id);

  return (
    <div className="p-4 rounded-lg" style={{ backgroundColor: cardBgColor }}>
      <div className="flex items-center justify-between mb-3 pb-2 border-b" style={{ borderColor }}>
        <div className="flex items-center gap-1.5 text-xs text-[var(--text-color)]/60">
          <User size={14} />
          <span>{userDisplay}</span>
        </div>
        <div className="flex items-center gap-1.5 text-xs text-[var(--text-color)]/60">
          <Calendar size={14} />
          <span>{formatDate(annotation.created_at)}</span>
        </div>
      </div>
      <TruncatedText text={annotation.annotation_content || null} />
    </div>
  );
};

interface AnnotationSearchSheetProps {
  isOpen: boolean;
  onClose: () => void;
  selectedText: string;
  sentenceIds: string[];
  bookId?: string;
  sectionId?: string;
}

type SortOption = 'newest' | 'oldest';

export function AnnotationSearchSheet({
  isOpen,
  onClose,
  selectedText,
  sentenceIds,
  bookId,
  sectionId,
}: AnnotationSearchSheetProps) {
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [userProfiles, setUserProfiles] = useState<Record<string, { username?: string; display_name?: string }>>({});
  const fetchedUserIds = useRef<Set<string>>(new Set());

  const sheetBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const cardBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const borderColor = 'color-mix(in srgb, var(--text-color) 15%, transparent)';

  const {
    annotations,
    loading,
    error,
    reload,
  } = useAnnotationSearch({
    selectedText,
    sentenceIds,
    bookId,
    sectionId,
    isPublicOnly: true,
  });

  // Kullanıcı profillerini toplu çek
  useEffect(() => {
    const fetchProfiles = async () => {
      const uniqueUserIds = Array.from(new Set(annotations.map(a => a.user_id)));
      // Sadece daha önce çekilmemişleri sorgula
      const toFetch = uniqueUserIds.filter(id => !fetchedUserIds.current.has(id));
      if (toFetch.length === 0) return;
      // Supabase'den toplu çek
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, display_name')
        .in('id', toFetch);
      
      if (error) {
        console.error("Error fetching user profiles:", error);
      } else if (data) {
        const newProfiles: Record<string, { username?: string; display_name?: string }> = {};
        for (const p of data) {
          newProfiles[p.id] = { username: p.username, display_name: p.display_name };
          fetchedUserIds.current.add(p.id);
        }
        setUserProfiles(prev => ({ ...prev, ...newProfiles }));
      }
    };
    if (annotations.length > 0) fetchProfiles();
  }, [annotations]);

  const sortedAnnotations = useMemo(() => {
    return [...annotations].sort((a, b) => {
      if (sortBy === 'newest') {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
      return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
    });
  }, [annotations, sortBy]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-end justify-center md:items-center"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <div className="absolute inset-0 bg-black/40 backdrop-blur-sm" />
      <div
        className="relative w-full max-w-2xl max-h-[90vh] md:max-h-[80vh] rounded-t-2xl md:rounded-2xl shadow-2xl overflow-hidden border flex flex-col"
        style={{ backgroundColor: sheetBgColor, borderColor }}
      >
        {/* Unified Content Area */}
        <div className="overflow-y-auto flex-1 p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="flex items-center gap-3 text-lg font-semibold text-[var(--text-color)]">
              <MessageSquare size={20} className="text-[var(--color-primary)]" />
              <span>Topluluk Şerhleri</span>
            </h2>
            <button onClick={onClose} className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/10 text-[var(--text-color)]">
              <X size={18} />
            </button>
          </div>

          <blockquote className="text-sm italic p-3 rounded-lg bg-black/5 dark:bg-white/5 text-[var(--text-color)]/80 mb-4">
            "{selectedText.length > 150 ? selectedText.substring(0, 150) + '...' : selectedText}"
          </blockquote>

          <div className="flex items-center justify-between text-sm mb-6">
              <span className="text-[var(--text-color)]/70">
                  {loading ? 'Aranıyor...' : `${sortedAnnotations.length} sonuç bulundu`}
              </span>
              <button
                onClick={() => setSortBy(prev => prev === 'newest' ? 'oldest' : 'newest')}
                className="flex items-center gap-2 text-sm p-2 rounded-lg hover:bg-[var(--text-color)]/10 transition-colors"
              >
                <ArrowUpDown size={14} className="text-[var(--text-color)]/60" />
                <span className="text-[var(--text-color)] font-medium">
                  {sortBy === 'newest' ? 'Önce en yeni' : 'Önce en eski'}
                </span>
              </button>
          </div>
          
          {loading ? (
            <LoadingState message="Şerhler yükleniyor..." />
          ) : error ? (
            <div className="p-8 text-center text-[var(--text-color)]/80">
              <h3 className="text-lg font-semibold text-red-500 mb-2">Hata Oluştu</h3>
              <p className="text-sm mb-4">{error}</p>
              <button onClick={reload} className="px-4 py-2 bg-red-500/90 text-white rounded-lg text-sm font-medium hover:bg-red-500 transition-colors flex items-center gap-2 mx-auto">
                <RefreshCw size={14} />
                Tekrar Dene
              </button>
            </div>
          ) : sortedAnnotations.length === 0 ? (
            <EmptyState message="Bu metin için herkese açık bir şerh bulunamadı." />
          ) : (
            <AnnotationErrorBoundary>
              <div className="space-y-3">
                {sortedAnnotations.map(annotation =>
                  <AnnotationCard
                    key={annotation.id}
                    annotation={annotation}
                    profile={userProfiles[annotation.user_id]}
                    cardBgColor={cardBgColor}
                    borderColor={borderColor}
                    formatDate={formatDate}
                  />
                )}
              </div>
            </AnnotationErrorBoundary>
          )}
        </div>
      </div>
    </div>
  );
}
