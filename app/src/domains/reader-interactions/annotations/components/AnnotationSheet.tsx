/**
 * AnnotationSheet Component
 * NoteBottomSheet'in tasarımına uygun modern annotation sheet'i
 */
import React, { useState, useEffect } from 'react';
import { X, Check } from 'lucide-react';
import MessageIcon from '@shared/components/Icons/SherhIcon';
import BookmarkIcon from '@shared/components/Icons/BookmarkIcon';
import NoteIcon from '@shared/components/Icons/NoteIcon';
import HighlightIcon from '@shared/components/Icons/HighlightIcon';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import CompactBgColorPicker from '@shared/color/components/CompactBgColorPicker';
 import type { AnnotationType, CreateAnnotationInput } from '../../shared/types';

 interface AnnotationSheetProps {
  isOpen: boolean;
  selectedText: string;
  annotationType: AnnotationType;
  onClose: () => void;
  onSubmit: (input: CreateAnnotationInput) => Promise<void>;
  isLoading?: boolean;
  // Toggle for allowing public share option visibility
  allowPublicToggle?: boolean;
}

export function AnnotationSheet({
  isOpen,
  selectedText,
  annotationType,
  onClose,
  onSubmit,
  isLoading = false,
  allowPublicToggle = true
}: AnnotationSheetProps) {
  const [content, setContent] = useState('');
  const [isPublic, setIsPublic] = useState(false); // Yeni state
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [selectedBg, setSelectedBg] = useState<string | undefined>(undefined);

  // NoteBottomSheet'e benzer color palette
  const sheetBg = 'var(--bg-color)';
  const borderColor = useAutoOverlay(21, 'var(--bg-color)');
  const cardBg = useAutoOverlay(2, 'var(--bg-color)');
  const successColor = '#10b981';

  // Type config
  const getTypeConfig = () => {
    switch (annotationType) {
      case 'note':
        return {
          icon: NoteIcon as any,
          title: 'Not Ekle',
          color: '#3b82f6',
          placeholder: 'Notunuzu yazın...'
        };
      case 'sherh':
        return {
          icon: MessageIcon as any,
          title: 'Şerh Ekle',
          color: '#8b5cf6',
          placeholder: 'Şerhinizi yazın...'
        };
      case 'highlight':
        return {
          icon: HighlightIcon as any,
          title: 'Vurgula',
          color: '#f59e0b',
          placeholder: 'Vurgu açıklaması (opsiyonel)...'
        };
      case 'bookmark':
        return {
          icon: BookmarkIcon as any,
          title: 'Yer İmi Ekle',
          color: '#10b981',
          placeholder: 'Yer imi açıklaması (opsiyonel)...'
        };
      default:
        return {
          icon: MessageSquare,
          title: 'Annotation Ekle',
          color: '#6b7280',
          placeholder: 'İçeriğinizi yazın...'
        };
    }
  };

  const typeConfig = getTypeConfig();

  // Reset form when sheet opens
  useEffect(() => {
    if (isOpen) {
      setContent('');
      setIsPublic(false); // Reset on open
      setIsSaving(false);
      setSelectedBg(undefined);
    }
  }, [isOpen]);

  // Handle submit - NoteBottomSheet'e benzer
  const handleSubmit = async () => {
    if (isSaving || isLoading) {
      return;
    }

    // Note ve Sherh için content zorunlu
    if ((annotationType === 'note' || annotationType === 'sherh') && !content.trim()) {
      return;
    }

    setIsSaving(true);
    try {
      const input: Partial<CreateAnnotationInput> = {
        annotation_type: annotationType,
        annotation_content: content.trim() || undefined,
        color: selectedBg || typeConfig.color,
        tags: [],
        is_public: isPublic, // State'den al
        metadata: {}
      };

      await onSubmit(input as CreateAnnotationInput);
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        onClose();
      }, 1500);
    } catch (error) {
      console.error('[AnnotationSheet] Submit error:', error);
      alert('Kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const IconComponent = typeConfig.icon;

  return (
    <div className="fixed inset-0 z-50 flex items-end md:items-center justify-center md:p-4">
      {/* NoteBottomSheet tarzı backdrop */}
      <div 
        className="absolute inset-0 bg-black/25 backdrop-blur-sm transition-all duration-300"
        onClick={handleBackdropClick}
      />

      {/* NoteBottomSheet tarzı sheet - Mobile full width */}
      <div
        className="relative w-full md:max-w-md bg-white rounded-t-3xl md:rounded-3xl shadow-2xl md:border overflow-hidden transform transition-all duration-300 max-h-[85vh] md:max-h-[90vh] md:h-[700px] h-[85vh] flex flex-col"
        style={{
          backgroundColor: sheetBg,
          borderColor: borderColor,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15)'
        }}
      >
        {/* NoteBottomSheet tarzı header */}
        <div className="flex items-center gap-3 px-5 py-2" style={{ borderColor: borderColor }}>
          <div 
            className="w-9 h-9 rounded-lg flex items-center justify-center relative overflow-hidden"
            style={{ backgroundColor: 'var(--text-color)/10' }}
          >
            <IconComponent size={16} style={{ color: 'var(--text-color)' }} />
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-lg" />
          </div>
          <div className="flex-1 min-w-0">
            <h2 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
              {typeConfig.title}
            </h2>
            <p className="text-xs opacity-50 truncate" style={{ color: 'var(--text-color)' }}>
              {selectedText}
            </p>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-red-50 hover:text-red-500 transition-all duration-200 group"
            style={{ color: 'var(--text-color)' }}
          >
            <X size={14} className="group-hover:scale-110 transition-transform" />
          </button>
        </div>

        {/* NoteBottomSheet tarzı content */}
        <div className="flex-1 flex flex-col p-6 min-h-0">
          {/* NoteBottomSheet tarzı writing zone */}
          <div 
            className="flex-1 rounded-2xl p-6 border-2 border-dashed transition-all duration-300 relative min-h-0"
            style={{
              backgroundColor: cardBg,
              borderColor: content.trim() ? 'var(--text-color)' : borderColor
            }}
          >
            {/* NoteBottomSheet tarzı floating hint */}
            {!content.trim() && (
              <div className="absolute inset-0 flex items-center justify-center opacity-30 pointer-events-none">
                <div className="flex flex-col items-center gap-3">
                  <div className="p-3 rounded-full" style={{ backgroundColor: 'var(--text-color)/10' }}>
                    <IconComponent size={24} style={{ color: 'var(--text-color)' }} />
                  </div>
                  <p className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
                    {typeConfig.placeholder}
                  </p>
                </div>
              </div>
            )}
            
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder=""
              className="w-full h-full bg-transparent text-base leading-relaxed resize-none focus:outline-none relative z-10"
              style={{ color: 'var(--text-color)' }}
              disabled={isSaving || isLoading}
            />
          </div>

          {/* Arka Plan Rengi - ColorPicker’daki arka plan seçeneklerinin kompakt hali */}
          {(annotationType === 'note' || annotationType === 'sherh') && (
            <div className="mt-3">
              <label className="block text-xs font-medium mb-2" style={{ color: 'var(--text-color)' }}>
                Arka Plan Rengi
              </label>
              <CompactBgColorPicker
                selected={selectedBg}
                onSelect={setSelectedBg}
                includeNone
              />
            </div>
          )}

          {/* Public/Private Toggle - only when allowed */}
          {allowPublicToggle && (annotationType === 'sherh' || annotationType === 'highlight' || annotationType === 'note') && (
            <div className="flex items-center justify-between mt-4 p-4 rounded-lg" style={{ backgroundColor: cardBg }}>
              <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                Diğer kullanıcılarla paylaş
              </span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={isPublic}
                  onChange={(e) => setIsPublic(e.target.checked)}
                  className="sr-only peer"
                />
                <div
                  className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:after:bg-[var(--bg-color)]"
                  style={{ backgroundColor: isPublic ? 'var(--text-color)' : '' }}
                ></div>
              </label>
            </div>
          )}

          {/* NoteBottomSheet tarzı bottom bar */}
          <div className="flex items-center justify-between mt-4" style={{ borderColor: borderColor }}>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="text-xs font-medium opacity-40" style={{ color: 'var(--text-color)' }}>
                  {content.length}
                </div>
                <div className="text-xs opacity-40" style={{ color: 'var(--text-color)' }}>
                  karakter
                </div>
              </div>
              {content.trim() && (
                <div className="flex items-center gap-1 text-xs px-2 py-1 rounded-full" style={{ backgroundColor: 'var(--text-color)/15', color: 'var(--text-color)' }}>
                  <Check size={12} />
                  Hazır
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 rounded-xl font-medium transition-all duration-200 hover:bg-gray-50"
                style={{ color: 'var(--text-color)' }}
              >
                İptal
              </button>
              
              <button
                onClick={handleSubmit}
                disabled={!content.trim() || isSaving || isLoading}
                className="px-6 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 min-w-[130px] justify-center shadow-lg hover:shadow-xl relative overflow-hidden"
                style={{
                  backgroundColor: showSuccess ? successColor : 'var(--text-color)',
                  color: 'var(--bg-color)',
                  transform: showSuccess ? 'scale(1.05)' : 'scale(1)',
                  boxShadow: showSuccess 
                    ? `0 10px 30px ${successColor}30`
                    : `0 6px 20px color-mix(in srgb, var(--text-color) 30%, transparent)`
                }}
              >
                {showSuccess && (
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent animate-pulse" />
                )}
                {showSuccess ? (
                  <>
                    <Check size={16} />
                    Kaydedildi!
                  </>
                ) : isSaving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span className="ml-1">Kaydediliyor</span>
                  </>
                ) : (
                  <>
                    <Check size={16} />
                    Kaydet
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
