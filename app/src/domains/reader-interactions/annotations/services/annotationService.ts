import { supabase } from '@shared/utils/supabaseClient';
// ✅ YENİ ORGANİZE YAPISI - Shared types
import type {
  Annotation,
  CreateAnnotationInput,
  UpdateAnnotationInput,
  AnnotationFilters,
  AnnotationStats,
  AnnotationServiceResponse
} from '../../shared/types';

/**
 * 🛡️ Sentence ID validation - geçersiz ID'leri filtreler
 */
function isValidSentenceId(sentenceId: string): boolean {
  if (!sentenceId || typeof sentenceId !== 'string') {
    return false;
  }

  // Geçersiz pattern'leri kontrol et
  const invalidPatterns = [
    /^invalid-sentence-/,  // "invalid-sentence-" ile başlayanlar
    /^temp-/,              // "temp-" ile başlayanlar
    /^placeholder-/,       // "placeholder-" ile başlayanlar
    /^error-/,             // "error-" ile başlayanlar
  ];

  // Herhangi bir geç<PERSON><PERSON> pattern eşleşirse false döndür
  for (const pattern of invalidPatterns) {
    if (pattern.test(sentenceId)) {
      return false;
    }
  }

  // Minimum uzunluk kontrolü
  if (sentenceId.length < 3) {
    return false;
  }

  // Maksimum uzunluk kontrolü (çok uzun ID'ler de şüpheli)
  if (sentenceId.length > 50) {
    return false;
  }

  // Geçerli format kontrolü - örnek: "9_1_13" formatı
  const validFormatPattern = /^\d+_\d+_\d+$/;
  if (validFormatPattern.test(sentenceId)) {
    return true;
  }

  // Diğer geçerli formatlar için ek kontroller eklenebilir
  // Şimdilik sadece sayı_sayı_sayı formatını kabul ediyoruz
  return false;
}

/**
 * Supabase bağlantısını kontrol eder
 */
async function checkSupabaseConnection(): Promise<{ isConnected: boolean; error?: string }> {
  try {
    // Basit bir health check query
    const { error } = await supabase
      .from('text_annotations')
      .select('count', { count: 'exact', head: true })
      .limit(1);

    if (error) {
      // Network hatalarını daha zarif bir şekilde ele al
      if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
        console.warn('[checkSupabaseConnection] Network error, working offline:', error.message);
        return { isConnected: false, error: 'Network connection issue' };
      }
      
      console.error('[checkSupabaseConnection] Query error:', error);
      return {
        isConnected: false,
        error: `Database error: ${error.message}`
      };
    }

    return { isConnected: true };
  } catch (error) {
    console.warn('[checkSupabaseConnection] Connection failed, working offline:', error);
    return {
      isConnected: false,
      error: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Kullanıcının authentication durumunu kontrol eder
 */
async function checkAuthentication(): Promise<{ isAuthenticated: boolean; userId?: string; error?: string }> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      // Network hatalarını daha zarif bir şekilde ele al
      if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
        console.warn('[checkAuthentication] Network error, working offline:', error.message);
        return {
          isAuthenticated: false,
          error: 'Network connection issue - working offline'
        };
      }
      
      console.error('[checkAuthentication] Auth error:', error);
      return {
        isAuthenticated: false,
        error: `Auth error: ${error.message}`
      };
    }

    if (!user) {
      return {
        isAuthenticated: false,
        error: 'User not authenticated'
      };
    }

    return {
      isAuthenticated: true,
      userId: user.id
    };
  } catch (error) {
    console.error('[checkAuthentication] Exception:', error);
    return {
      isAuthenticated: false,
      error: `Auth check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}



/**
 * Input validation ve sanitization
 */
function validateAndSanitizeInput(input: CreateAnnotationInput): CreateAnnotationInput {
  // Text length validation
  if (!input.selected_text || input.selected_text.length > 10000) {
    throw new Error('Selected text is invalid or too long (max 10000 characters)');
  }

  if (input.annotation_content && input.annotation_content.length > 50000) {
    throw new Error('Annotation content is too long (max 50000 characters)');
  }

  // 🛡️ Sentence ID validation
  const sentenceIds = Array.isArray(input.sentence_id) ? input.sentence_id : [input.sentence_id];
  const validSentenceIds = sentenceIds.filter(id => isValidSentenceId(id));

  if (validSentenceIds.length === 0) {
    throw new Error('No valid sentence IDs provided');
  }

  // Geçersiz ID'ler sessizce filtrelendi

  // Basic XSS prevention - remove script tags and dangerous content
  const sanitizeText = (text: string): string => {
    return text
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  };

  return {
    ...input,
    sentence_id: validSentenceIds, // 🛡️ Geçersiz ID'ler filtrelenmiş hali
    selected_text: sanitizeText(input.selected_text),
    annotation_content: input.annotation_content ? sanitizeText(input.annotation_content) : input.annotation_content,
    tags: input.tags?.map(tag => sanitizeText(tag).substring(0, 50)) || [], // Limit tag length
  };
}

/**
 * Rate limiting check
 */
const rateLimitMap = new Map<string, number[]>();
function checkRateLimit(userId: string): void {
  const now = Date.now();
  const userRequests = rateLimitMap.get(userId) || [];

  // Remove requests older than 1 minute
  const recentRequests = userRequests.filter(time => now - time < 60000);

  // Allow max 20 requests per minute
  if (recentRequests.length >= 20) {
    throw new Error('Rate limit exceeded. Please wait before creating more annotations.');
  }

  // Update rate limit map
  rateLimitMap.set(userId, [...recentRequests, now]);
}

/**
 * Annotation Service
 * Supabase ile annotation CRUD işlemlerini yöneten servis
 */
export const annotationService = {

  /**
   * Yeni annotation oluşturur
   */
  async createAnnotation(input: CreateAnnotationInput): Promise<AnnotationServiceResponse<Annotation>> {
    try {
      // SECURITY: Validate and sanitize input
      const sanitizedInput = validateAndSanitizeInput(input);

      // SECURITY: Check rate limit
      if (sanitizedInput.user_id) {
        checkRateLimit(sanitizedInput.user_id);
      }

      const { data, error } = await supabase
        .from('text_annotations')
        .insert({
          ...sanitizedInput,
          color: sanitizedInput.color || '#fbbf24',
          is_public: sanitizedInput.is_public || false,
          tags: sanitizedInput.tags || [],
          metadata: sanitizedInput.metadata || {}
        })
        .select()
        .single();

      if (error) {
        console.error('[annotationService] Create error:', error);
        return {
          data: null,
          error: {
            code: 'CREATE_FAILED',
            message: 'Annotation oluşturulamadı',
            details: error
          }
        };
      }

      return { data, error: null };
    } catch (error) {
      console.error('[annotationService] Create exception:', error);
      return {
        data: null,
        error: {
          code: 'CREATE_EXCEPTION',
          message: 'Beklenmeyen hata oluştu',
          details: error
        }
      };
    }
  },

  /**
   * Annotation'ı günceller
   */
  async updateAnnotation(id: string, input: UpdateAnnotationInput): Promise<AnnotationServiceResponse<Annotation>> {
    try {
      const { data, error } = await supabase
        .from('text_annotations')
        .update({
          ...input,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('[annotationService] Update error:', error);
        return {
          data: null,
          error: {
            code: 'UPDATE_FAILED',
            message: 'Annotation güncellenemedi',
            details: error
          }
        };
      }

      return { data, error: null };
    } catch (error) {
      console.error('[annotationService] Update exception:', error);
      return {
        data: null,
        error: {
          code: 'UPDATE_EXCEPTION',
          message: 'Beklenmeyen hata oluştu',
          details: error
        }
      };
    }
  },

  /**
   * Annotation'ı siler
   */
  async deleteAnnotation(id: string): Promise<AnnotationServiceResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('text_annotations')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('[annotationService] Delete error:', error);
        return {
          data: null,
          error: {
            code: 'DELETE_FAILED',
            message: 'Annotation silinemedi',
            details: error
          }
        };
      }

      return { data: true, error: null };
    } catch (error) {
      console.error('[annotationService] Delete exception:', error);
      return {
        data: null,
        error: {
          code: 'DELETE_EXCEPTION',
          message: 'Beklenmeyen hata oluştu',
          details: error
        }
      };
    }
  },

  /**
   * Seçilen metinle eşleşen annotation'ları akıllı şekilde temizler
   * - Tam eşleşme: Annotation'ı siler
   * - Kısmi eşleşme: Annotation'ı böler ve sadece seçilen kısmı çıkarır
   */
  async smartDeleteAnnotationsBySelection(
    bookId: string,
    sectionId: string,
    selectedText: string,
    userId: string
  ): Promise<AnnotationServiceResponse<{ deletedCount: number; modifiedCount: number }>> {
    try {
      // Önce eşleşen annotation'ları bul (tüm bilgileri al)
      const { data: annotations, error: findError } = await supabase
        .from('text_annotations')
        .select('*')
        .eq('book_id', bookId)
        .eq('section_id', sectionId)
        .eq('user_id', userId)
        .eq('annotation_type', 'highlight');

      if (findError) {
        console.error('[annotationService] Find annotations error:', findError);
        return {
          data: null,
          error: {
            code: 'FIND_FAILED',
            message: 'Annotation\'lar bulunamadı',
            details: findError
          }
        };
      }

      if (!annotations || annotations.length === 0) {
        return { data: { deletedCount: 0, modifiedCount: 0 }, error: null };
      }

      let deletedCount = 0;
      let modifiedCount = 0;

      // Her annotation'ı kontrol et
      for (const annotation of annotations) {
        const annotationText = annotation.selected_text;

        // Tam eşleşme kontrolü
        if (annotationText === selectedText) {
          // Tam eşleşme - annotation'ı sil
          const { error: deleteError } = await supabase
            .from('text_annotations')
            .delete()
            .eq('id', annotation.id);

          if (!deleteError) {
            deletedCount++;
          }
          continue;
        }

        // Kısmi eşleşme kontrolü
        const selectedIndex = annotationText.indexOf(selectedText);
        if (selectedIndex !== -1) {


          // Seçilen metin annotation içinde bulundu
          const beforeText = annotationText.substring(0, selectedIndex);
          const afterText = annotationText.substring(selectedIndex + selectedText.length);



          // Orijinal annotation'ı sil
          const { error: deleteError } = await supabase
            .from('text_annotations')
            .delete()
            .eq('id', annotation.id);

          if (deleteError) {
            console.error('[annotationService] Delete original annotation error:', deleteError);
            continue;
          }

          // Yeni annotation'lar oluştur (boş olmayan kısımlar için)
          const newAnnotations = [];

          if (beforeText.trim()) {
            // Önceki kısım için yeni annotation
            const beforeAnnotation = {
              user_id: annotation.user_id,
              book_id: annotation.book_id,
              section_id: annotation.section_id,
              sentence_id: annotation.sentence_id,
              selected_text: beforeText.trim(),
              selection_start: annotation.selection_start,
              selection_end: annotation.selection_start + beforeText.length,
              prefix_text: annotation.prefix_text, // Aynı prefix
              suffix_text: selectedText + afterText, // Yeni suffix
              word_proximity: annotation.word_proximity, // Aynı proximity
              text_hash: annotation.text_hash, // Aynı hash
              sentence_hash: annotation.sentence_hash, // Aynı sentence hash
              annotation_type: annotation.annotation_type,
              annotation_content: annotation.annotation_content,
              color: annotation.color,
              highlight_style: annotation.highlight_style,
              is_public: annotation.is_public,
              tags: annotation.tags,
              metadata: annotation.metadata
            };
            newAnnotations.push(beforeAnnotation);
          }

          if (afterText.trim()) {
            // Sonraki kısım için yeni annotation
            const afterAnnotation = {
              user_id: annotation.user_id,
              book_id: annotation.book_id,
              section_id: annotation.section_id,
              sentence_id: annotation.sentence_id,
              selected_text: afterText.trim(),
              selection_start: annotation.selection_start + selectedIndex + selectedText.length,
              selection_end: annotation.selection_end,
              prefix_text: beforeText + selectedText, // Yeni prefix
              suffix_text: annotation.suffix_text, // Aynı suffix
              word_proximity: annotation.word_proximity, // Aynı proximity
              text_hash: annotation.text_hash, // Aynı hash
              sentence_hash: annotation.sentence_hash, // Aynı sentence hash
              annotation_type: annotation.annotation_type,
              annotation_content: annotation.annotation_content,
              color: annotation.color,
              highlight_style: annotation.highlight_style,
              is_public: annotation.is_public,
              tags: annotation.tags,
              metadata: annotation.metadata
            };
            newAnnotations.push(afterAnnotation);
          }

          // Yeni annotation'ları ekle
          if (newAnnotations.length > 0) {


            const { error: insertError } = await supabase
              .from('text_annotations')
              .insert(newAnnotations)
              .select();

            if (insertError) {
              console.error('[smartDeleteAnnotationsBySelection] Insert error:', insertError);
            } else {
              modifiedCount++;
            }
          } else {
            // Hiç kısım kalmadı, sadece silindi
            deletedCount++;
          }
        }
      }

      return {
        data: { deletedCount, modifiedCount },
        error: null
      };
    } catch (error) {
      console.error('[annotationService] Smart delete by selection exception:', error);
      return {
        data: null,
        error: {
          code: 'SMART_DELETE_EXCEPTION',
          message: 'Beklenmeyen hata oluştu',
          details: error
        }
      };
    }
  },

  /**
   * Seçilen metinle eşleşen annotation'ları bulur ve siler (eski basit versiyon)
   */
  async deleteAnnotationsBySelection(
    bookId: string,
    sectionId: string,
    selectedText: string,
    userId: string
  ): Promise<AnnotationServiceResponse<number>> {
    try {
      // Önce eşleşen annotation'ları bul
      const { data: annotations, error: findError } = await supabase
        .from('text_annotations')
        .select('id, selected_text')
        .eq('book_id', bookId)
        .eq('section_id', sectionId)
        .eq('user_id', userId)
        .eq('annotation_type', 'highlight');

      if (findError) {
        console.error('[annotationService] Find annotations error:', findError);
        return {
          data: null,
          error: {
            code: 'FIND_FAILED',
            message: 'Annotation\'lar bulunamadı',
            details: findError
          }
        };
      }

      if (!annotations || annotations.length === 0) {
        return { data: 0, error: null }; // Silinecek annotation yok
      }

      // Seçilen metinle eşleşen annotation'ları filtrele
      const matchingAnnotations = annotations.filter(annotation => {
        // Tam eşleşme veya içerme kontrolü
        return annotation.selected_text === selectedText ||
               annotation.selected_text.includes(selectedText) ||
               selectedText.includes(annotation.selected_text);
      });

      if (matchingAnnotations.length === 0) {
        return { data: 0, error: null }; // Eşleşen annotation yok
      }

      // Eşleşen annotation'ları sil
      const annotationIds = matchingAnnotations.map(a => a.id);
      const { error: deleteError } = await supabase
        .from('text_annotations')
        .delete()
        .in('id', annotationIds);

      if (deleteError) {
        console.error('[annotationService] Delete annotations error:', deleteError);
        return {
          data: null,
          error: {
            code: 'DELETE_MULTIPLE_FAILED',
            message: 'Annotation\'lar silinemedi',
            details: deleteError
          }
        };
      }

      return { data: matchingAnnotations.length, error: null };
    } catch (error) {
      console.error('[annotationService] Delete by selection exception:', error);
      return {
        data: null,
        error: {
          code: 'DELETE_BY_SELECTION_EXCEPTION',
          message: 'Beklenmeyen hata oluştu',
          details: error
        }
      };
    }
  },

  /**
   * Annotation'ları filtreler ve getirir
   */
  async getAnnotations(filters: AnnotationFilters = {}): Promise<AnnotationServiceResponse<Annotation[]>> {
    try {
      // Önce bağlantıyı kontrol et
      const connectionCheck = await checkSupabaseConnection();
      if (!connectionCheck.isConnected) {
        console.warn('[annotationService] Working offline - no annotations available');
        // Offline modda boş array döndür
        return {
          data: [],
          error: null
        };
      }

      // Authentication kontrol et
      const authCheck = await checkAuthentication();
      if (!authCheck.isAuthenticated) {
        // Network hatası ise offline mod
        if (authCheck.error?.includes('Network connection issue')) {
          console.warn('[annotationService] Auth failed due to network, working offline');
          return {
            data: [],
            error: null
          };
        }
        
        console.error('[annotationService] Auth check failed:', authCheck.error);
        return {
          data: null,
          error: {
            code: 'AUTH_FAILED',
            message: 'Kullanıcı girişi gerekli',
            details: authCheck.error
          }
        };
      }

      let query = supabase
        .from('text_annotations')
        .select('*')
        .order('created_at', { ascending: false });

      // Filtreleri uygula
      if (filters.book_id) {
        query = query.eq('book_id', filters.book_id);
      }
      if (filters.section_id) {
        query = query.eq('section_id', filters.section_id);
      }
      if (filters.annotation_type) {
        query = query.eq('annotation_type', filters.annotation_type);
      }
      // Priority filter kaldırıldı - gereksizdi
      if (filters.is_public !== undefined) {
        query = query.eq('is_public', filters.is_public);
      }
      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id);
      }
      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps('tags', filters.tags);
      }

      const { data, error } = await query;

      if (error) {
        console.error('[annotationService] Get error:', error);
        return {
          data: null,
          error: {
            code: 'GET_FAILED',
            message: 'Annotation\'lar getirilemedi',
            details: error
          }
        };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('[annotationService] Get exception:', error);
      return {
        data: null,
        error: {
          code: 'GET_EXCEPTION',
          message: 'Beklenmeyen hata oluştu',
          details: error
        }
      };
    }
  },

  /**
   * Tek annotation getirir
   */
  async getAnnotation(id: string): Promise<AnnotationServiceResponse<Annotation>> {
    try {
      const { data, error } = await supabase
        .from('text_annotations')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('[annotationService] Get single error:', error);
        return {
          data: null,
          error: {
            code: 'GET_SINGLE_FAILED',
            message: 'Annotation bulunamadı',
            details: error
          }
        };
      }

      return { data, error: null };
    } catch (error) {
      console.error('[annotationService] Get single exception:', error);
      return {
        data: null,
        error: {
          code: 'GET_SINGLE_EXCEPTION',
          message: 'Beklenmeyen hata oluştu',
          details: error
        }
      };
    }
  },

  /**
   * Kullanıcının annotation istatistiklerini getirir
   */
  async getAnnotationStats(userId?: string): Promise<AnnotationServiceResponse<AnnotationStats>> {
    try {
      let query = supabase
        .from('text_annotations')
        .select('annotation_type, created_at');

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('[annotationService] Stats error:', error);
        return {
          data: null,
          error: {
            code: 'STATS_FAILED',
            message: 'İstatistikler getirilemedi',
            details: error
          }
        };
      }

      // İstatistikleri hesapla
      const stats: AnnotationStats = {
        total_count: data.length,
        by_type: {
          note: data.filter(a => a.annotation_type === 'note').length,
          sherh: data.filter(a => a.annotation_type === 'sherh').length,
          highlight: data.filter(a => a.annotation_type === 'highlight').length,
          bookmark: data.filter(a => a.annotation_type === 'bookmark').length
        },
        // Priority stats kaldırıldı - gereksizdi
        recent_count: data.filter(a => {
          const createdAt = new Date(a.created_at);
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          return createdAt > weekAgo;
        }).length
      };

      return { data: stats, error: null };
    } catch (error) {
      console.error('[annotationService] Stats exception:', error);
      return {
        data: null,
        error: {
          code: 'STATS_EXCEPTION',
          message: 'Beklenmeyen hata oluştu',
          details: error
        }
      };
    }
  }
};
