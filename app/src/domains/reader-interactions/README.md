# 📖 Reader Interactions Domain

Bu domain, okuyucu etkileşimlerini (annotation, highlight, bookmark vb.) yöneten gelişmiş modülleri içerir. Kullanıcıların metinlerle etkileşime geçmesini, şerh eklemesini ve annotation'ları aramasını sağlar.

## 🏗️ **YENİ ORGANİZE YAPISI** (v2.0)

Domain artık **feature-based** olarak organize edilmiştir:

```
📁 reader-interactions/
├── 📝 annotations/          # Şerh (Note) sistemi
├── 🎨 highlights/           # Vurgulama sistemi
├── 📖 bookmarks/            # Yer imi sistemi
├── ✋ text-selection/       # Metin seçimi altyapısı
└── 🔧 shared/               # Ortak component'ler
```

## ✨ Özellikler

### 🎯 **Core Features**
- **Text Selection** - Akıllı metin seçimi ve pozisyon hesaplama
- **Multi-Sentence Support** - Çoklu sentence annotation'ları (max 20)
- **Annotation Types** - <PERSON><PERSON><PERSON>, vurg<PERSON><PERSON>, yer imi
- **Real-time Search** - Otomatik annotation arama ("Şerh Bul")
- **Hybrid UI** - Bottom sheet + full page görünüm

### 🎨 **Advanced Highlighting**
- **HTML-Aware Rendering** - Bold, italic, span tag'leri korunur
- **Arabic Text Support** - Font-size, dotted underlines, tooltips preserved
- **Multi-Style Support** - Background + text color combinations
- **Position Recovery** - 3-stage fallback system (exact → context → fuzzy)
- **Smart Deletion** - Temizle butonu ile annotation silme

### 🔍 **Advanced Search**
- **Smart Matching** - Text benzerliği algoritması (%60+ threshold)
- **Sentence-based Filtering** - JSON array sentence ID support
- **Multi-criteria Search** - Type, date, author filtreleri
- **Performance Optimized** - On-demand loading + memoization

### 📱 **User Experience**
- **Touch Optimized** - Mobile-first tasarım, scroll sorunları çözüldü
- **Responsive Design** - Desktop ve mobile uyumlu
- **Accessibility** - Klavye navigasyonu + ARIA labels
- **Error Handling** - Kapsamlı hata yönetimi + recovery
- **Theme Integration** - CSS variables ile tema uyumlu

## 📁 Yeni Klasör Yapısı

```
app/src/domains/reader-interactions/
├── 📝 annotations/                  # Şerh (Note) Sistemi
│   ├── components/
│   │   ├── AnnotationSheet.tsx      ✅ Annotation ekleme sheet'i
│   │   ├── AnnotationEditModal.tsx  ✅ Annotation düzenleme modal'ı
│   │   └── AnnotationSearchSheet.tsx ✅ Annotation arama sheet'i
│   ├── hooks/
│   │   └── useAnnotationManager.ts  ✅ Annotation CRUD işlemleri
│   ├── services/
│   │   └── annotationService.ts     ✅ Supabase annotation servisi
│   └── pages/
│       ├── AnnotationDetailPage.tsx ✅ Annotation detay sayfası
│       └── AnnotationSearchPage.tsx ✅ Annotation arama sayfası
│
├── 🎨 highlights/                   # Vurgulama Sistemi
│   ├── components/
│   │   ├── TextHighlighter.tsx      ✅ Gelişmiş HTML-aware highlighting
│   │   ├── ColorPicker.tsx          ✅ Renk seçici + temizle butonu
│   │   └── AnnotationVisualizer.tsx ✅ Annotation görselleştirme
│   ├── types/
│   │   └── highlight.types.ts       ✅ Highlighting type definitions
│   └── index.ts                     ✅ Export'lar
│
├── 📖 bookmarks/                    # Yer İmi Sistemi
│   ├── components/
│   │   └── BookmarkBottomSheet.tsx  ✅ Bookmark koleksiyon seçici
│   ├── hooks/
│   │   └── useCollectionManager.ts  ✅ Koleksiyon yönetimi
│   ├── services/
│   │   ├── collectionService.ts     ✅ Koleksiyon servisi
│   │   └── savedContentService.ts   ✅ Kaydedilen içerik servisi
│   └── pages/
│       └── SavedContentPage.tsx     ✅ Kaydedilen içerik sayfası
│
├── ✋ text-selection/               # Metin Seçimi Altyapısı
│   ├── components/
│   │   ├── TextSelectionHandler.tsx ✅ Metin seçimi yönetimi
│   │   └── TextActionMenu.tsx       ✅ Metin seçim menüsü
│   ├── hooks/
│   │   └── useTextSelectionHandler.ts ✅ Metin seçimi hook'u
│   └── utils/
│       ├── textUtils.ts             ✅ Metin işleme + Hash fonksiyonları
│       └── annotationUtils.ts       ✅ Annotation recovery + Validation
│
└── 🔧 shared/                       # Ortak Component'ler
    ├── components/
    │   ├── AnnotationToast.tsx      ✅ Bildirim component'i
    │   ├── AnnotationErrorBoundary.tsx ✅ Hata yakalama
    │   ├── AnnotationSkeleton.tsx   ✅ Loading skeleton'ları
    │   ├── AnnotationFloatingPanel.tsx ✅ Floating panel
    │   └── AnnotationDebugger.tsx   ✅ Debug araçları
    ├── hooks/
    │   ├── useBottomSheet.ts        ✅ Bottom sheet state
    │   └── useReaderInteractionStyles.ts ✅ Styling hook'u
    ├── constants/
    │   └── ui.ts                    ✅ UI sabitleri
    └── types/
        └── index.ts                 ✅ Ortak tipler
```

## 🗄️ Veritabanı Yapısı

### text_annotations Tablosu (Güncel)
```sql
CREATE TABLE text_annotations (
  -- Temel bilgiler
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Metin referansı
  book_id TEXT NOT NULL,
  section_id TEXT NOT NULL,
  sentence_id JSONB NOT NULL, -- 🆕 JSON array for multi-sentence support

  -- Pozisyon bilgileri
  selection_start INTEGER NOT NULL,
  selection_end INTEGER NOT NULL,
  selected_text TEXT NOT NULL,

  -- Recovery sistemi (gelişmiş)
  prefix_text TEXT NOT NULL,
  suffix_text TEXT NOT NULL,
  word_proximity TEXT[] NOT NULL,
  text_hash TEXT NOT NULL,
  sentence_hash TEXT NOT NULL,

  -- Annotation içeriği
  annotation_type TEXT NOT NULL DEFAULT 'note',
  annotation_content TEXT,

  -- Görsel özellikler (gelişmiş)
  color TEXT DEFAULT '#fbbf24',
  highlight_style TEXT DEFAULT 'background', -- 🆕 'background' | 'text'

  -- Sosyal & organizasyon
  is_public BOOLEAN DEFAULT false,
  tags TEXT[] DEFAULT '{}',
  metadata JSONB DEFAULT '{}',

  -- Bookmark koleksiyonu
  collection_id UUID REFERENCES bookmark_collections(id) ON DELETE SET NULL,

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- 🆕 Constraints
  CONSTRAINT valid_annotation_type CHECK (annotation_type IN ('note', 'highlight', 'bookmark')),
  CONSTRAINT valid_highlight_style CHECK (highlight_style IN ('background', 'text')),
  CONSTRAINT valid_selection_range CHECK (selection_start >= 0 AND selection_end > selection_start),
  CONSTRAINT valid_sentence_id_array CHECK (jsonb_typeof(sentence_id) = 'array')
);

-- 🆕 Performance indexes
CREATE INDEX idx_text_annotations_user_book ON text_annotations(user_id, book_id);
CREATE INDEX idx_text_annotations_sentence_id ON text_annotations USING GIN(sentence_id);
CREATE INDEX idx_text_annotations_type ON text_annotations(annotation_type);
CREATE INDEX idx_text_annotations_created_at ON text_annotations(created_at DESC);
```

## 🚀 Kurulum

### 1. Supabase Tablo Oluşturma
```bash
# Supabase SQL Editor'da çalıştırın:
cat app/src/domains/reader-interactions/sql/create_annotations_table.sql
```

### 2. Entegrasyon
RisaleContent komponenti zaten entegre edilmiştir:

```tsx
// YENİ YÖNTEM (Önerilen)
import { TextSelectionHandler } from '@domains/reader-interactions/text-selection';
import { AnnotationHighlighter } from '@domains/reader-interactions/highlights';
import { AnnotationFloatingPanel } from '@domains/reader-interactions/shared';

// VEYA namespace style
import { TextSelection, Highlights, Shared } from '@domains/reader-interactions';

// RisaleContent.tsx içinde
<TextSelection.TextSelectionHandler>
  <Highlights.AnnotationHighlighter />
  {/* Metin içeriği */}
</TextSelection.TextSelectionHandler>
<Shared.AnnotationFloatingPanel />
```



**Kontroller:**
- 🎨 **Paintbrush**: Metinde görselleştirmeyi aç/kapat
- 👁️ **Eye**: Detaylı görünümü aç/kapat
- 📍 **MapPin**: Annotation'ı metinde göster
- 🗑️ **Trash**: Annotation'ı sil
- 🔄 **Refresh**: Annotation listesini yenile

## 📖 Kullanım

### Annotation Oluşturma
```tsx
// YENİ YÖNTEM (Önerilen)
import { useAnnotationManager } from '@domains/reader-interactions/annotations';

// VEYA namespace style
import { Annotations } from '@domains/reader-interactions';

function MyComponent() {
  const { createAnnotation } = useAnnotationManager();
  // VEYA: const { createAnnotation } = Annotations.useAnnotationManager();

  const handleCreateNote = async () => {
    await createAnnotation({
      book_id: '5',
      section_id: '1',
      sentence_id: '5_1_1',
      selection_start: 0,
      selection_end: 10,
      selected_text: 'Seçilen metin',
      annotation_type: 'note',
      annotation_content: 'Bu bir not',
      // ... diğer alanlar
    });
  };
}
```

### Annotation Listeleme
```tsx
// YENİ YÖNTEM (Önerilen)
import { useAnnotationManager } from '@domains/reader-interactions/annotations';

function AnnotationList() {
  const { annotations, loading } = useAnnotationManager({
    book_id: '5',
    section_id: '1'
  });

  if (loading) return <div>Yükleniyor...</div>;

  return (
    <div>
      {annotations.map(annotation => (
        <div key={annotation.id}>
          {annotation.selected_text}: {annotation.annotation_content}
        </div>
      ))}
    </div>
  );
}
```

## 🔧 Recovery Sistemi

Annotation pozisyonları 4 katmanlı recovery sistemi ile korunur:

1. **Exact Hash Match**: Orijinal pozisyon + hash kontrolü
2. **Prefix/Suffix Match**: Çevre metinle eşleştirme
3. **Word Proximity**: Yakın kelimelere göre bulma
4. **Fuzzy Search**: Benzerlik algoritması ile arama

```tsx
// YENİ YÖNTEM (Önerilen)
import { findAnnotationInText } from '@domains/reader-interactions/text-selection';

// VEYA namespace style
import { TextSelection } from '@domains/reader-interactions';

const result = await findAnnotationInText(annotation, currentText);
// VEYA: const result = await TextSelection.findAnnotationInText(annotation, currentText);

if (result.isFound) {
  console.log(`Recovered at ${result.position?.start}-${result.position?.end} with confidence ${result.confidence}`);
}
```

## 🎨 Annotation Türleri

### Note (Not)
- Kullanıcının yazdığı detaylı açıklamalar
- Zorunlu `annotation_content` alanı
- Mavi renk teması

### Highlight (Vurgulama)
- Metni görsel olarak vurgulama
- Renk seçimi mevcut
- Opsiyonel not eklenebilir

### Bookmark (Yer İmi)
- Hızlı erişim için işaretleme
- Minimal veri saklama
- Yeşil renk teması

## 🔒 Güvenlik

- **RLS (Row Level Security)**: Kullanıcılar sadece kendi annotation'larını görebilir
- **Public Annotations**: İsteğe bağlı herkese açık annotation'lar
- **Input Validation**: Tüm girişler doğrulanır
- **XSS Protection**: HTML içerik güvenli şekilde işlenir

## 📊 Performans

- **Indexing**: Optimized database indexes
- **Lazy Loading**: İhtiyaç duyulduğunda yükleme
- **Debounced Selection**: Performanslı metin seçimi
- **Memoization**: React optimizasyonları

## 🧪 Test

```bash
# Unit testler (gelecekte eklenecek)
npm test reader-interactions

# E2E testler (gelecekte eklenecek)
npm run e2e:annotations
```

## 🚀 Migration Guide (v1 → v2)

### **Eski Import'lar → Yeni Import'lar**

```tsx
// ❌ ESKİ YÖNTEM
import { TextSelectionHandler } from '@domains/reader-interactions/components/TextSelectionHandler';
import { useAnnotationManager } from '@domains/reader-interactions/hooks/useAnnotationManager';
import { AnnotationHighlighter } from '@domains/reader-interactions/components/AnnotationHighlighter';
import { ColorPicker } from '@domains/reader-interactions/components/ColorPicker';
import { BookmarkBottomSheet } from '@domains/reader-interactions/components/BookmarkBottomSheet';

// ✅ YENİ YÖNTEM (Feature-based)
import { TextSelectionHandler } from '@domains/reader-interactions/text-selection';
import { useAnnotationManager } from '@domains/reader-interactions/annotations';
import { AnnotationHighlighter } from '@domains/reader-interactions/highlights';
import { ColorPicker } from '@domains/reader-interactions/highlights';
import { BookmarkBottomSheet } from '@domains/reader-interactions/bookmarks';

// ✅ VEYA Namespace Style (Önerilen)
import {
  TextSelection,
  Annotations,
  Highlights,
  Bookmarks
} from '@domains/reader-interactions';

const { TextSelectionHandler } = TextSelection;
const { useAnnotationManager } = Annotations;
const { AnnotationHighlighter, ColorPicker } = Highlights;
const { BookmarkBottomSheet } = Bookmarks;
```

### **Avantajlar**
- ✅ **Clear Separation**: Her feature kendi domain'inde
- ✅ **Better Maintainability**: Kod daha organize ve bulunabilir
- ✅ **Reduced Coupling**: Feature'lar arası bağımlılık azaldı
- ✅ **Easier Testing**: Her domain ayrı test edilebilir
- ✅ **Team Collaboration**: Farklı feature'larda paralel çalışma

## 🔮 Gelecek Özellikler

- [ ] **AI Destekli Öneriler**: Akıllı annotation önerileri
- [ ] **Collaborative Annotations**: Çoklu kullanıcı annotation'ları
- [ ] **Export/Import**: Annotation'ları dışa/içe aktarma
- [ ] **Advanced Search**: Gelişmiş arama ve filtreleme
- [ ] **Annotation Analytics**: Kullanım istatistikleri
- [ ] **Mobile Optimizations**: Mobil cihaz optimizasyonları

## 🐛 Bilinen Sorunlar

- Çok uzun metinlerde performans optimizasyonu gerekebilir
- Safari'de bazı selection edge case'leri
- Realtime sync henüz implement edilmedi

## 🤝 Katkıda Bulunma

1. Feature branch oluşturun
2. Değişikliklerinizi yapın
3. Testleri çalıştırın
4. Pull request açın

## 📝 Changelog

### v2.0.0 (Mevcut - Major Update)
- ✅ **Feature-based Domain Organization** - Yeni klasör yapısı
- ✅ **TextHighlighter v2** - HTML-aware + Arabic support
- ✅ **Multi-Sentence Support** - JSON array sentence IDs
- ✅ **Advanced ColorPicker** - Temizle butonu + compact mode
- ✅ **Position Recovery v2** - 3-stage fallback system
- ✅ **Smart Deletion** - Annotation temizleme sistemi
- ✅ **Performance Optimizations** - Memoization + lazy loading
- ✅ **Theme Integration** - CSS variables support

### v1.0.0 (Önceki)
- ✅ Temel annotation sistemi
- ✅ Metin seçimi ve popup
- ✅ Supabase entegrasyonu
- ✅ Recovery algoritmaları
- ✅ TypeScript tipleri

### v2.1.0 (Planlanan)
- 🚧 **Real-time Collaboration** - Multi-user annotations
- 🚧 **AI-Powered Suggestions** - Smart annotation recommendations
- 🚧 **Export/Import** - Annotation data portability
- 🚧 **Advanced Analytics** - Usage statistics
