import { supabase } from '@shared/utils/supabaseClient';
import { CreateAnnotationInput } from '../shared/types/types';

// Hash utility function - Unicode safe
const createHash = (text: string): string => {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      hash = ((hash << 5) - hash + data[i]) & 0xffffffff;
    }
    return Math.abs(hash).toString(36).slice(0, 16);
  } catch (error) {
    console.error('Hash creation error:', error);
    return Math.random().toString(36).slice(0, 16);
  }
};

// 🎯 Enhanced Unified Types
export type ContentType = 'quran' | 'risale' | 'tafsir';
export type AnnotationType = 'bookmark' | 'note' | 'highlight';

export interface ContentPart {
  id: string;
  type: string;
  text: string;
  selected: boolean;
  [key: string]: any;
}

export interface SelectionInfo {
  selected_parts: string[];
  combined_text: string;
  word_count?: number;
  [key: string]: any;
}

export interface StructuredContent {
  content_parts: ContentPart[];
  selection_info: SelectionInfo;
  [key: string]: any; // Content-specific metadata
}

export interface EnhancedAnnotationInput {
  user_id: string;
  content_type: ContentType;
  book_id: string;
  section_id: string;
  annotation_type: AnnotationType;
  structured_content: StructuredContent;
  annotation_content?: string;
  color?: string;
  is_public?: boolean;
  collection_id?: string;
}

/**
 * 🚀 ENHANCED UNIFIED SERVICE
 * - Eski text_annotations tablosunu kullanır
 * - Recovery sistemi korunur (selection_start, prefix_text, text_hash, etc.)
 * - Structured content eklenir
 * - Tüm content türlerini destekler
 */
export class EnhancedUnifiedService {
  
  /**
   * Enhanced annotation kaydet - Recovery sistemi ile
   */
  static async saveAnnotation(input: EnhancedAnnotationInput): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      const {
        user_id,
        content_type,
        book_id,
        section_id,
        annotation_type,
        structured_content,
        annotation_content,
        color = '#10b981',
        is_public = false,
        collection_id
      } = input;



      // Combined text (eski selected_text için) - Not için özel format
      let selected_text = structured_content.selection_info.combined_text;

      // Not için daha bilgilendirici format
      if (annotation_type === 'note') {
        const verse_info = structured_content.verse_info;
        const arabicPart = structured_content.content_parts.find(part => part.type === 'arabic');
        const translationPart = structured_content.content_parts.find(part => part.type === 'translation');

        if (verse_info) {
          let noteText = `${verse_info.surah_name} Suresi, ${verse_info.verse_number}. Ayet\n\n`;

          // Arapça metin varsa ekle (kısaltılmış)
          if (arabicPart?.text) {
            const arabicPreview = arabicPart.text.length > 50
              ? arabicPart.text.slice(0, 50) + '...'
              : arabicPart.text;
            noteText += `${arabicPreview}\n\n`;
          }

          // Meal varsa ekle (kısaltılmış)
          if (translationPart?.text) {
            const translationPreview = translationPart.text.length > 100
              ? translationPart.text.slice(0, 100) + '...'
              : translationPart.text;
            noteText += `${translationPreview}`;
          }

          selected_text = noteText.trim();
        }
      }
      
      // Sentence ID oluştur
      const sentence_id = this.generateSentenceId(content_type, section_id, structured_content);
      
      // Recovery sistemi için prefix/suffix (basit implementasyon)
      const words = selected_text.split(/\s+/);
      const prefix_text = words.slice(0, 3).join(' ');
      const suffix_text = words.slice(-3).join(' ');
      const word_proximity = words.slice(0, 10); // İlk 10 kelime

      // Enhanced annotation input - ESKİ TABLO YAPISI + YENİ STRUCTURED CONTENT
      const annotationData: CreateAnnotationInput = {
        user_id,
        book_id,
        section_id,
        sentence_id: [sentence_id],
        
        // 📍 Recovery sistemi (korundu)
        selection_start: 0,
        selection_end: selected_text.length,
        selected_text,
        prefix_text,
        suffix_text,
        word_proximity,
        text_hash: createHash(selected_text),
        sentence_hash: createHash(sentence_id),
        
        // 🎯 Annotation bilgileri
        annotation_type,
        annotation_content,
        color,
        is_public,
        
        // 📊 Metadata (eski sistem uyumluluğu)
        metadata: {
          content_type,
          ...this.createLegacyMetadata(content_type, structured_content)
        },
        
        // 🏗️ YENİ: Structured content
        structured_content,
        
        // Diğer alanlar
        tags: []
      };



      // Supabase'e kaydet
      const { data, error } = await supabase
        .from('text_annotations')
        .insert({
          ...annotationData,
          content_type, // Yeni alan
          structured_content // Yeni alan
        })
        .select()
        .single();

      if (error) {
        console.error('🚀 EnhancedUnifiedService: Database error:', error);
        return {
          success: false,
          message: 'Kaydetme hatası: ' + error.message
        };
      }



      // Bookmark ise koleksiyona ekle
      if (annotation_type === 'bookmark' && collection_id) {
        const { error: linkError } = await supabase
          .from('bookmark_annotation_collections')
          .insert({
            annotation_id: data.id,
            collection_id: collection_id
          });

        if (linkError) {
          console.warn('🚀 Collection link warning:', linkError);
        }
      }

      return {
        success: true,
        message: `${this.getTypeLabel(annotation_type)} başarıyla kaydedildi!`,
        data
      };

    } catch (error) {
      console.error('🚀 EnhancedUnifiedService: Unexpected error:', error);
      return {
        success: false,
        message: 'Beklenmeyen bir hata oluştu.'
      };
    }
  }

  /**
   * Enhanced annotation'ları getir
   */
  static async getAnnotations(filters: {
    user_id: string;
    content_type?: ContentType;
    book_id?: string;
    section_id?: string;
    annotation_type?: AnnotationType;
  }): Promise<{
    success: boolean;
    data: any[];
  }> {
    try {
      let query = supabase
        .from('text_annotations')
        .select('*')
        .eq('user_id', filters.user_id);

      if (filters.content_type) {
        query = query.eq('content_type', filters.content_type);
      }

      if (filters.book_id) {
        query = query.eq('book_id', filters.book_id);
      }

      if (filters.section_id) {
        query = query.eq('section_id', filters.section_id);
      }

      if (filters.annotation_type) {
        query = query.eq('annotation_type', filters.annotation_type);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('🚀 EnhancedUnifiedService: Get error:', error);
        return { success: false, data: [] };
      }

      return { success: true, data: data || [] };

    } catch (error) {
      console.error('🚀 EnhancedUnifiedService: Get unexpected error:', error);
      return { success: false, data: [] };
    }
  }

  // 🛠️ Helper Methods

  private static generateSentenceId(content_type: ContentType, section_id: string, content: StructuredContent): string {
    switch (content_type) {
      case 'quran':
        const verse_info = content.verse_info;
        return `${verse_info?.surah_id || section_id}_${verse_info?.verse_number || 1}_0`;
      
      case 'risale':
        const first_part = content.content_parts[0];
        return first_part?.id || `${section_id}_1_0`;
      
      case 'tafsir':
        const tafsir_info = content.section_info;
        return `${section_id}_${tafsir_info?.verse_number || 1}_0`;
      
      default:
        return `${section_id}_1_0`;
    }
  }

  private static createLegacyMetadata(content_type: ContentType, content: StructuredContent): Record<string, any> {
    switch (content_type) {
      case 'quran':
        return {
          verse_key: content.verse_info?.verse_key,
          surah_name: content.verse_info?.surah_name,
          verse_number: content.verse_info?.verse_number,
          selected_meals: content.selection_info.selected_parts
        };
      
      case 'risale':
        return {
          chapter_id: content.section_info?.chapter_id,
          section_title: content.section_info?.section_title,
          sentence_range: content.selection_info.sentence_range
        };
      
      case 'tafsir':
        return {
          surah_id: content.section_info?.surah_id,
          verse_number: content.section_info?.verse_number,
          author: content.section_info?.tafsir_author
        };
      
      default:
        return {};
    }
  }

  private static getTypeLabel(type: AnnotationType): string {
    switch (type) {
      case 'bookmark': return 'Yer imi';
      case 'note': return 'Not';
      case 'highlight': return 'Vurgu';
      default: return 'Annotation';
    }
  }

  // 📚 Content Creators

  /**
   * Kuran için structured content oluştur
   */
  static createQuranContent(
    verseData: any,
    selectedMeals: Array<{ id: string; enabled: boolean; name: string }>,
    availableTranslators: Array<{ id: string; name: string }>,
    verseKey: string,
    surahName: string
  ): StructuredContent {
    const content_parts: ContentPart[] = [];
    const selected_parts: string[] = [];
    let combined_text = '';

    // Arabic text
    if (verseData.arabic_text) {
      content_parts.push({
        id: 'arabic',
        type: 'arabic',
        text: verseData.arabic_text,
        language: 'ar',
        selected: true
      });
      selected_parts.push('arabic');
      combined_text += verseData.arabic_text + '\n\n';
    }

    // Translations
    if (selectedMeals && availableTranslators && verseData.translations) {
      selectedMeals.forEach(meal => {
        if (meal.enabled) {
          const translator = availableTranslators.find(t => t.id === meal.id);
          const translationData = verseData.translations[meal.id];
          
          if (translator && translationData) {
            const mealText = Array.isArray(translationData.paragraphs) 
              ? translationData.paragraphs.join(' ')
              : String(translationData);

            content_parts.push({
              id: meal.id,
              type: 'translation',
              text: mealText,
              language: 'tr',
              translator: translator.name,
              selected: true
            });

            selected_parts.push(meal.id);
            combined_text += `[${translator.name}]\n${mealText}\n\n`;
          }
        }
      });
    }

    const [surahId, verseNumber] = verseKey.split('-');

    return {
      verse_info: {
        verse_key: verseKey,
        surah_name: surahName,
        surah_id: parseInt(surahId),
        verse_number: parseInt(verseNumber)
      },
      content_parts,
      selection_info: {
        selected_parts,
        combined_text: combined_text.trim(),
        word_count: combined_text.split(/\s+/).length
      }
    };
  }
}
