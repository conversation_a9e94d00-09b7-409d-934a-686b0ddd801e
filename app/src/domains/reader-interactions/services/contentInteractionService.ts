import { supabase } from '@shared/utils/supabaseClient';

// contentInteractionService.ts

export interface IFavoriteContent {
  id: string;
  user_id: string;
  content_id: string;
  content_type: 'book'; 
  created_at: string;
}

export interface IUserContentActivity {
  id: string;
  user_id: string;
  content_id: string;
  content_type: 'book';
  last_opened_at: string;
  metadata?: any; 
}


export const contentInteractionService = {
  // =================================================================
  // Favori İçerik İşlemleri
  // =================================================================

  /**
   * Bir içeriği favorilere ekler.
   * @param userId - Kullanıcı ID'si
   * @param contentId - Kitap veya içerik ID'si
   * @param contentType - İçerik tipi ('book' vb.)
   */
  async addFavorite(userId: string, contentId: string, contentType: 'book' = 'book'): Promise<IFavoriteContent> {
    const { data, error } = await supabase
      .from('favorite_content')
      .insert({
        user_id: userId,
        content_id: contentId,
        content_type: contentType,
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding favorite:', error.message);
      throw new Error(`Favori eklenemedi: ${error.message}`);
    }
    return data;
  },

  /**
   * Bir içeriği favorilerden kaldırır.
   * @param userId - Kullanıcı ID'si
   * @param contentId - Kitap veya içerik ID'si
   */
  async removeFavorite(userId: string, contentId: string): Promise<void> {
    const { error } = await supabase
      .from('favorite_content')
      .delete()
      .match({ user_id: userId, content_id: contentId });

    if (error) {
      console.error('Error removing favorite:', error.message);
      throw new Error(`Favori kaldırılamadı: ${error.message}`);
    }
  },

  /**
   * Kullanıcının tüm favori içeriklerini getirir.
   * @param userId - Kullanıcı ID'si
   */
  async getFavorites(userId: string): Promise<IFavoriteContent[]> {
    const { data, error } = await supabase
      .from('favorite_content')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching favorites:', error.message);
      throw new Error(`Favoriler alınamadı: ${error.message}`);
    }
    return data || [];
  },
  
  /**
   * Belirli bir içeriğin favori olup olmadığını kontrol eder.
   * @param userId - Kullanıcı ID'si
   * @param contentId - Kitap veya içerik ID'si
   */
  async isFavorite(userId: string, contentId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('favorite_content')
      .select('id')
      .match({ user_id: userId, content_id: contentId })
      .maybeSingle();

    if (error) {
      console.error('Error checking favorite status:', error.message);
      return false; // Hata durumunda favori değil olarak varsayalım
    }
    return !!data;
  },


  // =================================================================
  // Kullanıcı İçerik Aktivitesi İşlemleri
  // =================================================================

  /**
   * Bir içerik açıldığında veya okunduğunda aktiviteyi kaydeder/günceller.
   * @param userId - Kullanıcı ID'si
   * @param contentId - Kitap veya içerik ID'si
   * @param contentType - İçerik tipi ('book' vb.)
   */
  async recordActivity(userId: string, contentId: string, contentType: 'book' = 'book'): Promise<IUserContentActivity> {
    const { data, error } = await supabase
      .from('user_content_activity')
      .upsert({
        user_id: userId,
        content_id: contentId,
        content_type: contentType,
        last_opened_at: new Date().toISOString(),
      }, { onConflict: 'user_id,content_id,content_type' })
      .select()
      .single();

    if (error) {
      console.error('Error recording activity:', error.message);
      throw new Error(`Aktivite kaydedilemedi: ${error.message}`);
    }
    return data;
  },

  /**
   * Kullanıcının son okuduğu içerikleri getirir.
   * @param userId - Kullanıcı ID'si
   * @param limit - Getirilecek içerik sayısı
   */
  async getRecentActivities(userId: string, limit: number = 10): Promise<IUserContentActivity[]> {
    const { data, error } = await supabase
      .from('user_content_activity')
      .select('*')
      .eq('user_id', userId)
      .order('last_opened_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent activities:', error.message);
      throw new Error(`Son okunanlar alınamadı: ${error.message}`);
    }
    return data || [];
  },
};
