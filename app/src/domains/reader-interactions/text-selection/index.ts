/**
 * Text Selection Domain
 * Metin seçimi ve pozisyon yönetimi - Tüm annotation türleri için ortak altyapı
 */

// Components
export { TextSelectionHandler } from './components/TextSelectionHandler';
export { TextActionMenu } from './components/TextActionMenu';

// Hooks
export { useTextSelectionHandler } from './hooks/useTextSelectionHandler';

// Utils
export * from './utils/textUtils';
export * from './utils/annotationUtils';

// Types (re-export from shared)
export type {
  TextSelection,
  AnnotationPosition,
  PositionData
} from '../shared/types';
