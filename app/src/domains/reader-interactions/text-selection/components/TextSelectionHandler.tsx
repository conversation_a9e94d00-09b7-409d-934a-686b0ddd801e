import React, { useCallback, useState, useEffect, useMemo } from 'react';
import { supabase } from '@shared/utils/supabaseClient';
import { useParams } from 'react-router-dom';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { useTextSelectionHandler } from '../hooks/useTextSelectionHandler';
import { useAnnotationManager } from '../../annotations/hooks/useAnnotationManager';
import { useCollectionManager } from '../../bookmarks/hooks/useCollectionManager';
import { useAuth } from '@domains/auth/hooks/useAuth';
import { TextActionMenu } from './TextActionMenu';
import { TextActionPanel } from './TextActionPanel';
import { AnnotationSheet } from '../../annotations/components/AnnotationSheet';
import { AnnotationToast } from '../../shared/components/AnnotationToast';
import { AnnotationSearchSheet } from '../../annotations/components/AnnotationSearchSheet';
import { BookmarkBottomSheet } from '../../bookmarks/components/BookmarkBottomSheet';
import type { CreateAnnotationInput, BookmarkCollection, CreateCollectionInput } from '../../shared/types';
import { useBookService } from '@domains/library/hooks/useBookService';
import { fetchData } from '@shared/utils/dataFetcher';
import { useSurahs } from '@domains/reader/hooks/quran/useSurahs';
import { SentenceTagsSheet } from '@domains/reader/components/tags/SentenceTagsSheet';
import { TagDetailPage } from '@domains/reader/components/tags/TagDetailPage';
import { useIsMobile } from '@shared/hooks/useIsMobile';

interface TextSelectionHandlerProps {
  children: React.ReactNode;
  className?: string;
  onAnnotationCreated?: () => void; // Yeni annotation oluşturulduğunda callback
}

/**
 * Text Selection Handler Component
 * Metin seçimi ve annotation oluşturma işlemlerini yöneten wrapper component
 */
export function TextSelectionHandler({
  children,
  className,
  onAnnotationCreated
}: TextSelectionHandlerProps) {
  const { bookId, sectionId } = useParams<{ bookId: string; sectionId: string }>();
  const { user } = useAuth();
  const { books } = useBookService();
  const { data: surahs } = useSurahs();

  const bookTitleMap = useMemo(() => {
    const map: Record<string, string> = {};
    books.forEach((b) => { map[String(b.id)] = b.title; });
    // Quran özel durum
    map['quran'] = "Kur'an-ı Kerim";
    return map;
  }, [books]);

  const surahMap = useMemo(() => {
    const map: Record<string, string> = {};
    if (surahs) {
      surahs.forEach((s: any) => { map[String(s.id)] = s.name; });
    }
    return map;
  }, [surahs]);

  // Bölüm başlıkları (Risale için index.json'dan)
  const [sectionTitleMap, setSectionTitleMap] = useState<Record<string, string>>({});

  useEffect(() => {
    const loadSectionTitles = async () => {
      if (!bookId || bookId === 'quran') { setSectionTitleMap({}); return; }
      try {
        const indexPath = `risalei_nur/${bookId}/index.json`;
        const raw = await fetchData<any>(indexPath);
        const sections = raw?.sections || raw?.subsections || [];
        const map: Record<string, string> = {};
        sections.forEach((s: any) => { map[String(s.id)] = String(s.title); });
        setSectionTitleMap(map);
      } catch (err) {
        console.warn('[TextSelectionHandler] Bölüm başlıkları yüklenemedi:', err);
        setSectionTitleMap({});
      }
    };
    loadSectionTitles();
  }, [bookId]);

  // Form state (Şerh için)
  const [isFormSheetOpen, setIsFormSheetOpen] = useState(false);
  const [formSelectedText, setFormSelectedText] = useState('');
  const [formSelectionData, setFormSelectionData] = useState<any>(null);

  // ✅ Note sheet state (Not için) - Artık AnnotationSheet kullanılıyor
  const [isNoteSheetOpen, setIsNoteSheetOpen] = useState(false);
  const [noteSelectionData, setNoteSelectionData] = useState<any>(null);

  // Toast state
  const [toastState, setToastState] = useState({
    isVisible: false,
    message: '',
    type: 'success' as 'success' | 'error'
  });

  // Search sheet state
  const [isSearchSheetOpen, setIsSearchSheetOpen] = useState(false);

  // Bookmark sheet state
  const [isBookmarkSheetOpen, setIsBookmarkSheetOpen] = useState(false);

  // Tags sheet state
  const [isTagsSheetOpen, setIsTagsSheetOpen] = useState(false);
  const [selectedTag, setSelectedTag] = useState<{ id: number; name: string } | null>(null);

  const showToast = useCallback((message: string, type: 'success' | 'error' = 'success') => {
    setToastState({ isVisible: true, message, type });
  }, []);

  const hideToast = useCallback(() => {
    setToastState(prev => ({ ...prev, isVisible: false }));
  }, []);

  const {
    selection,
    isSelecting,
    selectionRect,
    clearSelection,
    prepareAnnotationDataWithSelection,
    clearAnnotations, // 🗑️ Yeni silme fonksiyonu
    setIsFormOpen
  } = useTextSelectionHandler();

  // Cihaz türü (mobil mi?)
  const isMobile = useIsMobile();

  // Sheet açık olduğunda form state'ini güncelle (selection koruması için)
  useEffect(() => {
    setIsFormOpen(isFormSheetOpen || isNoteSheetOpen || isSearchSheetOpen || isBookmarkSheetOpen || isTagsSheetOpen || !!selectedTag);
  }, [isFormSheetOpen, isNoteSheetOpen, isSearchSheetOpen, isBookmarkSheetOpen, isTagsSheetOpen, selectedTag, setIsFormOpen]);

  /**
   * Şerh arama handler'ı
   */
  const handleFindAnnotations = useCallback(() => {
    if (!selection?.text) {
      showToast('Arama için metin seçmelisiniz.', 'error');
      return;
    }

    setIsSearchSheetOpen(true);
  }, [selection, showToast]);

  /**
   * Etiketleri göster handler'ı
   */
  const handleShowTags = useCallback(() => {
    if (!selection?.text) {
      showToast('Etiketleri görmek için metin seçmelisiniz.', 'error');
      return;
    }

    setIsTagsSheetOpen(true);
  }, [selection, showToast]);

  /**
   * Tag'e tıklandığında detail sayfasını aç
   */
  const handleTagClick = useCallback((tagId: number, tagName: string) => {
    setSelectedTag({ id: tagId, name: tagName });
    setIsTagsSheetOpen(false);
  }, []);

  /**
   * Tag detail sayfasından geri dön
   */
  const handleTagDetailBack = useCallback(() => {
    setSelectedTag(null);
  }, []);

  // Seçili sentence'ın tag'lerini al
  const selectedSentenceTags = useMemo(() => {
    if (!selection?.sentence_id) return undefined;

    // İlk sentence ID'yi al (çoklu sentence durumunda)
    const sentenceId = Array.isArray(selection.sentence_id)
      ? selection.sentence_id[0]
      : selection.sentence_id;

    // DOM'dan sentence element'ini bul ve tag'leri al
    const sentenceElement = document.querySelector(`[data-sentence-id="${sentenceId}"]`);
    if (!sentenceElement) return undefined;

    // Sentence element'inin data attribute'larından tag'leri al
    const tagsAttr = sentenceElement.getAttribute('data-tags');
    if (!tagsAttr) return undefined;

    try {
      return JSON.parse(tagsAttr);
    } catch {
      return undefined;
    }
  }, [selection?.sentence_id]);

  const {
    annotations,
    createAnnotation,
    smartDeleteAnnotationsBySelection,
    deleteAnnotation,
    loading: annotationLoading
  } = useAnnotationManager();

  const {
    collections,
    createCollection,
    loading: collectionLoading
  } = useCollectionManager();

  /**
   * Basit ve etkili kısmi temizleme
   */
  const handleSmartClear = useCallback(async (
    bookId: string,
    sectionId: string,
    selection: { start: number; end: number; text: string; sentence_id: string | string[] }
  ) => {
    console.log('[handleSmartClear] Başlıyor:', {
      bookId,
      sectionId,
      selection: { start: selection.start, end: selection.end, text: selection.text }
    });

    // Mevcut annotation'ları al (zaten yüklü olan)
    const sentenceIds = Array.isArray(selection.sentence_id) ? selection.sentence_id : [selection.sentence_id];

    // Tüm annotation'ları filtrele
    const allRelevantAnnotations = annotations.filter(annotation => {
      // Bu sentence'a ait mi?
      const annotationSentenceIds = Array.isArray(annotation.sentence_id)
        ? annotation.sentence_id
        : [annotation.sentence_id];

      const hasSentenceMatch = sentenceIds.some(id => annotationSentenceIds.includes(id));

      // Highlight annotation'ı mı?
      const isHighlight = annotation.annotation_type === 'highlight';

      // Pozisyon kesişimi var mı? (hem kısmi hem tam kapsama)
      const hasOverlap = selection.start < annotation.selection_end &&
                        selection.end > annotation.selection_start;

      // TAM KAPSAMA: Seçim annotation'ı tamamen kapsıyor mu?
      const isFullyCovered = selection.start <= annotation.selection_start &&
                            selection.end >= annotation.selection_end;

      return hasSentenceMatch && isHighlight && (hasOverlap || isFullyCovered);
    });

    console.log('[handleSmartClear] Tüm kesişen annotations:', {
      count: allRelevantAnnotations.length,
      annotations: allRelevantAnnotations.map(a => ({
        id: a.id,
        start: a.selection_start,
        end: a.selection_end,
        text: a.selected_text,
        color: a.color,
        style: a.highlight_style
      }))
    });

    // Renk gruplarına ayır - en üstteki (son eklenen) annotation'ı bul
    const annotationsByColor = new Map();

    allRelevantAnnotations.forEach(annotation => {
      const colorKey = `${annotation.color}_${annotation.highlight_style || 'background'}`;
      if (!annotationsByColor.has(colorKey)) {
        annotationsByColor.set(colorKey, []);
      }
      annotationsByColor.get(colorKey).push(annotation);
    });

    console.log('[handleSmartClear] Renk grupları:', {
      groups: Array.from(annotationsByColor.entries()).map(([colorKey, anns]) => ({
        colorKey,
        count: anns.length,
        annotations: anns.map((a: any) => ({
          id: a.id,
          start: a.selection_start,
          end: a.selection_end,
          created_at: a.created_at
        }))
      }))
    });

    // Her renk grubu için en son eklenen annotation'ı al (üstte olan)
    const topAnnotations: any[] = [];
    for (const [, colorAnnotations] of annotationsByColor) {
      // En son eklenen (created_at en büyük olan)
      const latestAnnotation = colorAnnotations.reduce((latest: any, current: any) =>
        new Date(current.created_at) > new Date(latest.created_at) ? current : latest
      );
      topAnnotations.push(latestAnnotation);
    }

    console.log('[handleSmartClear] En üstteki annotations:', {
      count: topAnnotations.length,
      annotations: topAnnotations.map(a => ({
        id: a.id,
        start: a.selection_start,
        end: a.selection_end,
        text: a.selected_text,
        color: a.color,
        style: a.highlight_style,
        created_at: a.created_at
      }))
    });

    // Sadece en üstteki annotation'ları işle
    const relevantAnnotations = topAnnotations;

    console.log('[handleSmartClear] Bulunan annotations:', {
      count: relevantAnnotations.length,
      annotations: relevantAnnotations.map(a => ({
        id: a.id,
        start: a.selection_start,
        end: a.selection_end,
        text: a.selected_text
      }))
    });

    let deletedCount = 0;
    let modifiedCount = 0;

    // Önce tam kapsanan annotation'ları ayır
    const fullyCoveredAnnotations = [];
    const partiallyCoveredAnnotations = [];

    for (const annotation of relevantAnnotations) {
      const annotationStart = annotation.selection_start;
      const annotationEnd = annotation.selection_end;
      const selectionStart = selection.start;
      const selectionEnd = selection.end;

      // Tam kapsama kontrolü
      if (selectionStart <= annotationStart && selectionEnd >= annotationEnd) {
        fullyCoveredAnnotations.push(annotation);
      } else {
        // Kısmi kesişme
        partiallyCoveredAnnotations.push(annotation);
      }
    }

    console.log('[handleSmartClear] Kapsama analizi:', {
      fullyCovered: fullyCoveredAnnotations.length,
      partiallyCovered: partiallyCoveredAnnotations.length,
      fullyCoveredList: fullyCoveredAnnotations.map(a => ({
        id: a.id,
        start: a.selection_start,
        end: a.selection_end,
        text: a.selected_text.substring(0, 30) + '...',
        color: a.color
      }))
    });

    // 1. Önce tam kapsanan annotation'ları sil
    for (const annotation of fullyCoveredAnnotations) {
      console.log('[handleSmartClear] Tam kapsama - siliniyor:', {
        id: annotation.id,
        start: annotation.selection_start,
        end: annotation.selection_end,
        color: annotation.color
      });

      const success = await deleteAnnotation(annotation.id);
      if (success) {
        deletedCount++;
      }
    }

    // 2. Sonra kısmi kesişen annotation'ları işle
    for (const annotation of partiallyCoveredAnnotations) {
      const annotationStart = annotation.selection_start;
      const annotationEnd = annotation.selection_end;
      const selectionStart = selection.start;
      const selectionEnd = selection.end;

      console.log('[handleSmartClear] Kısmi kesişme - işleniyor:', {
        annotation: { start: annotationStart, end: annotationEnd, text: annotation.selected_text.substring(0, 30) + '...' },
        selection: { start: selectionStart, end: selectionEnd, text: selection.text.substring(0, 30) + '...' }
      });

      // Kısmi kesişme - annotation'ı böl
      console.log('[handleSmartClear] Kısmi kesişme - bölünüyor');

      // Orijinal annotation'ı sil
      const deleteSuccess = await deleteAnnotation(annotation.id);
      if (!deleteSuccess) {
        console.error('[handleSmartClear] Kısmi silme başarısız:', annotation.id);
        continue;
      }

      // Yeni annotation'lar oluştur
      const newAnnotations = [];

      // Sol kısım (seçimden önce)
      if (annotationStart < selectionStart) {
        const leftEnd = Math.min(selectionStart, annotationEnd);
        const leftText = annotation.selected_text.substring(0, leftEnd - annotationStart);

        if (leftText.trim()) {
          newAnnotations.push({
            book_id: annotation.book_id,
            section_id: annotation.section_id,
            sentence_id: annotation.sentence_id,
            annotation_type: annotation.annotation_type,
            selected_text: leftText.trim(),
            selection_start: annotationStart,
            selection_end: leftEnd,
            prefix_text: annotation.prefix_text,
            suffix_text: annotation.suffix_text,
            word_proximity: annotation.word_proximity,
            text_hash: annotation.text_hash,
            sentence_hash: annotation.sentence_hash,
            color: annotation.color,
            highlight_style: annotation.highlight_style,
            metadata: annotation.metadata,
            user_id: annotation.user_id
          });
        }
      }

      // Sağ kısım (seçimden sonra)
      if (annotationEnd > selectionEnd) {
        const rightStart = Math.max(selectionEnd, annotationStart);
        const rightStartOffset = rightStart - annotationStart;
        const rightText = annotation.selected_text.substring(rightStartOffset);

        if (rightText.trim()) {
          newAnnotations.push({
            book_id: annotation.book_id,
            section_id: annotation.section_id,
            sentence_id: annotation.sentence_id,
            annotation_type: annotation.annotation_type,
            selected_text: rightText.trim(),
            selection_start: rightStart,
            selection_end: annotationEnd,
            prefix_text: annotation.prefix_text,
            suffix_text: annotation.suffix_text,
            word_proximity: annotation.word_proximity,
            text_hash: annotation.text_hash,
            sentence_hash: annotation.sentence_hash,
            color: annotation.color,
            highlight_style: annotation.highlight_style,
            metadata: annotation.metadata,
            user_id: annotation.user_id
          });
        }
      }

      // Yeni annotation'ları ekle
      for (const newAnnotation of newAnnotations) {
        console.log('[handleSmartClear] Yeni annotation ekleniyor:', {
          start: newAnnotation.selection_start,
          end: newAnnotation.selection_end,
          text: newAnnotation.selected_text
        });

        const created = await createAnnotation(newAnnotation);
        if (created) {
          modifiedCount++;
        }
      }

      if (newAnnotations.length === 0) {
        // Hiç kısım kalmadı
        deletedCount++;
      }
    }

    console.log('[handleSmartClear] Sonuç:', {
      deletedCount,
      modifiedCount,
      fullyCoveredDeleted: fullyCoveredAnnotations.length,
      partiallyCoveredProcessed: partiallyCoveredAnnotations.length
    });
    return { deletedCount, modifiedCount };
  }, [annotations, deleteAnnotation, createAnnotation]);

  /**
   * ✅ Note sheet açma handler'ı (Not Al için)
   */
  const handleNoteOpen = useCallback(() => {
    if (!selection?.text || !user) {
      showToast('Not eklemek için metin seçmelisiniz ve giriş yapmalısınız.', 'error');
      return;
    }

    // Selection'ı state'e kaydet
    setNoteSelectionData(selection);
    setIsNoteSheetOpen(true);
  }, [selection, user, showToast]);

  /**
   * Form açma handler'ı (Şerh Ekle için)
   */
  const handleFormOpen = useCallback(() => {
    if (!selection) {
      return;
    }

    // Selection'ı state'e kaydet
    setFormSelectedText(selection.text);
    setFormSelectionData(selection);
    setIsFormSheetOpen(true);
    setIsFormOpen(true);
  }, [selection, setIsFormOpen]);

  /**
   * Bookmark sheet açma handler'ı
   */
  const handleBookmarkOpen = useCallback(() => {
    if (!selection?.text || !user) {
      showToast('Yer imi eklemek için metin seçmelisiniz ve giriş yapmalısınız.', 'error');
      return;
    }

    setIsBookmarkSheetOpen(true);
  }, [selection, user, showToast]);

  /**
   * Bookmark sheet kapama handler'ı
   */
  const handleBookmarkClose = useCallback(() => {
    setIsBookmarkSheetOpen(false);
  }, []);

  /**
   * Koleksiyon seçimi handler'ı
   */
  const handleSelectCollection = useCallback(async (collection: BookmarkCollection) => {
    if (!selection?.text || !user || !bookId || !sectionId) {
      showToast('Yer imi eklemek için gerekli bilgiler eksik.', 'error');
      return;
    }

    try {
      // Annotation verilerini hazırla
      const annotationData = await prepareAnnotationDataWithSelection(bookId, sectionId, selection);
      if (!annotationData) {
        showToast('Annotation verisi hazırlanamadı.', 'error');
        return;
      }

      // Sheet tarafında seçilen arka plan kimliği geçici olarak collection.color içine konuyor
      const maybeIdentifier = (collection as any)?.color;
      const identifierPattern = /^(HIGHLIGHT_COLOR_|AUTO_BG_OVERLAY_|NO_COLOR)/;
      const bgIdentifier = typeof maybeIdentifier === 'string' && identifierPattern.test(maybeIdentifier)
        ? maybeIdentifier
        : undefined;

      // Bookmark annotation'ı oluştur (collection_id yazmadan)
      const bookmarkInput: CreateAnnotationInput = {
        ...annotationData,
        annotation_type: 'bookmark',
        user_id: user.id,
        metadata: {
          ...(annotationData as any).metadata,
          bg_identifier: bgIdentifier ?? null
        }
      };

      const created = await createAnnotation(bookmarkInput);
      if (created && (created as any).id) {
        const annotationId = (created as any).id as string;
        const { error: linkError } = await supabase
          .from('bookmark_annotation_collections')
          .insert({ annotation_id: annotationId, collection_id: collection.id } as any);

        if (linkError) {
          // Rollback: oluşturulan annotation'ı sil
          await supabase.from('text_annotations').delete().eq('id', annotationId);
          showToast('Yer imi oluşturuldu ancak koleksiyon bağlantısı kurulamadı.', 'error');
        } else {
          showToast(`"${collection.name}" koleksiyonuna yer imi eklendi!`, 'success');
          setIsBookmarkSheetOpen(false);
          clearSelection();
          if (onAnnotationCreated) onAnnotationCreated();
        }
      } else {
        showToast('Yer imi eklenirken hata oluştu.', 'error');
      }
    } catch (error) {
      console.error('Bookmark creation error:', error);
      showToast('Beklenmeyen bir hata oluştu.', 'error');
    }
  }, [selection, user, bookId, sectionId, prepareAnnotationDataWithSelection, createAnnotation, showToast, clearSelection, onAnnotationCreated]);

  /**
   * Yeni koleksiyon oluşturma handler'ı
   */
  const handleCreateCollection = useCallback(async (input: Omit<CreateCollectionInput, 'user_id'>) => {
    if (!user) {
      showToast('Koleksiyon oluşturmak için giriş yapmalısınız.', 'error');
      return;
    }

    try {
      const result = await createCollection(input);
      if (result) {
        showToast(`"${result.name}" koleksiyonu oluşturuldu!`, 'success');
      } else {
        showToast('Koleksiyon oluşturulurken hata oluştu.', 'error');
      }
    } catch (error) {
      console.error('Collection creation error:', error);
      showToast('Beklenmeyen bir hata oluştu.', 'error');
    }
  }, [user, createCollection, showToast]);

  /**
   * Vurgulama handler'ı - renk ve stil parametresi ile
   */
  const handleCreateHighlight = useCallback(async (color: string, style: 'background' | 'text') => {
    if (!selection?.text || !user) {
      showToast('Vurgulamak için metin seçmelisiniz ve giriş yapmalısınız.', 'error');
      return;
    }

    if (!bookId || !sectionId) {
      showToast('Kitap ve bölüm bilgisi bulunamadı.', 'error');
      return;
    }

    // Eğer "clear" seçildiyse, mevcut vurgulamaları akıllı şekilde temizle
    if (color === 'clear') {
      try {
        // Basit ve etkili kısmi temizleme
        const result = await handleSmartClear(bookId, sectionId, selection);

        const totalChanges = result.deletedCount + result.modifiedCount;
        if (totalChanges > 0) {
          let message = '';
          if (result.deletedCount > 0 && result.modifiedCount > 0) {
            message = `${result.deletedCount} vurgulama silindi, ${result.modifiedCount} vurgulama bölündü!`;
          } else if (result.deletedCount > 0) {
            message = `${result.deletedCount} vurgulama temizlendi!`;
          } else if (result.modifiedCount > 0) {
            message = `${result.modifiedCount} vurgulamadan seçilen kısım çıkarıldı!`;
          }

          showToast(message, 'success');
          if (onAnnotationCreated) {
            onAnnotationCreated(); // Annotation listesini yenile
          }
        } else {
          showToast('Bu metinde temizlenecek vurgulama bulunamadı.', 'success');
        }

        clearSelection();
        return;
      } catch (error) {
        console.error('Highlight clearing error:', error);
        showToast('Vurgulama temizlenirken hata oluştu.', 'error');
        return;
      }
    }

    try {
      // Annotation verilerini hazırla
      const annotationData = await prepareAnnotationDataWithSelection(bookId, sectionId, selection);
      if (!annotationData) {
        showToast('Annotation verisi hazırlanamadı.', 'error');
        return;
      }

      // Highlight annotation'ı oluştur
      const highlightInput: CreateAnnotationInput = {
        ...annotationData,
        annotation_type: 'highlight',
        color: color,
        highlight_style: style, // Artık veritabanında sütun var!
        user_id: user.id
      };

      const result = await createAnnotation(highlightInput);

      if (result) {
        showToast('Vurgulama başarıyla eklendi!', 'success');
        clearSelection(); // Seçimi temizle
        if (onAnnotationCreated) {
          onAnnotationCreated(); // Annotation oluşturuldu callback'i
        }
      } else {
        showToast('Vurgulama eklenirken hata oluştu.', 'error');
      }
    } catch (error) {
      console.error('Highlight creation error:', error);
      showToast('Beklenmeyen bir hata oluştu.', 'error');
    }
  }, [selection, user, bookId, sectionId, prepareAnnotationDataWithSelection, createAnnotation, smartDeleteAnnotationsBySelection, showToast, clearSelection, onAnnotationCreated]);

  /**
   * 🗑️ Annotation temizleme handler'ı - YENİ BASİT SİSTEM
   */
  const handleClearAnnotations = useCallback(async () => {
    if (!selection?.text || !user) {
      showToast('Temizlemek için metin seçmelisiniz ve giriş yapmalısınız.', 'error');
      return;
    }

    try {
      // Yeni basit silme sistemi - seçilen alandaki TÜM annotation'ları sil
      const success = await clearAnnotations(deleteAnnotation);

      if (success) {
        showToast('Seçilen alandaki tüm annotation\'lar temizlendi!', 'success');
        if (onAnnotationCreated) {
          onAnnotationCreated(); // Annotation listesini yenile
        }
      } else {
        showToast('Bu alanda temizlenecek annotation bulunamadı.', 'success');
      }
    } catch (error) {
      console.error('Annotation clearing error:', error);
      showToast('Annotation temizlenirken hata oluştu.', 'error');
    }
  }, [selection, user, clearAnnotations, deleteAnnotation, showToast, onAnnotationCreated]);

  /**
   * Form kapama handler'ı
   */
  const handleFormClose = useCallback(() => {
    setFormSelectedText('');
    setFormSelectionData(null);
    setIsFormSheetOpen(false);
    setIsFormOpen(false);
  }, [setIsFormOpen]);

  /**
   * ✅ Note save handler'ı (Artık AnnotationSheet için)
   */
  const handleNoteSave = useCallback(async (input: CreateAnnotationInput) => {
    const currentSelection = noteSelectionData;

    if (!bookId || !sectionId || !currentSelection || !user) {
      if (!user) {
        showToast('Not eklemek için giriş yapmanız gerekiyor.', 'error');
      }
      return;
    }

    try {
      const annotationData = await prepareAnnotationDataWithSelection(bookId, sectionId, currentSelection);
      if (!annotationData) return;

      const completeInput: CreateAnnotationInput = {
        ...annotationData,
        ...input,
        user_id: user.id,
        annotation_type: 'note',
      };

      const result = await createAnnotation(completeInput);

      if (result) {
        showToast('Not başarıyla kaydedildi!');
        setIsNoteSheetOpen(false);
        clearSelection();
        if (onAnnotationCreated) {
          onAnnotationCreated();
        }
      } else {
        showToast('Not kaydedilemedi. Lütfen tekrar deneyin.', 'error');
      }
    } catch (error) {
      showToast('Not kaydedilirken bir hata oluştu.', 'error');
      throw error;
    }
  }, [noteSelectionData, bookId, sectionId, user, createAnnotation, showToast, clearSelection, onAnnotationCreated, prepareAnnotationDataWithSelection]);

  /**
   * Bottom sheet submit handler'ı (Şerh Ekle için)
   */
  const handleFormSubmit = useCallback(async (input: CreateAnnotationInput) => {
    const currentSelection = formSelectionData;

    if (!bookId || !sectionId || !currentSelection || !user) {
      if (!user) {
        showToast('Şerh eklemek için giriş yapmanız gerekiyor.', 'error');
      }
      return;
    }

    try {
      // Annotation data'sını hazırla - saved selection'ı kullan
      const annotationData = await prepareAnnotationDataWithSelection(bookId, sectionId, currentSelection);

      if (!annotationData) {
        return;
      }

      // Input ile birleştir ve user_id ekle
      const completeInput: CreateAnnotationInput = {
        ...annotationData,
        ...input,
        user_id: user.id, // User ID'yi ekle
        annotation_type: 'sherh' // Şerh için sabit
      };

      // Annotation oluştur
      const result = await createAnnotation(completeInput);

      if (result) {
        showToast(`${input.annotation_type === 'note' ? 'Şerh' : input.annotation_type === 'highlight' ? 'Vurgulama' : 'Yer imi'} başarıyla kaydedildi!`);
        handleFormClose();
        clearSelection();
        // Annotation oluşturuldu callback'ini çağır - gerçek zamanlı güncelleme için
        if (onAnnotationCreated) {
          onAnnotationCreated();
        }
      } else {
        showToast('Şerh kaydedilemedi. Lütfen tekrar deneyin.', 'error');
      }
    } catch (error) {
      showToast('Şerh kaydedilirken bir hata oluştu.', 'error');
      throw error;
    }
  }, [bookId, sectionId, prepareAnnotationDataWithSelection, createAnnotation, handleFormClose, clearSelection, showToast, formSelectionData, user]);

  /**
   * Kopyalama işlemi - hem menü hem sheet tarafından kullanılır
   */
  const handleCopyCurrentSelection = useCallback(() => {
    const text = selection?.text?.trim();
    if (!text) {
      showToast('Kopyalamak için metin seçmelisiniz.', 'error');
      return;
    }

    // İnsan-dostu kitap ve bölüm isimleri
    const humanBook = bookTitleMap[bookId || ''] || (bookId === 'quran' ? "Kur'an-ı Kerim" : (bookId || 'Bilinmeyen Kitap'));
    let humanSection = sectionId || 'Bölüm';
    if (bookId === 'quran') {
      humanSection = surahMap[sectionId || ''] || `Sure ${sectionId}`;
    } else if (sectionId) {
      humanSection = sectionTitleMap[sectionId] || sectionId;
    }
    const source = `(${humanBook} • ${humanSection})`;
    const composed = `${text}\n\n${source}`;

    const copyWithFallback = (value: string) => {
      try {
        const ta = document.createElement('textarea');
        ta.value = value;
        ta.style.position = 'fixed';
        ta.style.left = '-9999px';
        document.body.appendChild(ta);
        ta.select();
        document.execCommand('copy');
        document.body.removeChild(ta);
        showToast('Metin panoya kopyalandı.');
      } catch (err) {
        showToast('Kopyalama başarısız. Lütfen tekrar deneyin.', 'error');
      }
    };

    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(composed)
        .then(() => showToast('Metin panoya kopyalandı.'))
        .catch(() => copyWithFallback(composed));
    } else {
      copyWithFallback(composed);
    }
  }, [selection?.text, bookId, sectionId, bookTitleMap, surahMap, sectionTitleMap, showToast]);

  /**
   * Menu pozisyonunu hesaplar
   */
  const getMenuPosition = useCallback(() => {
    if (!selectionRect) return null;

    // Viewport boyutlarını al
    const viewportHeight = window.innerHeight;
    const menuHeight = 45; // Daha doğru menu yüksekliği
    const padding = 2; // Çok yakın - daha da azaltıldı

    // Seçimin üstünde yer var mı kontrol et
    const spaceAbove = selectionRect.top;
    const spaceBelow = viewportHeight - selectionRect.bottom;

    let top: number;
    let placement: 'above' | 'below';

    if (spaceAbove >= menuHeight + padding) {
      // Üstte yer var, üstte göster - çok yakın
      top = selectionRect.top - menuHeight - padding;
      placement = 'above';
    } else if (spaceBelow >= menuHeight + padding) {
      // Altta yer var, altta göster - çok yakın
      top = selectionRect.bottom + padding;
      placement = 'below';
    } else {
      // Her iki tarafta da yer yok, daha fazla yer olan tarafta göster
      if (spaceAbove > spaceBelow) {
        top = Math.max(3, selectionRect.top - menuHeight - 1); // Minimum 3px üstten, çok yakın
        placement = 'above';
      } else {
        top = selectionRect.bottom + 1; // Neredeyse yapışık
        placement = 'below';
      }
    }

    return {
      top: top + window.scrollY, // Scroll offset'i ekle
      left: selectionRect.left + (selectionRect.width / 2) + window.scrollX,
      placement
    };
  }, [selectionRect]);

  // Seçim mevcutken, başka bir sheet açık değilken aksiyon UI'ını göster
  const showActionUI = isSelecting && !!selection && !isFormSheetOpen && !isNoteSheetOpen && !isSearchSheetOpen && !isBookmarkSheetOpen && !isTagsSheetOpen && !selectedTag;

  // Floating annotation panel'i kapatmak için global event yayınla (mobil seçim paneli açılınca)
  useEffect(() => {
    if (!isMobile) return;
    const eventName = showActionUI ? 'ikra:selection-sheet-open' : 'ikra:selection-sheet-close';
    window.dispatchEvent(new CustomEvent(eventName));
  }, [isMobile, showActionUI]);

  return (
    <div className={className}>
      {children}

      {/* Text Action UI */}
      {!isMobile && (
        <TextActionMenu
          isVisible={showActionUI}
          position={getMenuPosition()}
          selectedText={selection?.text || ''}
          selectionKey={selection ? `${selection.start}-${selection.end}-${selection.text.length}` : undefined}
          onActionSelect={(action) => {
            if (action === 'copy') {
              handleCopyCurrentSelection();
            } else if (action === 'share') {
              // TODO: share
            } else if (action === 'comment') {
              // TODO: comment
            }
          }}
          onCreateNote={handleNoteOpen}
          onCreateAnnotation={handleFormOpen}
          onCreateHighlight={handleCreateHighlight}
          onCreateBookmark={handleBookmarkOpen}
          onFindAnnotations={handleFindAnnotations}
          onClearAnnotations={handleClearAnnotations}
          onShowTags={handleShowTags}
        />
      )}

      {/* Mobile bottom sheet - sadece seçim varken */}
      {isMobile && (
        <TextActionPanel
          isOpen={showActionUI}
          onClose={clearSelection}
          selectedText={selection?.text || ''}
          selectionKey={selection ? `${selection.start}-${selection.end}-${selection.text.length}` : undefined}
          onCopy={handleCopyCurrentSelection}
          onShare={() => { /* TODO: share */ }}
          onCreateNote={handleNoteOpen}
          onCreateAnnotation={handleFormOpen}
          onCreateHighlight={handleCreateHighlight}
          onCreateBookmark={handleBookmarkOpen}
          onFindAnnotations={handleFindAnnotations}
          onClearAnnotations={handleClearAnnotations}
          onShowTags={handleShowTags}
        />
      )}

      {/* ✅ Note Sheet (Artık AnnotationSheet kullanıyor) - Not Al için */}
      <AnnotationSheet
        isOpen={isNoteSheetOpen}
        onClose={() => setIsNoteSheetOpen(false)}
        selectedText={noteSelectionData?.text || ''}
        annotationType="note"
        allowPublicToggle={bookId === 'quran'}
        onSubmit={handleNoteSave}
        isLoading={annotationLoading}
      />

      {/* Annotation Sheet - Şerh Ekle için */}
      <AnnotationSheet
        isOpen={isFormSheetOpen}
        selectedText={formSelectedText}
        annotationType="sherh"
        onClose={handleFormClose}
        allowPublicToggle={true}
        onSubmit={handleFormSubmit}
        isLoading={annotationLoading}
      />

      {/* Annotation Search Sheet */}
      <AnnotationSearchSheet
        isOpen={isSearchSheetOpen}
        onClose={() => setIsSearchSheetOpen(false)}
        selectedText={selection?.text || ''}
        sentenceIds={Array.isArray(selection?.sentence_id) ? selection.sentence_id : (selection?.sentence_id ? [selection.sentence_id] : [])}
        bookId={bookId}
        sectionId={sectionId}
      />

      {/* Bookmark Bottom Sheet */}
      <BookmarkBottomSheet
        isOpen={isBookmarkSheetOpen}
        onClose={handleBookmarkClose}
        selectedText={selection?.text || ''}
        collections={collections}
        onSelectCollection={handleSelectCollection}
        onCreateCollection={handleCreateCollection}
        loading={collectionLoading}
      />

      {/* Sentence Tags Sheet */}
      <SentenceTagsSheet
        isOpen={isTagsSheetOpen}
        onClose={() => setIsTagsSheetOpen(false)}
        sentenceId={
          selection?.sentence_id
            ? (Array.isArray(selection.sentence_id) ? selection.sentence_id[0] : selection.sentence_id)
            : null
        }
        sentenceTags={selectedSentenceTags}
        selectedText={selection?.text}
        onTagClick={handleTagClick}
      />

      {/* Tag Detail Page */}
      {selectedTag && (
        <div
          className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
          onClick={handleTagDetailBack}
        >
          <div
            className="w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-2xl shadow-2xl"
            onClick={(e) => e.stopPropagation()}
            style={{ backgroundColor: 'var(--bg-color)' }}
          >
            <TagDetailPage
              tagId={selectedTag.id}
              onBack={handleTagDetailBack}
              onSentenceClick={(sentence) => {
                // Sentence'a tıklandığında yapılacak işlem
                console.log('Sentence clicked:', sentence);
                // Burada sentence'a scroll yapılabilir veya başka bir işlem yapılabilir
              }}
            />
          </div>
        </div>
      )}

      {/* Toast Notification */}
      <AnnotationToast
        isVisible={toastState.isVisible}
        message={toastState.message}
        type={toastState.type}
        onClose={hideToast}
      />


    </div>
  );
}
