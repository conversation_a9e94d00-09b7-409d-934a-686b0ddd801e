import React, { useState, useEffect, useRef } from 'react';
import { Copy, Share2, Search, Tag } from 'lucide-react';
import MessageIcon from '@shared/components/Icons/SherhIcon';
import BookmarkIcon from '@shared/components/Icons/BookmarkIcon';
import FileTextIcon from '@shared/components/Icons/NoteIcon';
import HighlightIcon from '@shared/components/Icons/HighlightIcon';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import ColorPicker from '@shared/color/components/ColorPicker';
import { useReaderInteractionStyles } from '../../shared/hooks/useReaderInteractionStyles';
import { useColorPicker } from '../hooks/useColorPicker';

interface TextActionMenuProps {
  isVisible: boolean;
  position: { top: number; left: number; placement?: 'above' | 'below' } | null;
  menuRef?: React.RefObject<HTMLDivElement>;
  selectedText: string;
  onActionSelect: (actionType: 'copy' | 'share' | 'comment') => void;
  onCopy?: () => void; // Optional override for copy behavior
  // Annotation handlers - form açmak için
  onCreateNote?: () => void; // ✅ Not Al handler
  onCreateAnnotation?: () => void; // Şerh Ekle handler (mevcut)
  onCreateHighlight?: (color: string, style: 'background' | 'text') => void; // Renk ve stil parametresi eklendi
  onCreateBookmark?: () => void;
  onFindAnnotations?: () => void; // Şerh bul handler
  onClearAnnotations?: () => void; // Temizle handler
  onShowTags?: () => void; // Etiketleri göster handler
  // Seçim değiştiğinde color picker'ı kapatmak için key
  selectionKey?: string;
}

// ActionButton bileşeni
const ActionButton: React.FC<React.PropsWithChildren<{
  label: string;
  onClick?: () => void;
  hoverBgColor: string;
  showText?: boolean; // Yeni prop: yazı gösterilsin mi?
}>> = ({ children, label, onClick, hoverBgColor, showText = false }) => {
  return (
    <button
      title={label}
      className={`px-2 py-1.5 rounded text-[var(--text-color)]/80 hover:text-[var(--text-color)] transition-colors duration-150 flex items-center ${
        showText ? 'gap-1.5' : 'justify-center'
      } min-w-0 flex-shrink-0`} // ✅ min-w-0 ve flex-shrink-0 eklendi
      onClick={onClick}
      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = hoverBgColor}
      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
    >
      {children}
      {showText && <span className="text-xs whitespace-nowrap font-medium">{label}</span>}
    </button>
  );
};

export const TextActionMenu: React.FC<TextActionMenuProps> = ({
  isVisible,
  position,
  menuRef,
  selectedText,
  onActionSelect,
  onCopy,
  onCreateNote,
  onCreateAnnotation,
  onCreateHighlight,
  onCreateBookmark,
  onFindAnnotations,
  onClearAnnotations,
  onShowTags,
  selectionKey
}) => {
  // Centralized color picker state/handlers
  const {
    showColorPicker,
    selectedColor,
    selectedStyle,
    toggleColorPicker,
    handleColorSelect,
  } = useColorPicker({
    onSelect: onCreateHighlight,
    onClear: onClearAnnotations,
    selectionKey,
    defaultColor: '#fbbf24',
    defaultStyle: 'background'
  });
  const [colorPickerPlacement, setColorPickerPlacement] = useState<'above' | 'below'>('above');
  const menuContainerRef = useRef<HTMLDivElement | null>(null);
  
  // ✅ Mobil cihaz tespiti
  const [isMobile, setIsMobile] = useState(false);

  // Styles hook
  const { bgColors, getMenuStyles } = useReaderInteractionStyles();

  // ✅ Ekran genişliğine göre yazı gösterme mantığı
  const getAdaptiveTextDisplay = (screenWidth: number) => {
    if (screenWidth >= 400) return 'all'; // Büyük mobil - tüm yazılar
    if (screenWidth >= 350) return 'priority'; // Orta mobil - öncelikli yazılar
    return 'minimal'; // Küçük mobil - minimal yazı
  };

  const [screenWidth, setScreenWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 1024
  );
  const textDisplay = getAdaptiveTextDisplay(screenWidth);

  // ✅ Optimize edilmiş resize handler - tek useEffect
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setScreenWidth(width);
      setIsMobile(width < 640);
    };
    
    // Initial call
    handleResize();
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // ColorPicker için ekranda boşluğa göre konum flip et
  useEffect(() => {
    if (!showColorPicker) return;
    const container = menuContainerRef.current;
    if (!container) return;
    const rect = container.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const margin = 8;
    const estimatedPickerHeight = 180; // yaklaşık yükseklik
    const spaceAbove = rect.top;
    const spaceBelow = viewportHeight - rect.bottom;
    if (spaceAbove >= estimatedPickerHeight + margin) {
      setColorPickerPlacement('above');
    } else if (spaceBelow >= estimatedPickerHeight + margin) {
      setColorPickerPlacement('below');
    } else {
      setColorPickerPlacement(spaceAbove > spaceBelow ? 'above' : 'below');
    }
  }, [showColorPicker]);

  if (!isVisible || !position) {
    return null;
  }

  // Handle default copy function
  const handleCopy = () => {
    if (onCopy) {
      onCopy();
    } else {
      navigator.clipboard.writeText(selectedText || window.getSelection()?.toString() || '');
      onActionSelect('copy');
    }
  };

  // Handle highlight button click
  const handleHighlightClick = () => {
    toggleColorPicker();
  };

  // Get menu styles using hook with mobile parameter
  const menuStyle = getMenuStyles(position, isVisible, isMobile);

  // Mobil için öncelikli butonlar - ✅ Şerh Bul alt satıra taşındı
  const priorityActions = [
    { 
      key: 'note', 
      icon: FileTextIcon as any, 
      label: 'Not Al', 
      handler: onCreateNote, 
      showText: textDisplay === 'all' || textDisplay === 'priority'
    },
    { 
      key: 'annotation', 
      icon: MessageIcon as any, 
      label: 'Şerh Ekle', 
      handler: onCreateAnnotation, 
      showText: textDisplay === 'all' || textDisplay === 'priority'
    },
    { 
      key: 'copy', 
      icon: Copy, 
      label: 'Kopyala', 
      handler: handleCopy, 
      showText: false // Kopyala her zaman sadece icon
    }
  ];

  // Mobil için ikincil butonlar - ✅ Şerh Bul buraya eklendi
  const secondaryActions = [
    {
      key: 'search',
      icon: Search,
      label: 'Şerh Bul',
      handler: onFindAnnotations,
      showText: textDisplay === 'all' || textDisplay === 'priority' // ✅ Alt satırda da yazılı
    },
    {
      key: 'tags',
      icon: Tag,
      label: 'Etiketler',
      handler: onShowTags,
      showText: false
    },
    { key: 'highlight', icon: HighlightIcon as any, label: 'Vurgula', handler: handleHighlightClick, showText: false },
    { key: 'bookmark', icon: BookmarkIcon as any, label: 'Yer İmi', handler: onCreateBookmark, showText: false },
    { key: 'share', icon: Share2, label: 'Paylaş', handler: () => onActionSelect('share'), showText: false }
  ];

  return (
    <>
      <div
        ref={(el) => {
          menuContainerRef.current = el;
          if (menuRef && 'current' in menuRef) {
            (menuRef as React.MutableRefObject<HTMLDivElement | null>).current = el;
          }
        }}
        className={`${
          isMobile 
            ? 'flex flex-col w-auto min-w-0 max-w-none' // Mobilde overflow kontrolü
            : 'flex items-center justify-between max-w-none' // Desktop'ta overflow kontrolü
        } p-1 rounded-lg shadow-lg border relative overflow-visible`} // ✅ overflow-visible yapıldı
        style={menuStyle}
      >
        
        {/* 🏠 DESKTOP LAYOUT */}
        {!isMobile && (
          <>
            {/* Sol taraf - Annotation butonları */}
            <div className="flex items-center">
              {/* ✅ Not Al */}
              {onCreateNote && (
                <ActionButton label="Not Al" onClick={onCreateNote} hoverBgColor={bgColors.hover} showText={true}>
                  <FileTextIcon size={16} />
                </ActionButton>
              )}

              {/* Şerh Ekle */}
              {onCreateAnnotation && (
                <ActionButton label="Şerh Ekle" onClick={onCreateAnnotation} hoverBgColor={bgColors.hover} showText={true}>
                  <MessageIcon size={16} />
                </ActionButton>
              )}

              {/* Şerh Bul - 3. sıraya taşındı */}
              {onFindAnnotations && (
                <ActionButton label="Şerh Bul" onClick={onFindAnnotations} hoverBgColor={bgColors.hover} showText={true}>
                  <Search size={16} />
                </ActionButton>
              )}

              {/* Etiketler */}
              {onShowTags && (
                <ActionButton label="Etiketler" onClick={onShowTags} hoverBgColor={bgColors.hover} showText={true}>
                  <Tag size={16} />
                </ActionButton>
              )}

              {/* Vurgula */}
              {onCreateHighlight && (
                <ActionButton label="Vurgula" onClick={handleHighlightClick} hoverBgColor={bgColors.hover}>
                  <HighlightIcon size={18} />
                </ActionButton>
              )}

              {/* Yer İmi */}
              {onCreateBookmark && (
                <ActionButton label="Yer İmi" onClick={onCreateBookmark} hoverBgColor={bgColors.hover}>
                  <BookmarkIcon size={16} />
                </ActionButton>
              )}
            </div>

            {/* Sağ taraf - Kopyala ve Paylaş */}
            <div className="flex items-center">
              {/* Kopyala */}
              <ActionButton label="Kopyala" onClick={handleCopy} hoverBgColor={bgColors.hover}>
                <Copy size={16} />
              </ActionButton>

              {/* Paylaş */}
              <ActionButton label="Paylaş" onClick={() => onActionSelect('share')} hoverBgColor={bgColors.hover}>
                <Share2 size={16} />
              </ActionButton>
            </div>
          </>
        )}

        {/* 📱 MOBILE LAYOUT - Adaptive Text System */}
        {isMobile && (
          <>
            {/* İlk satır - Adaptive yazılar ile */}
            <div className="flex items-center justify-center gap-0.5 mb-1 min-w-0">
              {priorityActions.map(action => 
                action.handler && (
                  <ActionButton 
                    key={action.key}
                    label={action.label} 
                    onClick={action.handler} 
                    hoverBgColor={bgColors.hover}
                    showText={action.showText}
                  >
                    <action.icon size={14} />
                  </ActionButton>
                )
              )}
            </div>

            {/* İkinci satır - Şerh Bul yazılı, diğerleri icon */}
            <div className="flex items-center justify-center gap-0.5 min-w-0">
              {secondaryActions.map(action => 
                action.handler && (
                  <ActionButton 
                    key={action.key}
                    label={action.label} 
                    onClick={action.handler} 
                    hoverBgColor={bgColors.hover}
                    showText={action.showText || false} // ✅ Şerh Bul yazılı, diğerleri icon
                  >
                    <action.icon size={14} />
                  </ActionButton>
                )
              )}
            </div>
          </>
        )}

        {/* Renk Seçici - Akıllı Pozisyonlama */}
        {showColorPicker && onCreateHighlight && position && (
          <div
            className="absolute z-[9999]"
            style={{
              // Akıllı pozisyonlama: üstte yer yoksa aşağıda aç
              ...(colorPickerPlacement === 'above'
                ? {
                    bottom: '100%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    marginBottom: '4px',
                  }
                : {
                    top: '100%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    marginTop: '4px',
                  }),
            }}
          >
            <ColorPicker
              selectedColor={selectedColor}
              selectedStyle={selectedStyle}
              onColorSelect={handleColorSelect}
            />
          </div>
        )}
      </div>
    </>
  );
};
