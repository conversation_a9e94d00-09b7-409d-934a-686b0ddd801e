import React, { useEffect } from 'react';
import { Copy, Share2, Search, Tag } from 'lucide-react';
import MessageIcon from '@shared/components/Icons/SherhIcon';
import BookmarkIcon from '@shared/components/Icons/BookmarkIcon';
import NoteIcon from '@shared/components/Icons/NoteIcon';
import HighlightIcon from '@shared/components/Icons/HighlightIcon';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import ColorPicker from '@shared/color/components/ColorPicker';
import { useColorPicker } from '../hooks/useColorPicker';

interface TextActionPanelProps {
  isOpen: boolean;
  onClose?: () => void; // optional; not used since pane is persistent
  selectedText?: string;
  onCopy?: () => void;
  onShare?: () => void;
  onCreateNote?: () => void;
  onCreateAnnotation?: () => void;
  onCreateHighlight?: (color: string, style: 'background' | 'text') => void;
  onCreateBookmark?: () => void;
  onFindAnnotations?: () => void;
  onClearAnnotations?: () => void;
  onShowTags?: () => void;
  selectionKey?: string;
}

export const TextActionPanel: React.FC<TextActionPanelProps> = ({
  isOpen,
  // onClose,
  // selectedText,
  onCopy,
  onShare,
  onCreateNote,
  onCreateAnnotation,
  onCreateHighlight,
  onCreateBookmark,
  onFindAnnotations,
  onClearAnnotations,
  onShowTags,
  selectionKey
}) => {
  const borderColor = useAutoOverlay(21, 'var(--bg-color)');
  const pillBg = useAutoOverlay(8, 'var(--bg-color)');
  const pillBorder = useAutoOverlay(15, 'var(--bg-color)');

  const {
    showColorPicker,
    selectedColor,
    selectedStyle,
    toggleColorPicker,
    handleColorSelect,
  } = useColorPicker({
    onSelect: onCreateHighlight,
    onClear: onClearAnnotations,
    selectionKey,
    defaultColor: '#fbbf24',
    defaultStyle: 'background'
  });

  useEffect(() => {
    // keep effect for potential external side-effects; closing handled in hook
  }, [selectionKey]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end pointer-events-none">
      <div className="relative w-full pointer-events-auto">
        <div className="w-full h-[10px]" aria-hidden="true" />
        <div
          className="mx-2 mb-2 rounded-2xl shadow-lg border overflow-hidden"
          style={{
            backgroundColor: 'var(--bg-color)',
            borderColor: borderColor
          }}
        >
          <div className="w-full h-[1px] opacity-40" style={{ backgroundColor: borderColor }} />

          <div className="px-3 py-3">
            <div className="grid grid-cols-4 gap-2 mb-2">
              {onCreateNote && (
                <button
                  onClick={onCreateNote}
                  className="flex items-center justify-center gap-1 px-2.5 py-2 rounded-xl text-xs font-medium active:scale-[0.98] border"
                  style={{ color: 'var(--text-color)', borderColor: pillBorder, backgroundColor: pillBg }}
                >
                  <NoteIcon size={14} />
                  <span>Not</span>
                </button>
              )}
              {onFindAnnotations && (
                <button
                  onClick={onFindAnnotations}
                  className="flex items-center justify-center gap-1 px-2.5 py-2 rounded-xl text-xs font-medium active:scale-[0.98] border"
                  style={{ color: 'var(--text-color)', borderColor: pillBorder, backgroundColor: pillBg }}
                >
                  <Search size={14} />
                  <span>Şerh Bul</span>
                </button>
              )}
              {onCreateAnnotation && (
                <button
                  onClick={onCreateAnnotation}
                  className="flex items-center justify-center gap-1 px-2.5 py-2 rounded-xl text-xs font-medium active:scale-[0.98] border"
                  style={{ color: 'var(--text-color)', borderColor: pillBorder, backgroundColor: pillBg }}
                >
                  <MessageIcon size={14} />
                  <span>Şerh</span>
                </button>
              )}
              {onCreateHighlight && (
                <button
                  onClick={toggleColorPicker}
                  className="flex items-center justify-center gap-1 px-2.5 py-2 rounded-xl text-xs font-medium active:scale-[0.98] border"
                  style={{ color: 'var(--text-color)', borderColor: pillBorder, backgroundColor: pillBg }}
                >
                  <HighlightIcon size={14} />
                  <span>Vurgu</span>
                </button>
              )}
            </div>

            <div className="grid grid-cols-4 gap-2 mt-2">
              {onShowTags && (
                <button
                  onClick={onShowTags}
                  className="flex items-center justify-center gap-1 px-2.5 py-2 rounded-xl text-xs font-medium active:scale-[0.98] border"
                  style={{ color: 'var(--text-color)', borderColor: pillBorder, backgroundColor: pillBg }}
                >
                  <Tag size={14} />
                  <span>Etiket</span>
                </button>
              )}
              {onCreateBookmark && (
                <button
                  onClick={onCreateBookmark}
                  className="flex items-center justify-center gap-1 px-2.5 py-2 rounded-xl text-xs font-medium active:scale-[0.98] border"
                  style={{ color: 'var(--text-color)', borderColor: pillBorder, backgroundColor: pillBg }}
                >
                  <BookmarkIcon size={14} color={'var(--text-color)'} />
                  <span>Kayıt</span>
                </button>
              )}
              {onCopy && (
                <button
                  onClick={onCopy}
                  className="flex items-center justify-center gap-1 px-2.5 py-2 rounded-xl text-xs font-medium active:scale-[0.98] border"
                  style={{ color: 'var(--text-color)', borderColor: pillBorder, backgroundColor: pillBg }}
                >
                  <Copy size={14} />
                  <span>Kopyala</span>
                </button>
              )}
              {onShare && (
                <button
                  onClick={onShare}
                  className="flex items-center justify-center gap-1 px-2.5 py-2 rounded-xl text-xs font-medium active:scale-[0.98] border"
                  style={{ color: 'var(--text-color)', borderColor: pillBorder, backgroundColor: pillBg }}
                >
                  <Share2 size={14} />
                  <span>Paylaş</span>
                </button>
              )}
            </div>

            {showColorPicker && onCreateHighlight && (
              <div className="mt-2 flex items-center justify-center">
                <ColorPicker
                  selectedColor={selectedColor}
                  selectedStyle={selectedStyle}
                  onColorSelect={handleColorSelect}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextActionPanel;
