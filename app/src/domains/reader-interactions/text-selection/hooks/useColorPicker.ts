import { useCallback, useEffect, useState } from 'react';

export type HighlightStyle = 'background' | 'text';

interface UseColorPickerOptions {
  onSelect?: (color: string, style: HighlightStyle) => void;
  onClear?: () => void;
  selectionKey?: string;
  defaultColor?: string;
  defaultStyle?: HighlightStyle;
}

export function useColorPicker({
  onSelect,
  onClear,
  selectionKey,
  defaultColor = '#fbbf24',
  defaultStyle = 'background',
}: UseColorPickerOptions) {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedColor, setSelectedColor] = useState<string>(defaultColor);
  const [selectedStyle, setSelectedStyle] = useState<HighlightStyle>(defaultStyle);

  // Close the palette when selection changes
  useEffect(() => {
    setShowColorPicker(false);
  }, [selectionKey]);

  const toggleColorPicker = useCallback(() => {
    setShowColorPicker((s) => !s);
  }, []);

  const handleColorSelect = useCallback((color: string, style: HighlightStyle) => {
    // Standardize delete sentinel handling
    if (color === 'DELETE' || color === 'clear') {
      setShowColorPicker(false);
      onClear?.();
      return;
    }
    setSelectedColor(color);
    setSelectedStyle(style);
    setShowColorPicker(false);
    onSelect?.(color, style);
  }, [onClear, onSelect]);

  return {
    // state
    showColorPicker,
    selectedColor,
    selectedStyle,
    // actions
    setShowColorPicker,
    setSelectedColor,
    setSelectedStyle,
    toggleColorPicker,
    handleColorSelect,
  } as const;
}
