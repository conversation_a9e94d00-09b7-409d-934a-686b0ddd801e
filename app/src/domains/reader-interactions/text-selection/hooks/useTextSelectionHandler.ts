import { useState, useCallback, useEffect, useRef } from 'react';
// ✅ YENİ ORGANİZE YAPISI - Shared types
import type { TextSelection } from '../../shared/types';
import {
  validateTextSelection,
  adjustSelectionForHtml,
  extractPrefix,
  extractSuffix,
  extractWordProximity,
  createTextHash,
  createSentenceHash
} from '../utils/textUtils';

/**
 * 🛡️ Sentence ID validation - geçersiz ID'leri filtreler
 */
function isValidSentenceId(sentenceId: string): boolean {
  if (!sentenceId || typeof sentenceId !== 'string') {
    return false;
  }

  // Geçersiz pattern'leri kontrol et
  const invalidPatterns = [
    /^invalid-sentence-/,  // "invalid-sentence-" ile başlayanlar
    /^temp-/,              // "temp-" ile başlayanlar
    /^placeholder-/,       // "placeholder-" ile başlayanlar
    /^error-/,             // "error-" ile başlayanlar
  ];

  // <PERSON><PERSON><PERSON> bir geçersiz pattern eşleşirse false döndür
  for (const pattern of invalidPatterns) {
    if (pattern.test(sentenceId)) {
      return false;
    }
  }

  // Minimum uzunluk kontrolü
  if (sentenceId.length < 3) {
    return false;
  }

  // Maksimum uzunluk kontrolü (çok uzun ID'ler de şüpheli)
  if (sentenceId.length > 50) {
    return false;
  }

  // Geçerli format kontrolü - örnek: "9_1_13" formatı
  const validFormatPattern = /^\d+_\d+_\d+$/;
  if (validFormatPattern.test(sentenceId)) {
    return true;
  }

  // Diğer geçerli formatlar için ek kontroller eklenebilir
  // Şimdilik sadece sayı_sayı_sayı formatını kabul ediyoruz
  return false;
}

/**
 * 🔍 Element'in divider olup olmadığını kontrol eder
 */
function isDividerElement(element: Element): boolean {
  // data-role="divider" kontrolü
  if (element.getAttribute('data-role') === 'divider') {
    return true;
  }

  // Class name kontrolü
  if (element.classList.contains('risale-divider') ||
      element.classList.contains('divider') ||
      element.classList.contains('annotation-divider')) {
    return true;
  }

  // Text content kontrolü (• • • pattern)
  const textContent = element.textContent?.trim();
  if (textContent && /^[•\s]+$/.test(textContent)) {
    return true;
  }

  return false;
}

/**
 * useTextSelectionHandler Hook
 * Metin seçimi yönetimi ve annotation oluşturma için gerekli verileri sağlar
 */
export function useTextSelectionHandler() {
  const [selection, setSelection] = useState<TextSelection | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionRect, setSelectionRect] = useState<DOMRect | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  // Use browser-safe timeout type (works both in DOM and Node typings)
  const selectionTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  /**
   * Aktif seçimi temizler
   */
  const clearSelection = useCallback(() => {
    console.log('[useTextSelection] Clearing selection');
    setSelection(null);
    setSelectionRect(null);
    setIsSelecting(false);

    // Browser selection'ı da temizle
    if (window.getSelection) {
      window.getSelection()?.removeAllRanges();
    }
  }, []);

  /**
   * Seçim event'lerini handle eder
   */
  const handleSelectionChange = useCallback(() => {
    // Debounce selection changes
    if (selectionTimeoutRef.current) {
      clearTimeout(selectionTimeoutRef.current);
    }

    selectionTimeoutRef.current = setTimeout(() => {
      const browserSelection = window.getSelection();

      // Eğer form açıksa veya sheet açıksa, yeni selection'ları ignore et
      if (isFormOpen) {
        return;
      }

      if (!browserSelection || browserSelection.rangeCount === 0) {
        clearSelection();
        return;
      }

      const range = browserSelection.getRangeAt(0);
      const selectedText = range.toString().trim();

      if (selectedText.length === 0) {
        clearSelection();
        return;
      }

      // Seçimin hangi sentence(lar) içinde olduğunu bul
      const sentenceData = findSentenceElements(range);

      if (!sentenceData || sentenceData.sentenceIds.length === 0) {
        console.log('[useTextSelection] No sentence elements found');
        clearSelection();
        return;
      }

      console.log('[useTextSelection] Found sentence IDs:', sentenceData.sentenceIds);

      // Çoklu sentence selection pozisyonunu hesapla
      const position = calculateMultiSentencePosition(range, sentenceData);

      if (!position) {
        clearSelection();
        return;
      }

      // Seçim geçerliliğini kontrol et
      const validation = validateTextSelection({
        text: selectedText,
        start: position.start,
        end: position.end
      });

      if (!validation.isValid) {
        clearSelection();
        return;
      }

      // Selection rect'i hesapla (popup pozisyonu için)
      const rect = range.getBoundingClientRect();
      setSelectionRect(rect);

      // Selection state'ini güncelle
      setSelection({
        start: position.start,
        end: position.end,
        text: selectedText,
        sentence_id: sentenceData.sentenceIds // Array olarak sakla
      });

      setIsSelecting(true);
  }, 100); // 100ms debounce
  }, [clearSelection, isFormOpen]);

  /**
   * Çoklu sentence element'lerini bulur
   */
  const findSentenceElements = useCallback((range: Range): {
    sentenceIds: string[];
    elements: Element[];
    startElement: Element;
    endElement: Element;
  } | null => {
    const sentenceElements = new Set<Element>();
    const sentenceIds: string[] = [];

    // Start ve end container'larından sentence element'lerini bul
    const startSentence = findSentenceElement(range.startContainer);
    const endSentence = findSentenceElement(range.endContainer);

    if (!startSentence || !endSentence) {
      return null;
    }

    // Eğer aynı sentence ise
    if (startSentence === endSentence) {
      const sentenceId = startSentence.getAttribute('data-sentence-id');
      // 🛡️ Sentence ID validation
      if (sentenceId && isValidSentenceId(sentenceId)) {
        return {
          sentenceIds: [sentenceId],
          elements: [startSentence],
          startElement: startSentence,
          endElement: endSentence
        };
      } else {
        return null; // Geçersiz sentence ID
      }
    }

    // Farklı sentence'lar ise, aralarındaki tüm sentence'ları bul
    let currentElement: Element | null = startSentence;

    while (currentElement) {
      const sentenceId = currentElement.getAttribute('data-sentence-id');
      // 🛡️ Sentence ID validation - geçersiz ID'leri filtrele
      if (sentenceId && isValidSentenceId(sentenceId) && !sentenceElements.has(currentElement)) {
        sentenceElements.add(currentElement);
        sentenceIds.push(sentenceId);
      }
      // Geçersiz ID'leri sessizce atla

      // End sentence'a ulaştık mı?
      if (currentElement === endSentence) {
        break;
      }

      // Bir sonraki sentence element'ini bul
      currentElement = findNextSentenceElement(currentElement);

      // Sonsuz loop'u önlemek için limit koy
      if (sentenceIds.length > 20) {
        console.warn('[findSentenceElements] Too many sentences selected, limiting to 20');
        console.warn('[findSentenceElements] Performance sorunları olabilir!');
        break;
      }

      // 10'dan fazla sentence seçildiğinde uyarı ver
      if (sentenceIds.length === 10) {
        console.warn('[findSentenceElements] Uzun seçim tespit edildi. Performance etkilenebilir.');
      }
    }

    return {
      sentenceIds,
      elements: Array.from(sentenceElements),
      startElement: startSentence,
      endElement: endSentence
    };
  }, []);

  /**
   * Tek sentence element'ini bulur (helper)
   */
  const findSentenceElement = useCallback((node: Node): Element | null => {
    let currentNode: Node | null = node;

    while (currentNode) {
      if (currentNode.nodeType === Node.ELEMENT_NODE) {
        const element = currentNode as Element;
        if (element.hasAttribute('data-sentence-id')) {
          return element;
        }
      }
      currentNode = currentNode.parentNode;
    }

    return null;
  }, []);

  /**
   * Bir sonraki sentence element'ini bulur
   * 🔧 Divider'ları atlar ve geçersiz ID'leri filtreler
   */
  const findNextSentenceElement = useCallback((element: Element): Element | null => {
    let nextElement = element.nextElementSibling;

    while (nextElement) {
      // 🔍 Divider element'ini atla
      if (isDividerElement(nextElement)) {
        nextElement = nextElement.nextElementSibling;
        continue;
      }

      // Sentence ID'si olan element'i bul
      if (nextElement.hasAttribute('data-sentence-id')) {
        const sentenceId = nextElement.getAttribute('data-sentence-id');

        // 🛡️ Sentence ID validation - geçersiz ID'leri filtrele
        if (sentenceId && isValidSentenceId(sentenceId)) {
          return nextElement;
        }
        // Geçersiz ID'yi atla, devam et
      }

      nextElement = nextElement.nextElementSibling;
    }

    return null;
  }, []);

  /**
   * Çoklu sentence selection pozisyonunu hesaplar
   */
  const calculateMultiSentencePosition = useCallback((
    range: Range,
    sentenceData: { sentenceIds: string[]; elements: Element[]; startElement: Element; endElement: Element }
  ): { start: number; end: number } | null => {
    try {
      // Tek sentence ise basit hesaplama
      if (sentenceData.sentenceIds.length === 1) {
        const preRange = document.createRange();
        preRange.setStart(sentenceData.startElement, 0);
        preRange.setEnd(range.startContainer, range.startOffset);

        const start = preRange.toString().length;
        const end = start + range.toString().length;

        return { start, end };
      }

      // Çoklu sentence için birleşik text pozisyonu hesapla
      let combinedText = '';
      let selectionStart = 0;
      let selectionEnd = 0;
      let foundStart = false;

      for (const element of sentenceData.elements) {
        const elementText = element.textContent || '';

        // Bu element'te selection başlıyor mu?
        if (element === sentenceData.startElement && !foundStart) {
          // Start element'te pozisyonu hesapla
          const preRange = document.createRange();
          preRange.setStart(element, 0);
          preRange.setEnd(range.startContainer, range.startOffset);

          selectionStart = combinedText.length + preRange.toString().length;
          foundStart = true;
        }

        combinedText += elementText;

        // Bu element'te selection bitiyor mu?
        if (element === sentenceData.endElement) {
          // End element'te pozisyonu hesapla
          const endRange = document.createRange();
          endRange.setStart(element, 0);
          endRange.setEnd(range.endContainer, range.endOffset);

          selectionEnd = combinedText.length - elementText.length + endRange.toString().length;
          break;
        }
      }

      return { start: selectionStart, end: selectionEnd };
    } catch (error) {
      console.error('[useTextSelection] Multi-sentence position calculation error:', error);
      return null;
    }
  }, []);

  /**
   * Annotation oluşturma için gerekli verileri hazırlar
   */
  const prepareAnnotationData = useCallback(async (
    bookId: string,
    sectionId: string
  ) => {
    return prepareAnnotationDataWithSelection(bookId, sectionId, selection);
  }, [selection, /* stable */]);

  /**
   * Belirli bir selection ile annotation data hazırlar
   */
  const prepareAnnotationDataWithSelection = useCallback(async (
    bookId: string,
    sectionId: string,
    selectionData: typeof selection
  ) => {
    console.log('[prepareAnnotationDataWithSelection] Starting with selection:', selectionData);

    if (!selectionData) {
      console.log('[prepareAnnotationDataWithSelection] No selection available');
      return null;
    }

    try {
      // Çoklu sentence desteği
      const sentenceIds = Array.isArray(selectionData.sentence_id)
        ? selectionData.sentence_id
        : [selectionData.sentence_id];

      console.log('[prepareAnnotationDataWithSelection] Looking for sentence elements:', sentenceIds);

      // İlk sentence element'ini bul (pozisyon hesaplama için)
      const firstSentenceId = sentenceIds[0];
      const firstSentenceElement = document.querySelector(`[data-sentence-id="${firstSentenceId}"]`);

      if (!firstSentenceElement) {
        console.error('[prepareAnnotationDataWithSelection] First sentence element not found:', firstSentenceId);
        throw new Error('İlk sentence element bulunamadı');
      }

      // Çoklu sentence ise tüm text'i birleştir
      let combinedText = '';
      for (const sentenceId of sentenceIds) {
        const element = document.querySelector(`[data-sentence-id="${sentenceId}"]`);
        if (element) {
          combinedText += element.textContent || '';
        } else {
          console.warn('[prepareAnnotationDataWithSelection] Sentence element not found:', sentenceId);
        }
      }

      const sentenceText = combinedText || firstSentenceElement.textContent || '';
      console.log('[prepareAnnotationDataWithSelection] Sentence text:', sentenceText);
      console.log('[prepareAnnotationDataWithSelection] Selection start/end:', selectionData.start, selectionData.end);

      // HTML-aware selection adjustment (çoklu sentence için birleşik HTML)
      let combinedHtml = '';
      for (const sentenceId of sentenceIds) {
        const element = document.querySelector(`[data-sentence-id="${sentenceId}"]`);
        if (element) {
          combinedHtml += element.innerHTML || '';
        }
      }

      const htmlContent = combinedHtml || firstSentenceElement.innerHTML;
      console.log('[prepareAnnotationDataWithSelection] Calling adjustSelectionForHtml...');
      const adjusted = adjustSelectionForHtml(htmlContent, selectionData.start, selectionData.end);
      console.log('[prepareAnnotationDataWithSelection] Adjusted selection:', adjusted);

      // Prefix ve suffix çıkar
      console.log('[prepareAnnotationDataWithSelection] Extracting prefix/suffix...');
      const prefix = extractPrefix(sentenceText, selectionData.start, 30);
      const suffix = extractSuffix(sentenceText, selectionData.end, 30);
      console.log('[prepareAnnotationDataWithSelection] Prefix/suffix:', prefix, suffix);

      // Word proximity çıkar
      console.log('[prepareAnnotationDataWithSelection] Extracting word proximity...');
      const wordProximity = extractWordProximity(
        sentenceText,
        selectionData.start,
        selectionData.end,
        3
      );
      console.log('[prepareAnnotationDataWithSelection] Word proximity:', wordProximity);

      // Hash'leri oluştur
      console.log('[prepareAnnotationDataWithSelection] Creating hashes...');
      const textHash = await createTextHash(selectionData.text);
      const sentenceHash = await createSentenceHash(sentenceText);
      console.log('[prepareAnnotationDataWithSelection] Hashes created:', textHash, sentenceHash);

      const result = {
        book_id: bookId,
        section_id: sectionId,
        sentence_id: Array.isArray(selectionData.sentence_id) ? selectionData.sentence_id : [selectionData.sentence_id],
        selection_start: adjusted.adjustedStart,
        selection_end: adjusted.adjustedEnd,
        selected_text: selectionData.text,
        prefix_text: prefix,
        suffix_text: suffix,
        word_proximity: wordProximity,
        text_hash: textHash,
        sentence_hash: sentenceHash
      };

      console.log('[prepareAnnotationDataWithSelection] Final result:', result);
      return result;
    } catch (error) {
      console.error('[prepareAnnotationDataWithSelection] Error:', error);
      return null;
    }
  }, []);

  /**
   * Programmatik olarak seçim oluşturur
   */
  const createSelection = useCallback((
    sentenceId: string,
    start: number,
    end: number
  ) => {
    const sentenceElement = document.querySelector(`[data-sentence-id="${sentenceId}"]`);

    if (!sentenceElement) {
      console.error('[useTextSelection] Sentence element not found:', sentenceId);
      return false;
    }

    try {
      const range = document.createRange();
      const textNode = findTextNode(sentenceElement, start);

      if (!textNode) {
        console.error('[useTextSelection] Text node not found');
        return false;
      }

      const { node: startNode, offset: startOffset } = textNode;
      const { node: endNode, offset: endOffset } = findTextNode(sentenceElement, end) || textNode;

      range.setStart(startNode, startOffset);
      range.setEnd(endNode, endOffset);

      const browserSelection = window.getSelection();
      if (browserSelection) {
        browserSelection.removeAllRanges();
        browserSelection.addRange(range);
      }

      // Selection state'ini güncelle
      const selectedText = range.toString();
      setSelection({
        start,
        end,
        text: selectedText,
        sentence_id: sentenceId
      });

      setSelectionRect(range.getBoundingClientRect());
      setIsSelecting(true);

      return true;
    } catch (error) {
      console.error('[useTextSelection] Create selection error:', error);
      return false;
    }
  }, []);

  /**
   * Text node bulur
   */
  const findTextNode = useCallback((
    element: Element,
    targetOffset: number
  ): { node: Text; offset: number } | null => {
    let currentOffset = 0;

    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null
    );

    let node = walker.nextNode();

    while (node) {
      const textNode = node as Text;
      const nodeLength = textNode.textContent?.length || 0;

      if (currentOffset + nodeLength >= targetOffset) {
        return {
          node: textNode,
          offset: targetOffset - currentOffset
        };
      }

      currentOffset += nodeLength;
      node = walker.nextNode();
    }

    return null;
  }, []);

  // Event listener'ları ekle
  useEffect(() => {
    document.addEventListener('selectionchange', handleSelectionChange);

    // Çift tıklama ve mouse up event'lerini de dinle
    const handleMouseUp = () => {
      // Kısa bir delay ile selection'ı kontrol et
      setTimeout(handleSelectionChange, 50);
    };

    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mouseup', handleMouseUp);
      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current);
      }
    };
  }, [handleSelectionChange]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearSelection();
    };
  }, [clearSelection]);

  /**
   * 🗑️ Seçilen alandaki tüm annotation'ları siler
   */
  const clearAnnotations = useCallback(async (
    deleteAnnotationFn: (annotationId: string) => Promise<boolean>
  ) => {
    if (!selection) {
      console.log('[clearAnnotations] No selection available');
      return false;
    }

    try {
      console.log('[clearAnnotations] Starting annotation cleanup for selection:', selection);

      // Seçilen alandaki annotation'ları bul
      const sentenceIds = Array.isArray(selection.sentence_id)
        ? selection.sentence_id
        : [selection.sentence_id];

      // DOM'dan annotation'ları bul
      const overlappingAnnotations: string[] = [];

      for (const sentenceId of sentenceIds) {
        const sentenceElement = document.querySelector(`[data-sentence-id="${sentenceId}"]`);
        if (!sentenceElement) continue;

        // Bu sentence'daki tüm annotation'ları bul
        const annotatedElements = sentenceElement.querySelectorAll('[data-annotation-id]');

        annotatedElements.forEach(element => {
          const annotationId = element.getAttribute('data-annotation-id');
          if (annotationId && !overlappingAnnotations.includes(annotationId)) {
            overlappingAnnotations.push(annotationId);
          }
        });
      }

      if (overlappingAnnotations.length === 0) {
        console.log('[clearAnnotations] No overlapping annotations found');
        return false; // nothing to delete -> report as no-op
      }

      console.log('[clearAnnotations] Found overlapping annotations:', overlappingAnnotations);

      // Tüm annotation'ları sil
      let deletedCount = 0;
      for (const annotationId of overlappingAnnotations) {
        try {
          const success = await deleteAnnotationFn(annotationId);
          if (success) {
            deletedCount++;
          }
        } catch (error) {
          console.error('[clearAnnotations] Failed to delete annotation:', annotationId, error);
        }
      }

      console.log(`[clearAnnotations] Successfully deleted ${deletedCount}/${overlappingAnnotations.length} annotations`);

      // Selection'ı temizle
      clearSelection();

      return deletedCount > 0;
    } catch (error) {
      console.error('[clearAnnotations] Error:', error);
      return false;
    }
  }, [selection, clearSelection]);

  return {
    // State
    selection,
    isSelecting,
    selectionRect,
    isFormOpen,

    // Actions
    clearSelection,
    prepareAnnotationData,
    prepareAnnotationDataWithSelection,
    createSelection,
    setIsFormOpen,
    clearAnnotations, // 🗑️ Yeni silme fonksiyonu

    // Computed
    hasSelection: selection !== null,
    selectedText: selection?.text || '',
    sentenceId: selection?.sentence_id || null
  };
}
