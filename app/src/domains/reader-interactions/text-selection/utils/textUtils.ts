/**
 * Text Processing Utilities
 * Metin işleme ve analiz fonksiyonları
 * Birleştirilmiş dosya: textProcessing.ts + hashUtils.ts
 */

// ============================================================================
// TEXT PROCESSING FUNCTIONS (eski textProcessing.ts)
// ============================================================================

/**
 * HTML tag'lerini temizler
 */
export function stripHtmlTags(text: string): string {
  return text.replace(/<[^>]*>/g, '');
}

/**
 * Metni normalize eder (boşlukları düzenler)
 */
export function normalizeText(text: string): string {
  return text
    .replace(/\s+/g, ' ') // Çoklu boşlukları tek boşluğa çevir
    .replace(/\n+/g, ' ') // Yeni satırları boşluğa çevir
    .trim(); // Başındaki ve sonundaki boşlukları temizle
}

/**
 * Metinden kelimeleri çıkarır
 */
export function extractWords(text: string): string[] {
  const cleanText = stripHtmlTags(normalizeText(text));
  return cleanText
    .split(/\s+/)
    .filter(word => word.length > 0)
    .map(word => word.toLowerCase().replace(/[^\w\u00C0-\u017F\u0100-\u024F]/g, '')); // Türkçe karakterleri korur
}

/**
 * Benzersiz kelimeleri çıkarır
 */
export function extractUniqueWords(text: string): string[] {
  const words = extractWords(text);
  return [...new Set(words)].filter(word => word.length > 2); // 2 karakterden uzun kelimeler
}

/**
 * Prefix text'i çıkarır (seçimden önceki N karakter)
 */
export function extractPrefix(fullText: string, selectionStart: number, length: number = 30): string {
  const start = Math.max(0, selectionStart - length);
  const prefix = fullText.substring(start, selectionStart);

  // Kelime sınırında kes
  const words = prefix.split(' ');
  if (words.length > 1 && start > 0) {
    words.shift(); // İlk kelimeyi çıkar (yarım olabilir)
  }

  return words.join(' ').trim();
}

/**
 * Suffix text'i çıkarır (seçimden sonraki N karakter)
 */
export function extractSuffix(fullText: string, selectionEnd: number, length: number = 30): string {
  const end = Math.min(fullText.length, selectionEnd + length);
  const suffix = fullText.substring(selectionEnd, end);

  // Kelime sınırında kes
  const words = suffix.split(' ');
  if (words.length > 1 && end < fullText.length) {
    words.pop(); // Son kelimeyi çıkar (yarım olabilir)
  }

  return words.join(' ').trim();
}

/**
 * Word proximity çıkarır (seçime yakın benzersiz kelimeler)
 */
export function extractWordProximity(
  fullText: string,
  selectionStart: number,
  selectionEnd: number,
  wordCount: number = 3
): string[] {
  const beforeText = fullText.substring(0, selectionStart);
  const afterText = fullText.substring(selectionEnd);

  const beforeWords = extractUniqueWords(beforeText).slice(-wordCount);
  const afterWords = extractUniqueWords(afterText).slice(0, wordCount);

  return [...beforeWords, ...afterWords];
}

/**
 * Text selection'ı validate eder
 */
export function validateTextSelection(selection: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!selection) {
    errors.push('Selection is required');
    return { isValid: false, errors };
  }

  if (!selection.text || typeof selection.text !== 'string' || selection.text.trim().length === 0) {
    errors.push('Selected text is required');
  }

  if (typeof selection.start !== 'number' || selection.start < 0) {
    errors.push('Valid start position is required');
  }

  if (typeof selection.end !== 'number' || selection.end <= selection.start) {
    errors.push('Valid end position is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * HTML-aware text selection (HTML tag'leri dikkate alarak seçim)
 */
export function adjustSelectionForHtml(
  htmlText: string,
  start: number,
  end: number
): { adjustedStart: number; adjustedEnd: number; selectedText: string } {
  // Geçici olarak HTML adjustment'ı devre dışı bırakıyoruz
  // Çünkü mevcut implementasyon yanlış çalışıyor

  const selectedText = stripHtmlTags(htmlText.substring(start, end));

  return {
    adjustedStart: start,
    adjustedEnd: end,
    selectedText: selectedText
  };
}

// ============================================================================
// HASH FUNCTIONS (eski hashUtils.ts)
// ============================================================================

/**
 * Metin için SHA-256 hash oluşturur
 */
export async function createTextHash(text: string): Promise<string> {
  const normalizedText = normalizeText(stripHtmlTags(text));

  // Browser environment
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    const encoder = new TextEncoder();
    const data = encoder.encode(normalizedText);
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  // Node.js environment (fallback) - Browser'da çalışmaz
  // try {
  //   return createHash('sha256').update(normalizedText).digest('hex');
  // } catch (error) {
    console.warn('[createTextHash] Crypto not available, using simple hash');
    return simpleHash(normalizedText);
  // }
}

/**
 * Cümle için hash oluşturur
 */
export async function createSentenceHash(sentenceText: string): Promise<string> {
  return createTextHash(sentenceText);
}

/**
 * Basit hash fonksiyonu (fallback)
 */
function simpleHash(text: string): string {
  let hash = 0;
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 32-bit integer'a çevir
  }
  return Math.abs(hash).toString(16);
}

/**
 * Hash'leri karşılaştırır
 */
export function compareHashes(hash1: string, hash2: string): boolean {
  return hash1 === hash2;
}

/**
 * Metin integrity'sini doğrular
 */
export async function verifyTextIntegrity(text: string, expectedHash: string): Promise<{ isValid: boolean; actualHash: string }> {
  const actualHash = await createTextHash(text);
  return {
    isValid: compareHashes(actualHash, expectedHash),
    actualHash
  };
}
