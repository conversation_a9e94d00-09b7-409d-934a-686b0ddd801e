// Reader Interactions UI Constants

// Modal and Sheet Sizes
export const MODAL_SIZES = {
  annotation: {
    mobile: {
      maxHeight: '85vh',
      borderRadius: 'rounded-t-2xl'
    },
    desktop: {
      width: '500px',
      maxHeight: '75vh',
      borderRadius: 'rounded-2xl',
      position: 'top-[12%] left-1/2 transform -translate-x-1/2'
    }
  },
  bookmark: {
    mobile: {
      maxHeight: '85vh',
      borderRadius: 'rounded-t-2xl'
    },
    desktop: {
      width: '500px',
      maxHeight: '75vh',
      borderRadius: 'rounded-2xl',
      position: 'top-[12%] left-1/2 transform -translate-x-1/2'
    }
  },
  search: {
    mobile: {
      maxHeight: '90vh',
      borderRadius: 'rounded-t-2xl'
    },
    desktop: {
      width: '600px',
      maxHeight: '80vh',
      borderRadius: 'rounded-2xl',
      position: 'top-[8%] left-1/2 transform -translate-x-1/2'
    }
  }
} as const;

// Color Palettes
export const ANNOTATION_COLORS = [
  { name: 'Sarı', value: '#fbbf24', bg: '#fef3c7' },
  { name: 'Yeşil', value: '#10b981', bg: '#d1fae5' },
  { name: 'Mavi', value: '#3b82f6', bg: '#dbeafe' },
  { name: 'Mor', value: '#8b5cf6', bg: '#ede9fe' },
  { name: 'Pembe', value: '#ec4899', bg: '#fce7f3' },
  { name: 'Turuncu', value: '#f97316', bg: '#fed7aa' }
] as const;

export const HIGHLIGHT_COLORS = [
  { name: 'Temizle', value: 'clear', bg: 'transparent', isSpecial: true },
  { name: 'Sarı', value: '#fbbf24', bg: '#fef3c7' },
  { name: 'Yeşil', value: '#10b981', bg: '#d1fae5' },
  { name: 'Mavi', value: '#3b82f6', bg: '#dbeafe' },
  { name: 'Mor', value: '#8b5cf6', bg: '#ede9fe' },
  { name: 'Pembe', value: '#ec4899', bg: '#fce7f3' },
  { name: 'Turuncu', value: '#f59e0b', bg: '#fed7aa' },
  { name: 'Kırmızı', value: '#ef4444', bg: '#fecaca' },
  { name: 'Teal', value: '#14b8a6', bg: '#ccfbf1' }
] as const;

export const COLLECTION_COLORS = [
  '#3b82f6', // Blue
  '#ef4444', // Red
  '#10b981', // Green
  '#f59e0b', // Yellow
  '#8b5cf6', // Purple
  '#06b6d4', // Cyan
  '#f97316', // Orange
  '#84cc16'  // Lime
] as const;

// Form Styles
export const FORM_STYLES = {
  input: {
    base: 'w-full p-3 rounded-lg border transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
    error: 'border-red-500 focus:ring-red-500',
    success: 'border-green-500 focus:ring-green-500'
  },
  textarea: {
    base: 'w-full p-3 border rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
    rows: {
      small: 3,
      medium: 4,
      large: 6
    }
  },
  button: {
    primary: 'py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold shadow-md hover:shadow-lg',
    secondary: 'py-3 px-4 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 transition-colors font-medium',
    ghost: 'p-2 rounded-lg hover:bg-opacity-10 hover:bg-gray-500 transition-colors',
    action: 'p-2 rounded text-[var(--text-color)]/80 hover:text-[var(--text-color)] transition-colors duration-150 flex items-center justify-center'
  },
  spacing: {
    form: 'space-y-4',
    section: 'space-y-6',
    grid: 'grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    actions: 'flex space-x-3'
  }
} as const;

// Animation Durations
export const ANIMATIONS = {
  fast: '120ms',
  normal: '150ms',
  slow: '300ms',
  sheet: '300ms'
} as const;

// Z-Index Layers
export const Z_INDEX = {
  backdrop: 40,
  sheet: 50,
  modal: 50,
  menu: 50,
  tooltip: 60
} as const;

// Breakpoints (matching Tailwind)
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px'
} as const;
