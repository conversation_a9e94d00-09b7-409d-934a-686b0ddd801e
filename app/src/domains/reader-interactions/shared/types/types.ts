/**
 * Reader Interactions Domain Types
 * Annotation (Şerh) sistemi için tip tanımları
 */

// Annotation türleri
export type AnnotationType = 'note' | 'sherh' | 'highlight' | 'bookmark';

// Priority sistemi kaldırıldı - gereksizdi

// Ana annotation interface'i
export interface Annotation {
  id: string;
  user_id: string;
  user_profile?: {
    display_name?: string;
    username?: string;
  };

  // Metin referansı
  book_id: string;
  section_id: string;
  sentence_id: string | string[]; // Tek veya çoklu sentence ID'leri destekle

  // Pozisyon bilgileri
  selection_start: number;
  selection_end: number;
  selected_text: string;

  // Recovery sistemi (flat structure)
  prefix_text: string;
  suffix_text: string;
  word_proximity: string[];
  text_hash: string;
  sentence_hash: string;

  // Annotation içeriği
  annotation_type: AnnotationType;
  annotation_content?: string;

  // Görsel özellikler
  color: string;
  highlight_style?: 'background' | 'text'; // Vurgulama stili

  // Sosyal & organizasyon
  is_public: boolean;
  tags: string[];

  // Metadata
  metadata: Record<string, unknown>;

  // Collection (sadece bookmark'lar için)
  collection_id?: string;

  created_at: string;
  updated_at: string;
}

// Annotation oluşturma için input tipi
export interface CreateAnnotationInput {
  user_id: string; // User ID zorunlu
  book_id: string;
  section_id: string;
  sentence_id: string | string[]; // Tek veya çoklu sentence ID'leri destekle
  selection_start: number;
  selection_end: number;
  selected_text: string;
  prefix_text: string;
  suffix_text: string;
  word_proximity: string[];
  text_hash: string;
  sentence_hash: string;
  annotation_type: AnnotationType;
  annotation_content?: string;
  color?: string;
  highlight_style?: 'background' | 'text'; // Vurgulama stili
  is_public?: boolean;
  tags?: string[];
  metadata?: Record<string, unknown>;
  collection_id?: string; // Bookmark koleksiyonu
}

// Annotation güncelleme için input tipi
export interface UpdateAnnotationInput {
  annotation_content?: string;
  color?: string;
  highlight_style?: 'background' | 'text'; // Vurgulama stili
  is_public?: boolean;
  tags?: string[];
  metadata?: Record<string, unknown>;
}

// Metin seçimi için tip
export interface TextSelection {
  start: number;
  end: number;
  text: string;
  sentence_id: string | string[]; // Tek veya çoklu sentence ID'leri destekle
}

// Annotation pozisyon recovery için tip
export interface AnnotationPosition {
  start: number;
  end: number;
  confidence: number; // 0-1 arası güven skoru
}

// Annotation filtreleme için tip
export interface AnnotationFilters {
  book_id?: string;
  section_id?: string;
  sentence_id?: string | string[];
  annotation_type?: AnnotationType;
  tags?: string[];
  is_public?: boolean;
  user_id?: string;
  exclude_user_id?: string;
  select?: 'count';
}

// Annotation istatistikleri için tip
export interface AnnotationStats {
  total_count: number;
  by_type: Record<AnnotationType, number>;
  recent_count: number; // Son 7 gündeki annotation sayısı
}

// Bookmark koleksiyonu için tip
export interface BookmarkCollection {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  is_default: boolean;
  is_public: boolean;
  color: string;
  icon?: string;
  created_at: string;
  updated_at: string;
}

// Koleksiyon oluşturma için input tipi
export interface CreateCollectionInput {
  user_id: string;
  name: string;
  description?: string;
  is_default?: boolean;
  is_public?: boolean;
  color?: string;
  icon?: string;
}

// Koleksiyon güncelleme için input tipi
export interface UpdateCollectionInput {
  name?: string;
  description?: string;
  is_public?: boolean;
  color?: string;
  icon?: string;
}

// Realtime subscription için tip
export interface AnnotationSubscription {
  book_id: string;
  section_id?: string;
  callback: (annotation: Annotation, event: 'INSERT' | 'UPDATE' | 'DELETE') => void;
}

// Error tipleri
export interface AnnotationError {
  code: string;
  message: string;
  details?: unknown;
}

// Service response tipleri
export interface AnnotationServiceResponse<T> {
  data: T | null;
  error: AnnotationError | null;
}

// Bulk operations için tipler
export interface BulkAnnotationOperation {
  operation: 'create' | 'update' | 'delete';
  annotations: (CreateAnnotationInput | UpdateAnnotationInput | string)[];
}

export interface BulkAnnotationResult {
  success_count: number;
  error_count: number;
  errors: Array<{
    index: number;
    error: AnnotationError;
  }>;
}

/**
 * Bookmark Koleksiyonu
 */
export interface BookmarkCollection {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  color: string; // Hex color code
  icon?: string; // Icon name
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Koleksiyon oluşturma input'u
 */
export interface CreateCollectionInput {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  is_default?: boolean;
  user_id: string;
}

/**
 * Koleksiyon güncelleme input'u
 */
export interface UpdateCollectionInput {
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
  is_default?: boolean;
}
