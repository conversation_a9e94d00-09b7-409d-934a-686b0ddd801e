import { useEffect, RefObject } from 'react';
import { MODAL_SIZES } from '../constants/ui';
import { useReaderInteractionStyles } from './useReaderInteractionStyles';

type SheetType = 'annotation' | 'bookmark' | 'search';

interface UseBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  sheetRef?: RefObject<HTMLDivElement>;
  type?: SheetType;
  enableClickOutside?: boolean;
  enableEscapeKey?: boolean;
  enableBodyScrollLock?: boolean;
}

export const useBottomSheet = ({
  isOpen,
  onClose,
  sheetRef,
  type = 'annotation',
  enableClickOutside = true,
  enableEscapeKey = true,
  enableBodyScrollLock = true
}: UseBottomSheetProps) => {
  const { getSheetStyles, getBackdropStyles } = useReaderInteractionStyles();

  // Click outside to close
  useEffect(() => {
    if (!isOpen || !enableClickOutside || !sheetRef?.current) return;
    
    const handleClickOutside = (event: MouseEvent) => {
      if (sheetRef.current && !sheetRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, sheetRef, enableClickOutside]);

  // ESC key to close
  useEffect(() => {
    if (!isOpen || !enableEscapeKey) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose, enableEscapeKey]);

  // Body scroll lock
  useEffect(() => {
    if (!enableBodyScrollLock) return;

    if (isOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [isOpen, enableBodyScrollLock]);

  // Get sheet configuration
  const getSheetConfig = () => {
    const sizes = MODAL_SIZES[type];
    const sheetStyles = getSheetStyles('sheet');
    
    return {
      sizes,
      styles: sheetStyles,
      className: `fixed z-50 bottom-0 left-0 right-0 max-h-[85vh] md:top-[12%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[${sizes.desktop.width}] md:max-h-[75vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20 overflow-hidden`
    };
  };

  // Get backdrop configuration
  const getBackdropConfig = () => ({
    className: "fixed inset-0 bg-black/40 backdrop-blur-[1px] transition-opacity",
    style: getBackdropStyles(isOpen)
  });

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (enableClickOutside && e.target === e.currentTarget) {
      onClose();
    }
  };

  return {
    isOpen,
    getSheetConfig,
    getBackdropConfig,
    handleBackdropClick
  };
};
