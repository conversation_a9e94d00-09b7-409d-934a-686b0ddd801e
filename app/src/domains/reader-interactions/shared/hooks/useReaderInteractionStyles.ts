import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { FORM_STYLES, ANIMATIONS, Z_INDEX } from '../constants/ui';

export const useReaderInteractionStyles = () => {
  // Background colors with different opacity levels
  const bgColors = {
    sheet: useAutoOverlay(8, 'var(--bg-color)'),
    card: useAutoOverlay(12, 'var(--bg-color)'),
    menu: useAutoOverlay(21, 'var(--bg-color)'),
    input: useAutoOverlay(4, 'var(--bg-color)'),
    hover: useAutoOverlay(8, 'var(--bg-color)')
  };

  // Border colors
  const borderColors = {
    default: useAutoOverlay(5, 'var(--bg-color)'),
    light: useAutoOverlay(8, 'var(--bg-color)'),
    strong: useAutoOverlay(15, 'var(--bg-color)')
  };

  // Get input styles with dynamic colors
  const getInputStyles = (hasError?: boolean, isSuccess?: boolean) => {
    let className = FORM_STYLES.input.base;
    
    if (hasError) {
      className += ` ${FORM_STYLES.input.error}`;
    } else if (isSuccess) {
      className += ` ${FORM_STYLES.input.success}`;
    }

    const borderColor = hasError 
      ? '#ef4444'
      : isSuccess 
        ? '#22c55e'
        : borderColors.default;

    return {
      className,
      style: {
        backgroundColor: bgColors.input,
        borderColor,
        color: 'var(--text-color)'
      }
    };
  };

  // Get textarea styles
  const getTextareaStyles = (rows: 'small' | 'medium' | 'large' = 'medium', hasError?: boolean) => ({
    className: FORM_STYLES.textarea.base + (hasError ? ` ${FORM_STYLES.input.error}` : ''),
    style: {
      backgroundColor: bgColors.input,
      borderColor: hasError ? '#ef4444' : borderColors.default,
      color: 'var(--text-color)'
    },
    rows: FORM_STYLES.textarea.rows[rows]
  });

  // Get button styles
  const getButtonStyles = (variant: 'primary' | 'secondary' | 'ghost' | 'action' = 'primary', disabled = false) => {
    const baseStyle = {
      opacity: disabled ? 0.5 : 1,
      cursor: disabled ? 'not-allowed' : 'pointer'
    };

    switch (variant) {
      case 'secondary':
        return {
          className: FORM_STYLES.button.secondary,
          style: {
            ...baseStyle,
            backgroundColor: bgColors.card,
            borderColor: borderColors.default,
            color: 'var(--text-color)'
          }
        };
      case 'ghost':
        return {
          className: FORM_STYLES.button.ghost,
          style: {
            ...baseStyle,
            color: 'var(--text-color)'
          }
        };
      case 'action':
        return {
          className: FORM_STYLES.button.action,
          style: baseStyle
        };
      default: // primary
        return {
          className: FORM_STYLES.button.primary,
          style: baseStyle
        };
    }
  };

  // Get sheet/modal styles
  const getSheetStyles = (type: 'sheet' | 'card' | 'menu' = 'sheet') => ({
    backgroundColor: bgColors[type],
    borderColor: borderColors.light
  });

  // Get backdrop styles
  const getBackdropStyles = (isVisible: boolean) => ({
    opacity: isVisible ? 1 : 0,
    zIndex: Z_INDEX.backdrop,
    transition: `opacity ${ANIMATIONS.sheet} ease-out`
  });

  // Get menu positioning styles with smart viewport bounds
  const getMenuStyles = (position: { top: number; left: number; placement?: 'above' | 'below' }, isVisible: boolean, isMobile = false) => {
    // ✅ SMART POSITIONING - Viewport sınırlarını kontrol et
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // Menü tahmini genişlikleri
    const estimatedMenuWidth = isMobile ? 200 : 350; // Mobilde daha dar
    const estimatedMenuHeight = isMobile ? 80 : 45;  // Mobilde daha uzun (2 satır)
    const margin = 16; // Kenarlardan minimum mesafe
    
    // Yatay pozisyon optimizasyonu
    let adjustedLeft = position.left;
    let transform = 'translate(-50%, ';
    
    // Sol kenara çok yakınsa
    if (position.left - estimatedMenuWidth/2 < margin) {
      adjustedLeft = margin + estimatedMenuWidth/2;
      transform = 'translate(-50%, '; // Ortalanmış kal
    }
    // Sağ kenara çok yakınsa  
    else if (position.left + estimatedMenuWidth/2 > viewportWidth - margin) {
      adjustedLeft = viewportWidth - margin - estimatedMenuWidth/2;
      transform = 'translate(-50%, '; // Ortalanmış kal
    }
    
    // Dikey pozisyon optimizasyonu
    let adjustedTop = position.top;
    let verticalTransform = '';
    
    if (position?.placement === 'below') {
      // Aşağıda gösterilecek - alt sınırı kontrol et
      if (position.top + estimatedMenuHeight + margin > viewportHeight) {
        // Alt sınırı aşıyor, yukarı göster
        verticalTransform = 'calc(-100% + 10px))';
        adjustedTop = position.top;
      } else {
        // Normal aşağı göster
        verticalTransform = '-10px)';
      }
    } else {
      // Yukarıda gösterilecek - üst sınırı kontrol et
      if (position.top - estimatedMenuHeight - margin < 0) {
        // Üst sınırı aşıyor, aşağı göster
        verticalTransform = '-10px)';
      } else {
        // Normal yukarı göster
        verticalTransform = 'calc(-100% + 10px))';
      }
    }
    
    const finalTransform = transform + verticalTransform;

    return {
      position: 'absolute' as const,
      top: `${adjustedTop}px`,
      left: `${adjustedLeft}px`,
      transform: finalTransform,
      zIndex: Z_INDEX.menu,
      opacity: isVisible ? 1 : 0,
      transformOrigin: position?.placement === 'below' ? 'top center' : 'bottom center',
      transition: `opacity ${ANIMATIONS.fast} ease-out, transform ${ANIMATIONS.fast} ease-out, background-color ${ANIMATIONS.normal} ease, border-color ${ANIMATIONS.normal} ease`,
      pointerEvents: isVisible ? 'auto' as const : 'none' as const,
      // ✅ Max width ekleyerek taşmayı önle
      maxWidth: `${viewportWidth - 2 * margin}px`,
      ...getSheetStyles('menu')
    };
  };

  return {
    bgColors,
    borderColors,
    getInputStyles,
    getTextareaStyles,
    getButtonStyles,
    getSheetStyles,
    getBackdropStyles,
    getMenuStyles,
    spacing: FORM_STYLES.spacing,
    animations: ANIMATIONS,
    zIndex: Z_INDEX
  };
};
