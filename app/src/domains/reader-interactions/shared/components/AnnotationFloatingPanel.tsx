import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { MessageSquare, Bookmark, Eye, EyeOff, Archive, ChevronUp, ChevronDown, FileText } from 'lucide-react';
import HighlightIcon from '@shared/components/Icons/HighlightIcon';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import type { Annotation } from '../types';

// Modern, entegre annotation tipi konfigürasyonları
const ANNOTATION_TYPES = {
  note: {
    title: 'Notlarım',
    icon: FileText,
    key: 'showNotes'
  },
  sherh: {
    title: '<PERSON>erhlerim', 
    icon: MessageSquare,
    key: 'showSherhs'
  },
  highlight: {
    title: 'Vurgularım',
    icon: HighlightIcon as any,
    key: 'showHighlights'
  },
  bookmark: {
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    icon: Bookmark,
    key: 'showBookmarks'
  }
} as const;

// Modern aksiyon butonu komponenti
const ModernButton: React.FC<{
  onClick: (e?: React.MouseEvent) => void;
  title: string;
  children: React.ReactNode;
  variant?: 'ghost' | 'outline';
  size?: 'sm' | 'md';
  active?: boolean;
}> = ({ onClick, title, children, variant = 'ghost', size = 'md', active = false }) => {
  const baseClasses = `
    ${size === 'sm' ? 'p-1.5' : 'p-2'} 
    rounded-lg transition-all duration-200 
    flex items-center justify-center
    hover:scale-105 active:scale-95
  `;
  
  const variantClasses = {
    ghost: `hover:bg-[var(--text-color)]/8 ${active ? 'bg-[var(--text-color)]/12' : ''}`,
    outline: `border hover:bg-[var(--text-color)]/5 ${active ? 'bg-[var(--text-color)]/8 border-[var(--text-color)]/20' : 'border-[var(--text-color)]/10'}`
  };

  return (
    <button
      onClick={onClick}
      title={title}
      className={`${baseClasses} ${variantClasses[variant]}`}
      style={{ 
        color: 'var(--text-color)', 
        opacity: active ? 1 : 0.7
      }}
    >
      {children}
    </button>
  );
};

// Modern annotation item komponenti
const ModernAnnotationItem: React.FC<{
  annotation: Annotation;
  onClick: (annotation: Annotation) => void;
  index: number;
  bgColor?: string;
}> = ({ annotation, onClick, index, bgColor }) => {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Parent'ın expand olmasını engelle
    onClick(annotation);
  };

  return (
    <div
      onClick={handleClick}
      className="group p-3 rounded-xl cursor-pointer transition-all duration-200 hover:scale-[1.01] active:scale-[0.99] border"
      style={{ 
        animationDelay: `${index * 50}ms`,
        animation: 'fadeInUp 0.3s ease-out forwards',
        backgroundColor: bgColor || 'transparent',
        borderColor: 'color-mix(in srgb, var(--text-color) 8%, transparent)'
      }}
    >
      {annotation.selected_text && (
        <div 
          className="font-medium text-sm mb-2 line-clamp-2 group-hover:text-[var(--color-primary)] transition-colors"
          style={{ color: 'var(--text-color)' }}
        >
          "{annotation.selected_text}"
        </div>
      )}
      {annotation.annotation_content && (
        <div 
          className="text-xs opacity-75 line-clamp-2"
          style={{ color: 'var(--text-color)' }}
        >
          {annotation.annotation_content}
        </div>
      )}
    </div>
  );
};

// Modern bölüm komponenti
const ModernSection: React.FC<{
  type: keyof typeof ANNOTATION_TYPES;
  annotations: Annotation[];
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
  onAnnotationClick: (annotation: Annotation) => void;
  user: any;
  onUnauthenticated: () => void;
}> = ({ 
  type, 
  annotations, 
  isExpanded, 
  setIsExpanded, 
  isVisible, 
  setIsVisible, 
  onAnnotationClick,
  user,
  onUnauthenticated
}) => {
  const config = ANNOTATION_TYPES[type];
  const Icon = config.icon;
  const maxDisplay = 5;
  
  // Katmanlı renkler - Daha da belirgin farklar
  const sectionBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const itemBgColor = useAutoOverlay(4, 'var(--bg-color)');
  
  // Tümünü göster state'i
  const [showAll, setShowAll] = React.useState(false);
  
  const handleToggleVisibility = () => {
    if (!user) {
      onUnauthenticated();
      return;
    }
    setIsVisible(!isVisible);
  };

  const displayedAnnotations = showAll ? annotations : annotations.slice(0, maxDisplay);
  const hasMore = annotations.length > maxDisplay;

  return (
    <div 
      className="mb-2 last:mb-0 rounded-xl border"
      style={{ 
        backgroundColor: sectionBgColor,
        borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)'
      }}
    >
      {/* Modern header */}
      <div 
        className="flex items-center justify-between p-3 hover:bg-[var(--text-color)]/5 transition-colors rounded-t-xl cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <div 
            className="p-2 rounded-lg" 
            style={{ backgroundColor: itemBgColor }}
          >
            <Icon size={16} style={{ color: 'var(--text-color)' }} />
          </div>
          <div>
            <h4 className="text-sm font-semibold" style={{ color: 'var(--text-color)' }}>
              {config.title}
            </h4>
            <p className="text-xs opacity-60" style={{ color: 'var(--text-color)' }}>
              {annotations.length} öğe
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <ModernButton
            onClick={(e) => {
              e?.stopPropagation();
              handleToggleVisibility();
            }}
            title={isVisible ? 'Gizle' : 'Göster'}
            size="sm"
            active={isVisible}
          >
            {isVisible ? <EyeOff size={14} /> : <Eye size={14} />}
          </ModernButton>
          
          <ModernButton
            onClick={(e) => {
              e?.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
            title={isExpanded ? 'Daralt' : 'Genişlet'}
            size="sm"
            active={isExpanded}
          >
            <ChevronDown 
              size={14} 
              className={`transition-transform duration-300 ${isExpanded ? 'rotate-180' : ''}`} 
            />
          </ModernButton>
        </div>
      </div>

      {/* Modern içerik */}
      {isExpanded && (
        <div className="px-3 pb-3">
          {annotations.length > 0 ? (
            <div className="space-y-1 max-h-80 overflow-y-auto">
              {displayedAnnotations.map((annotation, index) => (
                <ModernAnnotationItem
                  key={annotation.id}
                  annotation={annotation}
                  onClick={onAnnotationClick}
                  index={index}
                  bgColor={itemBgColor}
                />
              ))}
              
              {hasMore && !showAll && (
                <button
                  onClick={() => setShowAll(true)}
                  className="w-full text-center py-3 text-xs rounded-lg opacity-60 hover:opacity-100 hover:scale-105 transition-all cursor-pointer border-2 border-dashed hover:border-solid"
                  style={{ 
                    color: 'var(--text-color)',
                    borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
                  }}
                >
                  <div className="flex items-center justify-center gap-2">
                    <span>+{annotations.length - maxDisplay} öğe daha var</span>
                    <ChevronDown size={12} />
                  </div>
                </button>
              )}
              
              {showAll && hasMore && (
                <button
                  onClick={() => setShowAll(false)}
                  className="w-full text-center py-2 text-xs rounded-lg opacity-60 hover:opacity-100 transition-all cursor-pointer"
                  style={{ 
                    color: 'var(--text-color)',
                    backgroundColor: itemBgColor
                  }}
                >
                  <div className="flex items-center justify-center gap-2">
                    <span>Daha az göster</span>
                    <ChevronUp size={12} />
                  </div>
                </button>
              )}
            </div>
          ) : (
            <div 
              className="text-center py-6 rounded-xl border-2 border-dashed"
              style={{ 
                borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)',
                backgroundColor: itemBgColor
              }}
            >
              <Icon size={20} className="mx-auto mb-2 opacity-20" style={{ color: 'var(--text-color)' }} />
              <p className="text-xs opacity-50" style={{ color: 'var(--text-color)' }}>
                Henüz {config.title.toLowerCase()} yok
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

interface AnnotationFloatingPanelProps {
  className?: string;
  annotations: Annotation[];
  showNotes: boolean;
  setShowNotes: (show: boolean) => void;
  showSherhs: boolean;
  setShowSherhs: (show: boolean) => void;
  showHighlights: boolean;
  setShowHighlights: (show: boolean) => void;
  showBookmarks: boolean;
  setShowBookmarks: (show: boolean) => void;
  onAnnotationDetailOpen?: (annotation: Annotation) => void;
}

export function AnnotationFloatingPanel({
  className = '',
  annotations,
  showNotes,
  setShowNotes,
  showSherhs,
  setShowSherhs,
  showHighlights,
  setShowHighlights,
  showBookmarks,
  setShowBookmarks,
  onAnnotationDetailOpen,
}: AnnotationFloatingPanelProps) {
  const navigate = useNavigate();
  const user = useAuthStore((state) => state.user);
  const isMobile = useIsMobile();
  
  // Katmanlı renk sistemi - Daha belirgin derinlik farkları
  const panelBgColor = useAutoOverlay(12, 'var(--bg-color)');
  const headerBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(6, 'var(--bg-color)');
  const cardBgColor = useAutoOverlay(8, 'var(--bg-color)');
  
  // Modern state yönetimi
  const [isExpanded, setIsExpanded] = useState(false);
  const [suppressWhileSelection, setSuppressWhileSelection] = useState(false);
  
  // Bölüm durumları
  const [sectionStates, setSectionStates] = useState({
    note: true,
    sherh: true,
    highlight: true,
    bookmark: true
  });

  // Annotation'ları tiplere göre ayır
  const annotationsByType = useMemo(() => ({
    note: annotations.filter(a => a.annotation_type === 'note'),
    sherh: annotations.filter(a => a.annotation_type === 'sherh'),
    highlight: annotations.filter(a => a.annotation_type === 'highlight'),
    bookmark: annotations.filter(a => a.annotation_type === 'bookmark'),
  }), [annotations]);

  const totalCount = useMemo(() => 
    Object.values(annotationsByType).reduce((sum, arr) => sum + arr.length, 0),
    [annotationsByType]
  );

  const visibilityStates = {
    note: showNotes,
    sherh: showSherhs,
    highlight: showHighlights,
    bookmark: showBookmarks
  };

  const visibilitySetters = {
    note: setShowNotes,
    sherh: setShowSherhs,
    highlight: setShowHighlights,
    bookmark: setShowBookmarks
  };

  const allVisible = Object.values(visibilityStates).every(Boolean);

  // Event handlers
  const handleAnnotationClick = useCallback((annotation: Annotation) => {
    if (!user) {
      alert('Bu özelliği kullanmak için üye olmanız gerekiyor.');
      return;
    }
    
    // Annotation detay sheet'ini açmak için callback'i çağır
    if (onAnnotationDetailOpen) {
      onAnnotationDetailOpen(annotation);
      return;
    }
    
    // Fallback: Element'e scroll (eski davranış)
    const element = document.getElementById(`sentence-${annotation.sentence_id}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      element.style.transition = 'all 0.4s ease';
      element.style.backgroundColor = 'var(--color-primary)';
      element.style.opacity = '0.8';
      setTimeout(() => {
        element.style.backgroundColor = 'transparent';
        element.style.opacity = '1';
      }, 800);
    }
  }, [user, onAnnotationDetailOpen]);

  const handleUnauthenticated = useCallback(() => {
    alert('Şerh ve vurgulama özelliklerini kullanmak için üye olmanız gerekiyor.');
  }, []);

  const toggleAllVisibility = useCallback(() => {
    const newState = !allVisible;
    Object.values(visibilitySetters).forEach(setter => setter(newState));
  }, [allVisible, visibilitySetters]);

  const updateSectionState = (type: keyof typeof sectionStates, expanded: boolean) => {
    setSectionStates(prev => ({ ...prev, [type]: expanded }));
  };

  // Basit ve etkili boyutlar
  const panelSizes = {
    collapsed: { width: '250px' },
    expanded: { width: '400px' }
  };

  // Mobil için bottom sheet, desktop için floating panel
  const getMobileStyle = () => {
    if (!isMobile) return {};
    
    if (isExpanded) {
      return {
        position: 'fixed' as const,
        bottom: '0',
        left: '0',
        right: '0',
        width: '100%',
        maxHeight: '90vh',
        borderRadius: '16px 16px 0 0',
        zIndex: 50
      };
    } else {
      return {
        position: 'fixed' as const,
        bottom: '20px',
        right: '20px',
        width: 'auto',
        zIndex: 50
      };
    }
  };

  const getDesktopStyle = () => {
    if (isMobile) return {};
    
    return {
      position: 'fixed' as const,
      bottom: '30px',
      right: '30px',
      width: isExpanded ? panelSizes.expanded.width : panelSizes.collapsed.width,
      zIndex: 50
    };
  };

  const panelStyle = isMobile ? getMobileStyle() : getDesktopStyle();

  // Seçim paneli açıldığında mobilde paneli tamamen gizle; kapanınca geri göster
  useEffect(() => {
    if (!isMobile) return;
    const handleOpen = () => {
      setIsExpanded(false);
      setSuppressWhileSelection(true);
    };
    const handleClose = () => setSuppressWhileSelection(false);
    window.addEventListener('ikra:selection-sheet-open', handleOpen);
    window.addEventListener('ikra:selection-sheet-close', handleClose);
    return () => {
      window.removeEventListener('ikra:selection-sheet-open', handleOpen);
      window.removeEventListener('ikra:selection-sheet-close', handleClose);
    };
  }, [isMobile]);

  // Mobilde seçim paneli açıkken hiç render etme
  if (isMobile && suppressWhileSelection) {
    return null;
  }

  return (
    <>
      {/* CSS Animasyonları */}
      <style>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes slideIn {
          from {
            transform: scale(0.95) translateY(10px);
            opacity: 0;
          }
          to {
            transform: scale(1) translateY(0);
            opacity: 1;
          }
        }
      `}</style>

      <div
        className={`transition-all duration-500 ease-out ${className}`}
        style={{
          ...panelStyle,
          animation: 'slideIn 0.4s ease-out'
        }}
      >
        {!isExpanded ? (
          // Modern daraltılmış görünüm
          <div
            className={`cursor-pointer transition-all duration-300 ${
              isMobile 
                ? 'rounded-2xl shadow-lg border hover:shadow-xl' 
                : 'backdrop-blur-lg rounded-2xl shadow-2xl border hover:shadow-3xl hover:scale-105'
            }`}
            style={{ 
              backgroundColor: isMobile ? panelBgColor : `${panelBgColor}e6`,
              borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)'
            }}
            onClick={() => {
              if (!user) {
                handleUnauthenticated();
                return;
              }
              if (totalCount > 0) setIsExpanded(true);
            }}
          >
            {isMobile ? (
              // Mobil: Optimal boyut sağ alt çözüm
              <div className="p-3">
                <div className="flex items-center gap-2">
                  <FileText size={16} style={{ color: 'var(--text-color)' }} />
                  <span className="text-sm font-bold" style={{ color: 'var(--text-color)' }}>
                    {totalCount}
                  </span>
                  {totalCount > 0 && (
                    <ChevronUp size={14} style={{ color: 'var(--text-color)', opacity: 0.7 }} />
                  )}
                </div>
              </div>
            ) : (
              // Desktop: Orijinal kompakt görünüm
              <>
                {/* Kompakt header */}
                <div className="p-3">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-bold text-sm" style={{ color: 'var(--text-color)' }}>
                      İçeriklerim
                    </h3>
                    {totalCount > 0 && (
                      <div 
                        className="px-2 py-1 rounded-full text-xs font-bold shadow"
                        style={{ 
                          backgroundColor: headerBgColor,
                          color: 'var(--text-color)'
                        }}
                      >
                        {totalCount}
                      </div>
                    )}
                  </div>
                  
                  {/* Modern istatistik grid */}
                  <div className="grid grid-cols-2 gap-1.5">
                    {(Object.keys(ANNOTATION_TYPES) as Array<keyof typeof ANNOTATION_TYPES>).map((type) => {
                      const config = ANNOTATION_TYPES[type];
                      const count = annotationsByType[type].length;
                      const Icon = config.icon;
                      
                      return (
                        <div 
                          key={type} 
                          className="flex items-center gap-1.5 p-1.5 rounded-lg transition-colors hover:bg-[var(--text-color)]/5"
                          style={{ backgroundColor: cardBgColor }}
                        >
                          <Icon size={12} style={{ color: 'var(--text-color)', opacity: 0.7 }} />
                          <span className="text-xs font-bold" style={{ color: 'var(--text-color)' }}>
                            {count}
                          </span>
                          <span className="text-xs" style={{ color: 'var(--text-color)' }}>
                            {config.title.replace('larım', '').replace('lerim', '')}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
                
                {/* Modern footer ipucu */}
                <div className="px-3 pb-3">
                  <div 
                    className="text-xs text-center py-1.5 rounded-lg border border-dashed opacity-60"
                    style={{ 
                      color: 'var(--text-color)',
                      borderColor: 'color-mix(in srgb, var(--text-color) 30%, transparent)',
                      backgroundColor: listAreaBgColor
                    }}
                  >
                    {totalCount > 0 ? '↗ Detay için dokun' : 'Henüz içerik yok'}
                  </div>
                </div>
              </>
            )}
          </div>
        ) : (
          // Mobil ve Desktop için farklı genişletilmiş görünüm
          <div
            className={`overflow-hidden ${isMobile ? 'shadow-2xl border-t-2' : 'backdrop-blur-xl rounded-2xl shadow-2xl border'}`}
            style={{ 
              backgroundColor: isMobile ? panelBgColor : `${panelBgColor}f0`,
              borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)',
              maxHeight: isMobile ? '90vh' : '90vh',
              animation: 'slideIn 0.4s ease-out'
            }}
          >
            {isMobile && (
              // Mobil: Ultra minimal handle
              <div className="flex justify-center py-1">
                <div 
                  className="w-8 h-0.5 rounded-full opacity-30"
                  style={{ backgroundColor: 'var(--text-color)' }}
                />
              </div>
            )}
            
            {/* Header */}
            <div 
              className={`backdrop-blur-sm ${isMobile ? 'p-2 px-4 border-b' : 'p-4'}`}
              style={{ 
                backgroundColor: headerBgColor,
                borderColor: isMobile ? 'color-mix(in srgb, var(--text-color) 10%, transparent)' : 'transparent'
              }}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div>
                    <h3 className={`font-bold ${isMobile ? 'text-sm' : 'text-base'}`} style={{ color: 'var(--text-color)' }}>
                      {isMobile ? 'İçeriklerim' : 'Bu Sayfadaki İçeriklerim'}
                    </h3>
                    <p className="text-xs opacity-60" style={{ color: 'var(--text-color)' }}>
                      {totalCount} öğe{!isMobile && ' • Sayfadaki tüm notlarınız'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-1">
                  {!isMobile && (
                    <ModernButton
                      onClick={toggleAllVisibility}
                      title={allVisible ? 'Tümünü Gizle' : 'Tümünü Göster'}
                      size="sm"
                      active={allVisible}
                    >
                      {allVisible ? <EyeOff size={14} /> : <Eye size={14} />}
                    </ModernButton>
                  )}
                  
                  <ModernButton
                    onClick={() => setIsExpanded(false)}
                    title="Daralt"
                    size="sm"
                  >
                    <ChevronDown size={16} />
                  </ModernButton>
                </div>
              </div>
            </div>

            {/* Modern içerik alanı */}
            <div 
              className="overflow-y-auto p-4 space-y-3"
              style={{ 
                backgroundColor: listAreaBgColor,
                maxHeight: '70vh'
              }}
            >
              {(Object.keys(ANNOTATION_TYPES) as Array<keyof typeof ANNOTATION_TYPES>).map((type) => (
                <ModernSection
                  key={type}
                  type={type}
                  annotations={annotationsByType[type]}
                  isExpanded={sectionStates[type]}
                  setIsExpanded={(expanded) => updateSectionState(type, expanded)}
                  isVisible={visibilityStates[type]}
                  setIsVisible={visibilitySetters[type]}
                  onAnnotationClick={handleAnnotationClick}
                  user={user}
                  onUnauthenticated={handleUnauthenticated}
                />
              ))}
            </div>

            {/* Modern footer aksiyon */}
            <div 
              className="p-4 backdrop-blur-sm border-t"
              style={{ 
                backgroundColor: headerBgColor,
                borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
              }}
            >
              <button
                className="w-full py-3 px-4 rounded-xl transition-all duration-200 hover:scale-105 active:scale-95 border hover:shadow-md"
                style={{ 
                  color: 'var(--text-color)',
                  borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)',
                  backgroundColor: 'transparent'
                }}
                onClick={() => {
                  if (!user) {
                    alert('Bu özelliği kullanmak için üye olmanız gerekiyor.');
                    return;
                  }
                  navigate('/saved-content');
                }}
              >
                <div className="flex items-center justify-center gap-2">
                  <Archive size={16} />
                  <span className="text-sm font-medium">
                    Tüm İçeriklerimi Görüntüle
                  </span>
                </div>
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
