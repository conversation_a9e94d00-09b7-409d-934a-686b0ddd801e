import { Bug, Info } from 'lucide-react';
import { supabase } from '@shared/utils/supabaseClient';

interface AnnotationDebuggerProps {
  bookId?: string;
  sectionId?: string;
  isVisible?: boolean;
  onToggle?: () => void;
  onHighlightingToggle?: (enabled: boolean) => void;
  isHighlightingEnabled?: boolean;
}

/**
 * Annotation Debugger
 * Development modunda annotation sistemini test etmek için basit araçlar
 */
export function AnnotationDebugger({
  bookId,
  sectionId,
  isVisible = false,
  onToggle,
  onHighlightingToggle,
  isHighlightingEnabled = true
}: AnnotationDebuggerProps) {
  const handleLoadAnnotations = async () => {
    try {
      console.log('🔄 Loading annotations manually...');

      const { data, error } = await supabase
        .from('text_annotations')
        .select('*')
        .eq('book_id', bookId || '')
        .eq('section_id', sectionId || '');

      if (error) {
        console.error('❌ Error loading annotations:', error);
        alert(`Annotation yükleme hatası: ${error.message}`);
        return;
      }

      console.log('✅ Annotations loaded:', data);
      alert(`✅ ${data?.length || 0} annotation bulundu!\n\nDetaylar console'da görüntüleniyor.`);
    } catch (error) {
      console.error('❌ Unexpected error:', error);
      alert('Beklenmeyen hata oluştu!');
    }
  };

  const handleShowDebugInfo = async () => {
    console.clear();
    console.log('🐛 Annotation System Debug Info');

    // Environment check
    console.log('🌍 Environment:', process.env.NODE_ENV);
    console.log('🔗 Supabase URL:', import.meta.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing');
    console.log('🔑 Supabase Key:', import.meta.env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');

    // Auth check
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      console.log('🔐 Auth:', user ? '✅ Authenticated' : '❌ Not authenticated');
      if (user) console.log('👤 User ID:', user.id);
      if (authError) console.error('Auth error:', authError);
    } catch (error) {
      console.error('Auth check failed:', error);
    }

    // Database check
    try {
      const { error } = await supabase.from('text_annotations').select('count').limit(1);
      console.log('🗄️ Database:', error ? '❌ Error' : '✅ Connected');
      if (error) console.error('Database error:', error);
    } catch (error) {
      console.error('Database check failed:', error);
    }

    alert('🐛 Debug bilgileri console\'da görüntülendi.\n\nDetaylar için browser console\'unu kontrol edin.');
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 z-40 p-3 bg-purple-600 text-white rounded-full shadow-lg hover:bg-purple-700 transition-colors"
        title="Şerh Debugger'ı Aç"
      >
        <Bug size={20} />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80 bg-white rounded-lg shadow-2xl border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="bg-purple-600 text-white p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bug size={18} />
            <span className="font-semibold">Şerh Debugger</span>
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={handleShowDebugInfo}
              className="p-1 hover:bg-purple-500 rounded transition-colors"
              title="Debug Bilgileri"
            >
              <Info size={16} />
            </button>
            <button
              onClick={onToggle}
              className="p-1 hover:bg-purple-500 rounded transition-colors"
              title="Kapat"
            >
              ×
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-3">
        <div className="text-sm text-gray-600">
          {bookId && sectionId && (
            <div>📖 Kitap: {bookId} | Bölüm: {sectionId}</div>
          )}
        </div>

        <div className="space-y-2">
          <button
            onClick={handleLoadAnnotations}
            className="w-full px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 transition-colors"
            title="Annotation'ları manuel yükle"
          >
            📥 Annotation'ları Test Et
          </button>

          {onHighlightingToggle && (
            <button
              onClick={() => onHighlightingToggle(!isHighlightingEnabled)}
              className={`w-full px-3 py-2 rounded text-sm transition-colors ${
                isHighlightingEnabled
                  ? 'bg-yellow-600 text-white hover:bg-yellow-700'
                  : 'bg-gray-600 text-white hover:bg-gray-700'
              }`}
              title={isHighlightingEnabled ? 'Vurgulamayı Kapat' : 'Vurgulamayı Aç'}
            >
              {isHighlightingEnabled ? '🎨 Vurgulama Açık' : '🚫 Vurgulama Kapalı'}
            </button>
          )}
        </div>

        <div className="text-xs text-gray-500 text-center">
          Console'da detaylı bilgiler görüntülenir
        </div>
      </div>
    </div>
  );
}
