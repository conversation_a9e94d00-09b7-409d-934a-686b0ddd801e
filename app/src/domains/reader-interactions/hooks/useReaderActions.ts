import { useState, useCallback, useEffect } from 'react';
import { useAnnotationManager } from '@domains/reader-interactions/annotations/hooks/useAnnotationManager';
import { useCollectionManager } from '@domains/reader-interactions/bookmarks/hooks/useCollectionManager';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useReaderStore } from '@domains/reader/store/readerstore';
import {
  AnnotationService,
  SaveNoteInput,
  SaveBookmarkInput,
} from '@domains/reader-interactions/services/AnnotationService';
import { EnhancedUnifiedService } from '@domains/reader-interactions/services/EnhancedUnifiedService';
import { Annotation, CreateAnnotationInput, CreateCollectionInput } from '@domains/reader-interactions/shared/types/types';
import { CombinedVerseData, ITranslator } from '@reader/models/types';

interface SheetStates {
  note: {
    isOpen: boolean;
    content: string;
    id?: string; // For editing
    initialContent?: string; // For editing
  };
  notesList: {
    isOpen: boolean;
  };
  bookmark: {
    isOpen: boolean;
  };
  wordDetail: {
    isOpen: boolean;
  };
  noteEdit: {
    isOpen: boolean;
    annotation: Annotation | null;
  };
}

interface ToastState {
  isVisible: boolean;
  message: string;
  type: 'success' | 'error';
}

interface ActionContext {
  verseKey: string | null;
  verseData: CombinedVerseData | undefined;
  surahName?: string;
}

export const useReaderActions = (
  initialVerseKey: string | null,
  initialVerseData: CombinedVerseData | undefined,
  initialSurahName?: string,
  availableTranslators?: ITranslator[],
  selectedTranslators?: string[]
) => {
  const [actionContext, setActionContext] = useState<ActionContext>({
    verseKey: initialVerseKey,
    verseData: initialVerseData,
    surahName: initialSurahName,
  });

  const [sheetStates, setSheetStates] = useState<SheetStates>({
    note: { 
      isOpen: false, 
      content: '',
      id: undefined,
      initialContent: undefined
    },
    notesList: { isOpen: false },
    bookmark: { isOpen: false },
    wordDetail: { isOpen: false },
    noteEdit: { isOpen: false, annotation: null },
  });

  // Hook'un dış prop'larla senkronize olmasını sağlayan etki.
  // Bu, kullanıcı farklı bir ayet seçtiğinde context'in güncel kalmasını sağlar.
  useEffect(() => {
    // Eğer herhangi bir sheet açıksa, dışarıdan gelen güncellemeyi yoksay.
    // Bu, sheet'in kendi context'ini korumasını sağlar (race condition'ı önler).
    const anySheetOpen = Object.values(sheetStates).some(s => s.isOpen);
    if (anySheetOpen) {
      return;
    }

    setActionContext({
      verseKey: initialVerseKey,
      verseData: initialVerseData,
      surahName: initialSurahName,
    });
  }, [initialVerseKey, initialVerseData, initialSurahName, sheetStates]);

  const [toastState, setToastState] = useState<ToastState>({
    isVisible: false,
    message: '',
    type: 'success'
  });

  const { user } = useAuthStore();
  const { createAnnotation, getAnnotations, updateAnnotation } = useAnnotationManager();
  const { collections, loadCollections, createCollection } = useCollectionManager();
  const { openWordDetailSheet } = useReaderStore();

  // Initialize the service with the createAnnotation and getAnnotations functions
  useEffect(() => {
    AnnotationService.initialize(createAnnotation, getAnnotations);
  }, [createAnnotation, getAnnotations]);

  // Load collections when user changes
  useEffect(() => {
    if (user) {
      loadCollections();
    }
  }, [user, loadCollections]);

  // Toast functions
  const showToast = useCallback((message: string, type: 'success' | 'error' = 'success') => {
    setToastState({ isVisible: true, message, type });
  }, []);

  const hideToast = useCallback(() => {
    setToastState(prev => ({ ...prev, isVisible: false }));
  }, []);

  // Sheet control functions
  const openSheet = useCallback((type: keyof SheetStates) => {
    setSheetStates(prev => ({ ...prev, [type]: { ...prev[type], isOpen: true } }));
  }, []);

  const closeSheet = useCallback((type: keyof SheetStates) => {
    setSheetStates(prev => ({ ...prev, [type]: { ...prev[type], isOpen: false } }));
  }, []);

  // Action handlers
  const handleNoteSave = useCallback(async (input: CreateAnnotationInput) => {
    const { verseKey, verseData, surahName } = actionContext;
    const { id: noteId } = sheetStates.note;

    if (!user || !verseData) {
      showToast('Kullanıcı veya ayet bilgileri eksik.', 'error');
      return;
    }

    // Eğer ID varsa, güncelleme yap
    if (noteId) {
      const result = await updateAnnotation(noteId, {
        annotation_content: input.annotation_content,
        is_public: input.is_public,
      });
      if (result) {
        showToast('Not başarıyla güncellendi.', 'success');
      } else {
        showToast('Not güncellenirken hata oluştu.', 'error');
      }
    }
    // ID yoksa, yeni not oluştur - EnhancedUnifiedService ile
    else if (verseKey) {
      try {
        // Enhanced structured content oluştur (Not için)
        const structuredContent = EnhancedUnifiedService.createQuranContent(
          verseData,
          [], // Not için meal seçimi yok, sadece referans
          availableTranslators || [],
          verseKey,
          surahName || `Sure ${verseKey.split('-')[0]}`
        );

        const [surahId] = verseKey.split('-');

        const result = await EnhancedUnifiedService.saveAnnotation({
          user_id: user.id,
          content_type: 'quran',
          book_id: 'quran',
          section_id: surahId,
          annotation_type: 'note',
          structured_content: structuredContent,
          annotation_content: input.annotation_content,
          is_public: input.is_public
        });

        if (result.success) {
          showToast(result.message, 'success');
        } else {
          showToast(result.message, 'error');
        }
      } catch (error) {
        console.error('🔖 Note save error:', error);
        showToast('Beklenmeyen bir hata oluştu.', 'error');
      }
    }

    closeSheet('note');
  }, [user, actionContext, sheetStates.note, availableTranslators, showToast, closeSheet, updateAnnotation]);

  const handleBookmarkSave = useCallback(async (collection: { id: string; name: string } & { color?: string }, selectedMeals?: Array<{ id: string; enabled: boolean; name: string }>) => {
    const { verseKey, verseData, surahName } = actionContext;

    if (!user || !verseKey || !verseData) {
      showToast('Kullanıcı veya ayet bilgileri eksik.', 'error');
      return;
    }

    console.log('🔖 Bookmark save started:', { verseKey, surahName, selectedMeals, collection });

    try {
      // Enhanced structured content oluştur (Kuran için)
      const structuredContent = EnhancedUnifiedService.createQuranContent(
        verseData,
        selectedMeals || [],
        availableTranslators || [],
        verseKey,
        surahName || `Sure ${verseKey.split('-')[0]}`
      );

      console.log('🎯 Created enhanced structured content:', {
        content_parts: structuredContent.content_parts.length,
        selected_parts: structuredContent.selection_info.selected_parts,
        word_count: structuredContent.selection_info.word_count,
        combined_text_length: structuredContent.selection_info.combined_text.length
      });

      const [surahId] = verseKey.split('-');

      const result = await EnhancedUnifiedService.saveAnnotation({
        user_id: user.id,
        content_type: 'quran',
        book_id: 'quran',
        section_id: surahId,
        annotation_type: 'bookmark',
        structured_content: structuredContent,
        collection_id: collection.id
      });

      console.log('🔖 Bookmark save result:', result);

      if (result.success) {
        closeSheet('bookmark');
        showToast(result.message, 'success');
      } else {
        showToast(result.message, 'error');
      }
    } catch (error) {
      console.error('🔖 Bookmark save error:', error);
      showToast('Beklenmeyen bir hata oluştu.', 'error');
    }
  }, [user, actionContext, availableTranslators, selectedTranslators, showToast, closeSheet]);

  const handleCreateCollection = useCallback(async (collectionInput: Omit<CreateCollectionInput, 'user_id'>) => {
    if (!user) return;
    try {
      await createCollection(collectionInput);
      await loadCollections();
    } catch (error) {
      console.error('Collection creation error:', error);
      throw error;
    }
  }, [user, createCollection, loadCollections]);

  const handleWordDetailClick = useCallback(() => {
    const { verseKey, verseData } = actionContext;
    if (!verseData || !verseKey) {
      showToast('Ayet bilgileri eksik.', 'error');
      return;
    }
    openWordDetailSheet(verseKey);
  }, [actionContext, openWordDetailSheet, showToast]);

  // User validation functions
  const validateUser = useCallback((actionName: string) => {
    if (!user) {
      showToast(`${actionName} için giriş yapmanız gerekiyor.`, 'error');
      return false;
    }
    return true;
  }, [user, showToast]);

  const validateVerseData = useCallback(() => {
    const { verseKey, verseData } = actionContext;
    if (!verseData || !verseKey) {
      showToast('Ayet bilgileri eksik.', 'error');
      return false;
    }
    return true;
  }, [actionContext, showToast]);

  // Main action handlers that accept parameters
  const handleNoteAction = useCallback((actionVerseKey: string, actionVerseData: CombinedVerseData, actionSurahName?: string) => {
    if (!validateUser('Not eklemek')) return;
    
    setActionContext({
      verseKey: actionVerseKey,
      verseData: actionVerseData,
      surahName: actionSurahName,
    });

    // Reset editing state when adding a new note
    setSheetStates(prev => ({
      ...prev,
      note: { isOpen: true, content: '', id: undefined, initialContent: undefined }
    }));
  }, [validateUser]);

  const handleNoteEditAction = useCallback((annotation: Annotation) => {
    if (!validateUser('Not düzenlemek')) return;
    closeSheet('notesList');
    setSheetStates(prev => ({
      ...prev,
      noteEdit: {
        isOpen: true,
        annotation: annotation,
      }
    }));
  }, [validateUser, closeSheet]);

  const closeNoteEditModal = useCallback(() => {
    setSheetStates(prev => ({ ...prev, noteEdit: { isOpen: false, annotation: null } }));
  }, []);

  const onNoteUpdated = useCallback((updatedAnnotation: Annotation) => {
    // Notu güncelledikten sonra modalı kapat ve başarı mesajı göster.
    // Not listesi bir sonraki açılışında güncel veriyi yeniden çekecektir.
    closeNoteEditModal();
    showToast('Not başarıyla güncellendi.', 'success');
  }, [closeNoteEditModal, showToast]);

  const handleNotesListAction = useCallback((actionVerseKey: string, actionVerseData: CombinedVerseData, actionSurahName?: string) => {
    if (!validateUser('Notları görüntülemek')) return;
    
    setActionContext({
      verseKey: actionVerseKey,
      verseData: actionVerseData,
      surahName: actionSurahName,
    });

    openSheet('notesList');
  }, [validateUser, openSheet]);

  const handleBookmarkAction = useCallback((actionVerseKey: string, actionVerseData: CombinedVerseData, actionSurahName?: string) => {
    if (!validateUser('Yer imi eklemek')) return;

    setActionContext({
      verseKey: actionVerseKey,
      verseData: actionVerseData,
      surahName: actionSurahName,
    });
    
    openSheet('bookmark');
  }, [validateUser, openSheet]);

  const handleWordDetailAction = useCallback(() => {
    if (!validateVerseData()) return;
    handleWordDetailClick();
  }, [validateVerseData, handleWordDetailClick]);

  return {
    // States
    sheetStates,
    toastState,
    collections,
    actionContext,

    // Sheet controls
    openSheet,
    closeSheet,

    // Toast controls
    showToast,
    hideToast,

    // Action handlers
    handleNoteAction,
    handleNotesListAction,
    handleBookmarkAction,
    handleWordDetailAction,
    handleNoteSave,
    handleBookmarkSave,
    handleCreateCollection,
    handleNoteEditAction,
    onNoteUpdated,
    closeNoteEditModal,

    // Validation helpers
    validateUser,
    validateVerseData
  };
};
