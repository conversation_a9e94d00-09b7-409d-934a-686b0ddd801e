# 🚀 Reader Interactions Setup Guide

Bu rehber, gelişmiş annotation sistemini kurmanız için kapsamlı adımları içerir. Sistem çoklu sentence desteği, akıllı arama ve hibrit UI ile gelir.

## � Sistem Gereksinimleri

### ✅ **Minimum Gereksinimler**
- **Node.js** 18+
- **TypeScript** 4.9+
- **React** 18+
- **Supabase** Account
- **Modern Browser** (Chrome 90+, Firefox 88+, Safari 14+)

### 🔧 **Bağımlılıklar**
```json
{
  "zustand": "^4.4.0",
  "@supabase/supabase-js": "^2.38.0",
  "lucide-react": "^0.263.1"
}
```

## 🔧 Çözüm Adımları

### 1. Supabase Environment Variables

`app/.env.local` dosyasına Supabase bilgilerinizi ekleyin:

```bash
# Supabase Dashboard'dan alın
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here

# Not: Worker i e7in ayr31 VITE_ de315fkeni gerekmiyor (ayn31 origin /api/r2 kullan31l31yor)
```

**Supabase bilgilerini nereden alacağınız:**
1. [Supabase Dashboard](https://app.supabase.com) → Projenizi seçin
2. Settings → API → Project URL ve anon public key'i kopyalayın

### 2. Supabase Tablosunu Oluşturun

Supabase SQL Editor'da şu script'i çalıştırın:

```sql
-- Ana text annotation tablosu
CREATE TABLE IF NOT EXISTS public.text_annotations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,

  -- Metin referansı
  book_id text NOT NULL,
  section_id text NOT NULL,
  sentence_id text NOT NULL,

  -- Pozisyon bilgileri
  selection_start integer NOT NULL,
  selection_end integer NOT NULL,
  selected_text text NOT NULL,

  -- Recovery sistemi
  prefix_text text NOT NULL,
  suffix_text text NOT NULL,
  word_proximity text[] NOT NULL DEFAULT '{}',
  text_hash text NOT NULL,
  sentence_hash text NOT NULL,

  -- Annotation içeriği
  annotation_type text NOT NULL DEFAULT 'note',
  annotation_content text,

  -- Görsel özellikler
  color text DEFAULT '#fbbf24',
  opacity real DEFAULT 0.3,

  -- Sosyal & organizasyon
  is_public boolean DEFAULT false,
  tags text[] DEFAULT '{}',
  category text DEFAULT 'personal',

  -- Metadata
  metadata jsonb DEFAULT '{}',

  -- Timestamps
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,

  -- Constraints
  CONSTRAINT valid_annotation_type CHECK (annotation_type IN ('note', 'highlight', 'bookmark')),
  CONSTRAINT valid_category CHECK (category IN ('personal', 'academic', 'spiritual', 'other')),
  CONSTRAINT valid_selection CHECK (selection_start >= 0 AND selection_end > selection_start),
  CONSTRAINT valid_opacity CHECK (opacity BETWEEN 0.1 AND 1.0),
  CONSTRAINT note_requires_content CHECK (
    annotation_type != 'note' OR
    (annotation_content IS NOT NULL AND length(trim(annotation_content)) > 0)
  )
);

-- RLS (Row Level Security) Policies
ALTER TABLE public.text_annotations ENABLE ROW LEVEL SECURITY;

-- Kullanıcılar kendi annotation'larını görebilir
CREATE POLICY "Users can view own text annotations" ON public.text_annotations
  FOR SELECT USING (auth.uid() = user_id);

-- Kullanıcılar public annotation'ları görebilir
CREATE POLICY "Users can view public text annotations" ON public.text_annotations
  FOR SELECT USING (is_public = true);

-- Kullanıcılar kendi annotation'larını oluşturabilir
CREATE POLICY "Users can create own text annotations" ON public.text_annotations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Kullanıcılar kendi annotation'larını güncelleyebilir
CREATE POLICY "Users can update own text annotations" ON public.text_annotations
  FOR UPDATE USING (auth.uid() = user_id);

-- Kullanıcılar kendi annotation'larını silebilir
CREATE POLICY "Users can delete own text annotations" ON public.text_annotations
  FOR DELETE USING (auth.uid() = user_id);
```

### 3. Development Server'ı Yeniden Başlatın

Environment variables değişikliği sonrası:

```bash
# Development server'ı durdurun (Ctrl+C)
# Sonra tekrar başlatın
npm run dev
```

### 4. Test Edin

1. Uygulamayı açın
2. Giriş yapın (annotation sistemi authentication gerektirir)
3. Bir metni seçin
4. Sağ alt köşede mor bug ikonuna tıklayın
5. Info butonuna (ℹ️) tıklayarak debug bilgilerini kontrol edin

## 🐛 Debug Araçları

Development modunda sağ alt köşede **mor bug ikonu** görünür. Bu debugger ile:

### Debug Kontrolleri:
- **ℹ️ Info**: Sistem durumunu kontrol et
- **🎨 Paintbrush**: Annotation'ları metinde göster
- **👁️ Eye**: Detaylı görünümü aç/kapat
- **📍 MapPin**: Annotation'ı metinde bul
- **🗑️ Trash**: Test annotation'larını sil

### Console Komutları:
Debug butonuna tıkladıktan sonra console'da şu komutlar kullanılabilir:

```javascript
// SQL script'ini göster ve clipboard'a kopyala
showTableCreationScript()

// Sorun giderme rehberini göster
showTroubleshootingGuide()

// Debug bilgilerini tekrar logla
logDebugInfo()
```

## ✅ Başarılı Kurulum Kontrolü

Debug info'da şunları görmelisiniz:
- 🌍 Environment: development ✅
- 🔗 Supabase: ✅
- 🗄️ Database: ✅
- 📋 Table: ✅
- 🔐 Auth: ✅ (giriş yaptıysanız)

## 🚨 Sık Karşılaşılan Sorunlar

### NetworkError
- Environment variables eksik
- Supabase URL/key yanlış
- İnternet bağlantısı yok

### Table does not exist
- SQL script çalıştırılmamış
- Tablo adı yanlış
- RLS policies eksik

### Authentication Required
- Kullanıcı giriş yapmamış
- Session süresi dolmuş
- Auth policies yanlış

## 📞 Destek

Sorun devam ederse:
1. Debug info'yu kontrol edin
2. Console'da hata mesajlarını inceleyin
3. Network tab'ında Supabase isteklerini kontrol edin
4. Supabase dashboard'da logs'ları inceleyin

## 🎉 Kurulum Tamamlandıktan Sonra

Annotation sistemi çalışmaya başladığında:
- Metinleri seçebilirsiniz
- Şerh, vurgulama, yer imi ekleyebilirsiniz
- Debugger ile annotation'ları görüntüleyebilirsiniz
- Metinde gerçek zamanlı görselleştirme yapabilirsiniz
