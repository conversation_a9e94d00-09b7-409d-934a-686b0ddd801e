import React, { useState, useEffect, useMemo, useRef } from 'react';
import { createPortal } from 'react-dom';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';
import { 
  X, Search, 
  BookOpen, Folder, Tag, RotateCcw, ChevronRight
} from 'lucide-react';

// Types - inspired by TranslationSelectorSheet
export interface CategoryFilterOption {
  id: string;
  label: string;
  count: number;
}

export interface BookFilterOption {
  id: string;
  label: string;
  author?: string;
  count: number;
  categoryId: number;
}

export interface CollectionFilterOption {
  id: string;
  label: string;
  count: number;
}

interface FilterSheetProps {
  isOpen: boolean;
  onClose: () => void;
  categoryFilters: CategoryFilterOption[];
  bookFilters: BookFilterOption[];
  sectionFilters?: { id: string; label: string; count: number }[];
  collectionFilters: CollectionFilterOption[];
  selectedCategories: string[];
  selectedBooks: string[];
  selectedSections?: string[];
  selectedCollections: string[];
  onCategoryChange: (categories: string[]) => void;
  onBookChange: (books: string[]) => void;
  onSectionChange?: (sections: string[]) => void;
  onCollectionChange: (collections: string[]) => void;
  onReset: () => void;
  showCollections?: boolean;
  anchorRef?: React.RefObject<HTMLElement | null>; // Masaüstünde butonun altına hizalama
}

export const NewFilterSheet: React.FC<FilterSheetProps> = ({
  isOpen,
  onClose,
  categoryFilters,
  bookFilters,
  sectionFilters = [],
  collectionFilters,
  selectedCategories,
  selectedBooks,
  selectedSections = [],
  selectedCollections,
  onCategoryChange,
  onBookChange,
  onSectionChange,
  onCollectionChange,
  onReset,
  showCollections = false,
  anchorRef
}) => {
  // All hooks must be called unconditionally
  const sheetRef = useRef<HTMLDivElement>(null);
  // Başlangıçta tüm bölümler kapalı (daha minimal görünüm)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [bookSearch, setBookSearch] = useState('');
  
  // Styling hooks - inspired by ContentModeSelector and TranslationSelectorSheet
  const popoverBg = useAutoOverlay(8, 'var(--bg-color)');
  const listAreaBgColor = useAutoOverlay(4, 'var(--bg-color)');
  const [desktopCoords, setDesktopCoords] = useState<{ top: number; left: number; width: number } | null>(null);

  // Masaüstü konum hesaplama
  useEffect(() => {
    if (!isOpen) return;
    const update = () => {
      if (anchorRef?.current && window.innerWidth >= 768) {
        const rect = anchorRef.current.getBoundingClientRect();
        const desiredWidth = 360; // px
        const gap = 8;
        let left = rect.left;
        // Sağ taşmayı engelle
        if (left + desiredWidth + 8 > window.innerWidth) {
          left = Math.max(8, window.innerWidth - desiredWidth - 8);
        }
        setDesktopCoords({ top: rect.bottom + gap, left, width: desiredWidth });
      } else {
        setDesktopCoords(null);
      }
    };
    update();
    window.addEventListener('resize', update);
    window.addEventListener('scroll', update, true);
    return () => {
      window.removeEventListener('resize', update);
      window.removeEventListener('scroll', update, true);
    };
  }, [isOpen, anchorRef]);

  // Handle clicks outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sheetRef.current && !sheetRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Memoized values
  const filteredBooks = useMemo(() => {
    if (!bookSearch.trim()) return bookFilters;
    return filterByNormalizedQuery(bookFilters, bookSearch, (book) => [
      book.label,
      book.author || ''
    ]);
  }, [bookFilters, bookSearch]);

  // Early return after all hooks are called
  if (!isOpen) return null;

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  const toggleSelection = (items: string[], setItems: (items: string[]) => void, itemId: string) => {
    const newSelection = items.includes(itemId)
      ? items.filter(id => id !== itemId)
      : [...items, itemId];
    setItems(newSelection);
  };

  const hasActiveFilters = selectedCategories.length > 0 || selectedBooks.length > 0 || selectedSections.length > 0 || selectedCollections.length > 0;

  // Render filter section
  const renderFilterSection = (
    title: string,
    icon: React.ElementType,
    sectionKey: string,
    items: any[],
    selectedItems: string[],
    onToggle: (itemId: string) => void,
    showSearch = false
  ) => {
    const isExpanded = expandedSections.has(sectionKey);
    const Icon = icon;

    return (
      <div className="space-y-2">
        {/* Section Header */}
        <button
          onClick={() => toggleSection(sectionKey)}
          className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-[var(--text-color)]/6 transition-colors border border-transparent focus:outline-none focus:border-[var(--color-primary)]/50"
          style={{ backgroundColor: listAreaBgColor }}
        >
          <div className="flex items-center gap-2">
            <Icon size={16} className="text-[var(--color-primary)]" />
            <span className="font-medium text-sm text-[var(--text-color)]">{title}</span>
            <span className="text-xs bg-[var(--text-color)]/10 px-1.5 py-0.5 rounded-full">
              {selectedItems.length}/{items.length}
            </span>
          </div>
          <ChevronRight 
            size={16} 
            className={`text-[var(--text-color)]/60 transition-transform duration-200 ${
              isExpanded ? 'rotate-90' : ''
            }`} 
          />
        </button>
        {/* Section Content (animasyonlu) */}
        <div
          className={`transition-all duration-300 origin-top ${
            isExpanded ? 'max-h-64 opacity-100 scale-100 mt-1' : 'max-h-0 opacity-0 scale-[0.98] pointer-events-none'
          }`}
        >
          <div className="space-y-2">
            {showSearch && (
              <div className="relative">
                <input
                  type="text"
                  value={bookSearch}
                  onChange={(e) => setBookSearch(e.target.value)}
                  placeholder="Kitap ara..."
                  className="w-full px-3 py-2 pl-9 rounded-lg text-sm text-[var(--text-color)]/90 placeholder:text-[var(--text-color)]/30 focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors duration-200"
                  style={{ backgroundColor: listAreaBgColor }}
                />
                <Search
                  size={14}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-[var(--text-color)] opacity-50"
                />
              </div>
            )}
            <div
              className="space-y-1 p-2 rounded-lg max-h-48 overflow-y-auto"
              style={{ backgroundColor: listAreaBgColor }}
            >
              {items.map((item) => {
                const isSelected = selectedItems.includes(item.id);
                return (
                  <button
                    key={item.id}
                    onClick={() => onToggle(item.id)}
                    className="w-full text-left text-sm flex items-center justify-start relative"
                  >
                    <div
                      className={`w-full flex items-center justify-between rounded-lg px-3 transition-colors duration-200 text-[var(--text-color)] ${
                        isSelected ? 'bg-[var(--bg-color)] shadow py-1.5' : 'py-2 hover:bg-[var(--text-color)]/5'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-5 h-5 rounded border flex items-center justify-center transition-colors duration-150 ${
                            isSelected
                              ? 'bg-[var(--text-color)] border-[var(--text-color)]'
                              : 'border-[var(--text-color)]/80'
                          }`}
                        >
                          {isSelected && (
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" style={{ color: 'var(--bg-color)' }}>
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg>
                          )}
                        </div>
                        <div className={`truncate ${isSelected ? 'font-bold' : 'font-normal'}`}>
                          {item.label}
                        </div>
                      </div>
                      <span className="text-xs bg-[var(--text-color)]/10 px-1.5 py-0.5 rounded-full ml-2">{item.count}</span>
                    </div>
                  </button>
                );
              })}
              {items.length === 0 && (
                <div className="h-16 flex items-center justify-center text-sm text-[var(--text-color)] opacity-60">
                  {showSearch && bookSearch ? 'Sonuç bulunamadı' : 'Öğe bulunamadı'}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const isDesktop = typeof window !== 'undefined' && window.innerWidth >= 768;

  const sheet = (
    <div
      ref={sheetRef}
      className={`fixed z-[9999] flex flex-col overflow-hidden focus:outline-none shadow-2xl ${
        isDesktop
          ? 'rounded-xl border max-h-[70vh]' // desktop popover
          : 'bottom-0 left-0 right-0 max-h-[85vh] rounded-t-2xl border-t'
      }`}
      style={{
        backgroundColor: popoverBg,
        borderColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)',
        ...(isDesktop && desktopCoords
          ? { top: desktopCoords.top, left: desktopCoords.left, width: desktopCoords.width }
          : !isDesktop
            ? { bottom: 0, left: 0, right: 0 }
            : {})
      }}
      role="dialog"
      aria-modal="true"
      aria-label="Filtreler"
      tabIndex={-1}
    >
      <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15 flex-shrink-0" />
      <div className="mx-4 mt-2 md:mt-4 mb-1 flex items-center justify-between">
        <h3 className="text-base font-medium text-[var(--text-color)]">Filtreler</h3>
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <button
              onClick={onReset}
              className="flex items-center gap-1 px-2 py-1 rounded-lg hover:bg-[var(--text-color)]/5 text-[var(--text-color)]/70 text-xs"
            >
              <RotateCcw size={12} />
              Temizle
            </button>
          )}
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-xl hover:bg-[var(--text-color)]/5 text-[var(--text-color)]"
            aria-label="Kapat"
          >
            <X size={18} />
          </button>
        </div>
      </div>
      <div className="px-4 pt-2 pb-3 overflow-y-auto space-y-3">
        {renderFilterSection(
          'Kategoriler',
          Tag,
          'categories',
          categoryFilters,
          selectedCategories,
          (itemId) => toggleSelection(selectedCategories, onCategoryChange, itemId)
        )}
        {renderFilterSection(
          'Kitaplar',
          BookOpen,
          'books',
          filteredBooks,
          selectedBooks,
          (itemId) => toggleSelection(selectedBooks, onBookChange, itemId),
          true
        )}
        {sectionFilters.length > 0 && renderFilterSection(
          'Bölümler',
          BookOpen,
          'sections',
          sectionFilters,
          selectedSections,
          (itemId) => onSectionChange && toggleSelection(selectedSections, onSectionChange, itemId)
        )}
        {(showCollections || collectionFilters.length > 0) && renderFilterSection(
          'Koleksiyonlar',
          Folder,
          'collections',
          collectionFilters,
          selectedCollections,
          (itemId) => toggleSelection(selectedCollections, onCollectionChange, itemId)
        )}
      </div>
    </div>
  );

  return createPortal(
    <>
      <div
        className="fixed inset-0 bg-black/10 backdrop-blur-[1px] z-[9998] transition-opacity"
        onClick={onClose}
      />
      {sheet}
    </>,
    document.body
  );
};
