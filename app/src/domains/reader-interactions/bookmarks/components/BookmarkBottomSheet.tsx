import React, { useState, useEffect } from 'react';
import { X, Bookmark, FolderPlus } from 'lucide-react';
import BookmarkIcon from '@shared/components/Icons/BookmarkIcon';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
// ✅ YENİ ORGANİZE YAPISI - Shared types
import type { BookmarkCollection, CreateCollectionInput } from '../../shared/types';
import CompactBgColorPicker from '@shared/color/components/CompactBgColorPicker';

// ✅ Meal seçimi için yeni tipler
interface MealOption {
  id: string;
  name: string;
  enabled: boolean;
}

interface BookmarkBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  selectedText: string;
  collections: BookmarkCollection[];
  onSelectCollection: (collection: BookmarkCollection, selectedMeals?: MealOption[]) => void;
  onCreateCollection: (input: Omit<CreateCollectionInput, 'user_id'>) => Promise<void>;
  loading?: boolean;
  // ✅ Quran ayetleri için meal seçimi
  availableTranslators?: Array<{ id: string; name: string }>;
  selectedTranslators?: string[];
  isQuranVerse?: boolean;
}

export const BookmarkBottomSheet: React.FC<BookmarkBottomSheetProps> = ({
  isOpen,
  onClose,
  selectedText,
  collections,
  onSelectCollection,
  onCreateCollection,
  loading = false,
  availableTranslators = [],
  selectedTranslators = [],
  isQuranVerse = false
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [newCollectionDescription, setNewCollectionDescription] = useState('');
  const [newCollectionColor, setNewCollectionColor] = useState('#3b82f6');
  const [newCollectionIcon, setNewCollectionIcon] = useState('bookmark');
  const [creating, setCreating] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState<BookmarkCollection | null>(null);
  const [selectedCollectionIds, setSelectedCollectionIds] = useState<Set<string>>(new Set());

  // ✅ Meal seçimi state'i
  const [mealOptions, setMealOptions] = useState<MealOption[]>([]);
  const [showMealSelection, setShowMealSelection] = useState(false);
  const [mealSelectionVisited, setMealSelectionVisited] = useState(false);

  // ✅ Meal options'ları initialize et
  useEffect(() => {
    if (isQuranVerse && availableTranslators.length > 0) {
      const options: MealOption[] = [
        { id: 'arabic', name: 'Arapça Metin', enabled: true }, // Varsayılan olarak seçili
        ...availableTranslators.map(translator => ({
          id: translator.id,
          name: translator.name,
          enabled: selectedTranslators.includes(translator.id) // Mevcut seçili mealler
        }))
      ];
      setMealOptions(options);
    }
  }, [isQuranVerse, availableTranslators, selectedTranslators]);

  // Reset form when sheet closes
  useEffect(() => {
    if (!isOpen) {
      setShowCreateForm(false);
      setShowMealSelection(false);
      setMealSelectionVisited(false);
      setSelectedCollectionIds(new Set());
      setNewCollectionName('');
      setNewCollectionDescription('');
      setNewCollectionColor('#3b82f6');
      setNewCollectionIcon('bookmark');
    }
  }, [isOpen]);

  // Handle collection creation
  const handleCreateCollection = async () => {
    if (!newCollectionName.trim()) return;

    setCreating(true);
    try {
      await onCreateCollection({
        name: newCollectionName.trim(),
        description: newCollectionDescription.trim() || undefined,
        color: newCollectionColor,
        icon: newCollectionIcon
      });

      // Reset form
      setShowCreateForm(false);
      setNewCollectionName('');
      setNewCollectionDescription('');
    } catch (error) {
      console.error('Failed to create collection:', error);
    } finally {
      setCreating(false);
    }
  };

  // ✅ Meal seçimi toggle fonksiyonu
  const toggleMealOption = (mealId: string) => {
    setMealOptions(prev =>
      prev.map(option =>
        option.id === mealId
          ? { ...option, enabled: !option.enabled }
          : option
      )
    );
  };

  // ✅ Koleksiyon seçimi handler'ı - meal seçimi ile birlikte
  const [selectedBgIdentifier, setSelectedBgIdentifier] = useState<string | undefined>(undefined);

  const handleToggleCollection = (collection: BookmarkCollection) => {
    setSelectedCollectionIds(prev => {
      const next = new Set(prev);
      if (next.has(collection.id)) next.delete(collection.id); else next.add(collection.id);
      return next;
    });
  };

  const handleSaveSelected = () => {
    // Quran için en az bir kez içerik seçimi ekranını göster
    if (isQuranVerse && mealOptions.length > 0 && !mealSelectionVisited) {
      setShowMealSelection(true);
      setMealSelectionVisited(true);
      return;
    }
    const selected = collections.filter(c => selectedCollectionIds.has(c.id));
    if (selected.length === 0) return;
    const selectedMeals = (isQuranVerse && mealOptions.length > 0)
      ? mealOptions.filter(option => option.enabled)
      : undefined;
    selected.forEach(col => {
      onSelectCollection({ ...col, color: selectedBgIdentifier } as any, selectedMeals as any);
    });
    setSelectedCollectionIds(new Set());
    onClose();
  };



  const buttonBgColor = useAutoOverlay(5, 'var(--text-color)');
  const buttonTextColor = useAutoOverlay(5, 'var(--bg-color)');
  
  // NoteBottomSheet'e benzer color palette
  const sheetBg = 'var(--bg-color)';
  const borderColor = useAutoOverlay(21, 'var(--bg-color)');
  const cardBg = useAutoOverlay(2, 'var(--bg-color)');

  if (!isOpen) return null;

  return (
    <>
      {/* Premium Backdrop */}
      <div
        className="fixed inset-0 bg-black/20 backdrop-blur-md z-50 transition-all duration-300"
        onClick={onClose}
      />

      {/* Premium Sheet */}
      <div
        className="fixed z-50 bottom-0 left-0 right-0 md:bottom-auto md:top-1/2 md:left-1/2 md:transform md:-translate-x-1/2 md:-translate-y-1/2 md:w-[520px] md:max-h-[90vh] md:rounded-3xl rounded-t-3xl shadow-2xl md:border flex flex-col max-h-[92vh] h-[80vh] md:h-[650px]"
        style={{
          backgroundColor: sheetBg,
          borderColor: borderColor,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15)'
        }}
      >
        {/* Premium Header */}
        <div className="flex items-center gap-3 px-5 py-2" style={{ borderColor: borderColor }}>
          <div 
            className="w-9 h-9 rounded-lg flex items-center justify-center relative overflow-hidden"
            style={{ backgroundColor: 'var(--text-color)/10' }}
          >
            <Bookmark size={16} style={{ color: 'var(--text-color)' }} />
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-lg" />
          </div>
          <div className="flex-1 min-w-0">
            <h2 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
              Yer İmi Ekle
            </h2>
            <p className="text-xs opacity-50 truncate" style={{ color: 'var(--text-color)' }}>
              {selectedText}
            </p>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-red-50 hover:text-red-500 transition-all duration-200 group"
            style={{ color: 'var(--text-color)' }}
          >
            <X size={14} className="group-hover:scale-110 transition-transform" />
          </button>
        </div>

        {/* Premium Content */}
        <div className="flex-1 flex flex-col p-6 min-h-0">

          {!showCreateForm && !showMealSelection ? (
            <div className="flex flex-col gap-4">
              {/* Collection Selection Zone */}
              <div 
                className="flex-1 rounded-2xl p-6 border-2 border-dashed transition-all duration-300 relative min-h-[300px] overflow-y-auto"
                style={{
                  backgroundColor: cardBg,
                  borderColor: borderColor
                }}
              >
                <div className="mb-4">
                  <h4 className="text-sm font-semibold uppercase opacity-50 tracking-wide text-[var(--text-color)]">
                    Koleksiyon Seçin
                  </h4>
                </div>

                {/* Arka Plan Rengi - Yer imi için küçük palet */}
                <div className="mb-3">
                  <label className="block text-xs font-medium mb-2" style={{ color: 'var(--text-color)' }}>
                    Arka Plan Rengi
                  </label>
                  <CompactBgColorPicker
                    selected={selectedBgIdentifier}
                    onSelect={setSelectedBgIdentifier}
                    includeNone
                  />
                </div>

                {loading ? (
                  <div className="flex flex-col items-center justify-center py-12 text-sm opacity-70 text-[var(--text-color)]">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-3"></div>
                    <p>Koleksiyonlar yükleniyor...</p>
                  </div>
                ) : collections.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-sm opacity-70 text-[var(--text-color)]">
                    <div className="p-3 rounded-full mb-3" style={{ backgroundColor: 'var(--text-color)/10' }}>
                      <Bookmark size={24} style={{ color: 'var(--text-color)' }} />
                    </div>
                    <p>Henüz koleksiyonunuz yok</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {collections.map((collection) => {
                      const isSelected = selectedCollectionIds.has(collection.id);
                      return (
                        <button
                          key={collection.id}
                          onClick={() => handleToggleCollection(collection)}
                          className={`w-full px-4 py-3.5 text-left rounded-xl text-sm flex items-center justify-between relative transition-all group ${isSelected ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/12'} text-[var(--text-color)]`}
                        >
                          <div className="flex-1 text-left">
                            <div className="flex items-center space-x-2">
                              <span className="font-medium break-words">{collection.name}</span>
                              {collection.is_default && (
                                <span className="text-xs px-2 py-1 rounded-full font-medium" style={{ backgroundColor: 'color-mix(in srgb, var(--text-color) 20%, transparent)', color: 'var(--text-color)', opacity: 0.9 }}>Varsayılan</span>
                              )}
                            </div>
                            {collection.description && (
                              <p className="text-xs opacity-75 mt-1 leading-relaxed">{collection.description}</p>
                            )}
                          </div>
                          <div className="ml-3 shrink-0">
                            <BookmarkIcon size={18} color={'var(--text-color)'} filled={isSelected} />
                          </div>
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>

              {/* Premium Bottom Bar */}
              <div className="flex items-center justify-end gap-2" style={{ borderColor: borderColor }}>
                <button
                  onClick={handleSaveSelected}
                  disabled={selectedCollectionIds.size === 0}
                  className="px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow hover:shadow-md"
                  style={{ backgroundColor: buttonBgColor, color: buttonTextColor }}
                >
                  Kaydet
                </button>
                <button
                  onClick={() => setShowCreateForm(true)}
                  className="px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 shadow hover:shadow-md"
                  style={{ backgroundColor: buttonBgColor, color: buttonTextColor }}
                >
                  <FolderPlus size={16} />
                  Yeni Koleksiyon
                </button>
              </div>
            </div>
          ) : showMealSelection ? (
            <div className="flex flex-col gap-4">
              {/* Meal Selection Zone */}
              <div 
                className="flex-1 rounded-2xl p-6 border-2 border-dashed transition-all duration-300 relative min-h-[300px]"
                style={{
                  backgroundColor: cardBg,
                  borderColor: borderColor
                }}
              >
                <div className="mb-4">
                  <h4 className="text-sm font-semibold uppercase opacity-50 tracking-wide text-[var(--text-color)]">
                    Kaydetmek İstediğiniz İçerikleri Seçin
                  </h4>
                </div>

                {/* Meal Options - Scrollable */}
                <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
                  {mealOptions.map((option) => (
                    <label
                      key={option.id}
                      className="flex items-center space-x-3 p-3 rounded-xl hover:bg-[var(--text-color)]/12 cursor-pointer transition-all"
                      style={{ 
                        backgroundColor: 'color-mix(in srgb, var(--text-color) 8%, transparent)'
                      }}
                    >
                      <input
                        type="checkbox"
                        checked={option.enabled}
                        onChange={() => toggleMealOption(option.id)}
                        className="w-4 h-4 rounded border-2 border-[var(--text-color)]/20 text-blue-600 focus:ring-blue-500 focus:ring-2"
                      />
                      <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
                        {option.name}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Premium Bottom Bar */}
              <div className="flex items-center justify-between" style={{ borderColor: borderColor }}>
                <div className="flex items-center gap-2">
                  <div className="text-xs px-2 py-1 rounded-full" style={{ backgroundColor: 'var(--text-color)/15', color: 'var(--text-color)' }}>
                    {mealOptions.filter(option => option.enabled).length} seçili
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setShowMealSelection(false)}
                    className="px-4 py-2 rounded-xl font-medium transition-all duration-200 hover:bg-gray-50"
                    style={{ color: 'var(--text-color)' }}
                  >
                    Geri
                  </button>
                  
                  <button
                    onClick={handleSaveSelected}
                    disabled={!mealOptions.some(option => option.enabled) || selectedCollectionIds.size === 0}
                    className="px-6 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                    style={{
                      backgroundColor: buttonBgColor,
                      color: buttonTextColor,
                      boxShadow: `0 6px 20px color-mix(in srgb, var(--text-color) 30%, transparent)`
                    }}
                  >
                    Kaydet
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col gap-4">
              {/* Create Collection Form Zone */}
              <div 
                className="flex-1 rounded-2xl p-6 border-2 border-dashed transition-all duration-300 relative min-h-[300px]"
                style={{
                  backgroundColor: cardBg,
                  borderColor: borderColor
                }}
              >
                <div className="mb-4">
                  <h4 className="text-sm font-semibold uppercase opacity-50 tracking-wide text-[var(--text-color)]">
                    Yeni Koleksiyon Oluştur
                  </h4>
                </div>

                <div className="space-y-5">
                  {/* Collection Name */}
                  <div>
                    <label className="block text-sm font-medium mb-3" style={{ color: 'var(--text-color)' }}>
                      Koleksiyon Adı *
                    </label>
                    <input
                      type="text"
                      value={newCollectionName}
                      onChange={(e) => setNewCollectionName(e.target.value)}
                      placeholder="Örn: Önemli Ayetler"
                      className="w-full px-4 py-3 rounded-xl text-sm text-[var(--text-color)] placeholder:text-[var(--text-color)] placeholder:opacity-5 [&::placeholder]:opacity-70 focus:outline-none transition-all focus:ring-2 focus:ring-blue-500/30"
                      style={{
                        backgroundColor: 'color-mix(in srgb, var(--text-color) 8%, transparent)'
                      }}
                      maxLength={100}
                    />
                  </div>

                  {/* Collection Description */}
                  <div>
                    <label className="block text-sm font-medium mb-3" style={{ color: 'var(--text-color)' }}>
                      Açıklama (İsteğe bağlı)
                    </label>
                    <textarea
                      value={newCollectionDescription}
                      onChange={(e) => setNewCollectionDescription(e.target.value)}
                      placeholder="Bu koleksiyonun amacını açıklayın..."
                      className="w-full px-4 py-3 rounded-xl text-sm text-[var(--text-color)] placeholder:text-[var(--text-color)] placeholder:opacity-5 [&::placeholder]:opacity-70 resize-none focus:outline-none transition-all focus:ring-2 focus:ring-blue-500/30"
                      style={{
                        backgroundColor: 'color-mix(in srgb, var(--text-color) 8%, transparent)'
                      }}
                      rows={3}
                      maxLength={500}
                    />
                  </div>
                </div>
              </div>

              {/* Premium Bottom Bar */}
              <div className="flex items-center justify-between" style={{ borderColor: borderColor }}>
                <div className="flex items-center gap-2">
                  <div className="text-xs font-medium opacity-40" style={{ color: 'var(--text-color)' }}>
                    {newCollectionName.length}/100
                  </div>
                  <div className="text-xs opacity-40" style={{ color: 'var(--text-color)' }}>
                    karakter
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setShowCreateForm(false)}
                    className="px-4 py-2 rounded-xl font-medium transition-all duration-200 hover:bg-gray-50"
                    style={{ color: 'var(--text-color)' }}
                  >
                    İptal
                  </button>
                  
                  <button
                    onClick={handleCreateCollection}
                    disabled={!newCollectionName.trim() || creating}
                    className="px-6 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 shadow-lg hover:shadow-xl"
                    style={{
                      backgroundColor: buttonBgColor,
                      color: buttonTextColor,
                      boxShadow: `0 6px 20px color-mix(in srgb, var(--text-color) 30%, transparent)`
                    }}
                  >
                    {creating ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        Oluşturuluyor...
                      </>
                    ) : (
                      <>
                        <FolderPlus size={16} />
                        Oluştur
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
