import React from 'react';
import { ArrowUpRight } from 'lucide-react';

interface StructuredContentDisplayProps {
  annotation: {
    selected_text: string;
    structured_content?: any;
  };
  // Quran özel header bilgileri (varsa gösterilecek)
  surahName?: string; // Örn: Bakara
  surahNumber?: number | string; // Örn: 2
  verseNumber?: number | string; // Örn: 1
  onNavigate?: (e: React.MouseEvent) => void; // Yönlendirme ikonu için anchor alınacak
}

export const StructuredContentDisplay: React.FC<StructuredContentDisplayProps> = ({
  annotation,
  surahName,
  surahNumber,
  verseNumber,
  onNavigate
}) => {
  const ARABIC_REGEX = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]/;
  // Basitçe ardışık Arapça karakter kümelerini yakala
  const ARABIC_RUN_REGEX = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]+/g;

  const renderMixedArabic = (text: string) => {
    if (!ARABIC_REGEX.test(text)) return text;
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    let match: RegExpExecArray | null;
    while ((match = ARABIC_RUN_REGEX.exec(text)) !== null) {
      const { index } = match;
      if (index > lastIndex) {
        parts.push(text.slice(lastIndex, index));
      }
      const arabicSegment = match[0];
      parts.push(
        <span
          key={`ar-${index}`}
          dir="rtl"
          className="font-arabic text-[1.3rem] md:text-[1.4rem] text-[var(--color-primary)] mx-1 inline-block align-middle leading-snug"
        >
          {arabicSegment}
        </span>
      );
      lastIndex = index + arabicSegment.length;
    }
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }
    return parts;
  };

  const renderQuranHeader = () => {
    if (!surahName || !verseNumber || !surahNumber) return null;
    return (
      <div className="flex items-center justify-between mb-2 px-0.5">
        <div className="text-xs md:text-sm font-semibold tracking-wide text-[var(--text-color)]/80">
          <span className="text-[var(--text-color)]">{surahName}</span>, {verseNumber}. Ayet <span className="opacity-55 font-normal">({surahNumber}:{verseNumber})</span>
        </div>
    {onNavigate && (
          <button
            type="button"
      onClick={(e) => onNavigate(e)}
            className="inline-flex items-center justify-center w-6 h-6 rounded-md text-[var(--text-color)]/60 hover:text-[var(--color-primary)] hover:bg-[var(--text-color)]/10 transition-colors"
            aria-label="Metne git"
            title="Metne git"
          >
            <ArrowUpRight size={14} />
          </button>
        )}
      </div>
    );
  };

  // Eğer structured_content yoksa, fallback olarak düz metni göster
  if (!annotation.structured_content?.content_parts) {
  const isArabicPlain = ARABIC_REGEX.test(annotation.selected_text);
    return (
      <div
        className="relative mb-3 rounded-lg overflow-hidden"
        style={{
          background: `linear-gradient(145deg, color-mix(in srgb, var(--bg-color) 92%, var(--text-color) 8%), color-mix(in srgb, var(--bg-color) 88%, var(--text-color) 12%))`
        }}
      >
        <div className="absolute inset-0 opacity-[0.35] pointer-events-none" style={{
          background: `radial-gradient(circle at 15% 20%, color-mix(in srgb, var(--text-color) 25%, transparent) 0%, transparent 65%),
                       radial-gradient(circle at 90% 80%, color-mix(in srgb, var(--text-color) 15%, transparent) 0%, transparent 70%)`
        }} />
        <div className="p-3">
          {renderQuranHeader()}
          <blockquote className={`relative text-sm md:text-[15px] italic text-[var(--text-color)]/85 leading-relaxed`}>
            {isArabicPlain ? renderMixedArabic(annotation.selected_text) : annotation.selected_text}
          </blockquote>
        </div>
      </div>
    );
  }

  const selectedParts = annotation.structured_content.content_parts.filter((part: any) => part.selected);

  return (
  <div className="space-y-3 mb-3">
  {selectedParts.map((part: any) => {
        const isArabic = part.type === 'arabic';
        return (
          <div
            key={part.id}
            className="relative rounded-lg overflow-hidden group"
            style={{
              background: `linear-gradient(155deg, color-mix(in srgb, var(--bg-color) 90%, var(--text-color) 10%), color-mix(in srgb, var(--bg-color) 86%, var(--text-color) 14%))`
            }}
          >
            {/* Overlay katmanları */}
            <div
              className="absolute inset-0 opacity-[0.30] mix-blend-overlay pointer-events-none transition-opacity duration-300 group-hover:opacity-[0.42]"
              style={{
                background: `radial-gradient(circle at 12% 18%, color-mix(in srgb, var(--text-color) 22%, transparent) 0%, transparent 60%),
                             radial-gradient(circle at 88% 82%, color-mix(in srgb, var(--text-color) 18%, transparent) 0%, transparent 70%)`
              }}
            />
            <div className="relative p-3">
              {isArabic && renderQuranHeader()}
              {(part.translator || !isArabic) && (
                <div className="mb-2 text-[10px] uppercase tracking-wide font-semibold text-[var(--text-color)]/55">
                  {part.translator || 'Metin'}
                </div>
              )}
              <div
                className={`leading-relaxed whitespace-pre-wrap ${
                  isArabic
                    ? 'text-right md:text-left text-[var(--color-primary)] font-arabic text-[1.4rem] md:text-[1.45rem]'
                    : 'text-sm text-left text-[var(--text-color)]/90'
                }`}
              >
                {isArabic ? part.text : renderMixedArabic(part.text)}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};


