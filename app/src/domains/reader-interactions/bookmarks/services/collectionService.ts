import { supabase } from '@shared/utils/supabaseClient';
// ✅ YENİ ORGANİZE YAPISI - Shared types
import type {
  BookmarkCollection,
  CreateCollectionInput,
  UpdateCollectionInput,
  AnnotationServiceResponse
} from '../../shared/types';

/**
 * Koleksiyon servisi
 * Bookmark koleksiyonları için CRUD işlemleri
 */
export class CollectionService {
  /**
   * Kullanıcının tüm koleksiyonlarını getirir
   */
  async getUserCollections(userId: string): Promise<AnnotationServiceResponse<BookmarkCollection[]>> {
    try {
      const { data, error } = await supabase
        .from('bookmark_collections')
        .select('*')
        .eq('user_id', userId)
        .order('is_default', { ascending: false }) // Varsayılan koleksiyonlar önce
        .order('created_at', { ascending: true });

      if (error) {
        console.error('[CollectionService] Get collections error:', error);
        return {
          data: null,
          error: {
            code: 'FETCH_FAILED',
            message: 'Koleksiyonlar getirilemedi',
            details: error
          }
        };
      }

      return { data: data || [], error: null };
    } catch (err) {
      console.error('[CollectionService] Get collections error:', err);
      return {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'Beklenmeyen hata oluştu',
          details: err
        }
      };
    }
  }

  /**
   * Yeni koleksiyon oluşturur
   */
  async createCollection(input: CreateCollectionInput): Promise<AnnotationServiceResponse<BookmarkCollection>> {
    try {
      // Varsayılan değerler
      const collectionData = {
        ...input,
        color: input.color || '#3b82f6',
        icon: input.icon || 'bookmark',
        is_default: input.is_default || false
      };

      const { data, error } = await supabase
        .from('bookmark_collections')
        .insert(collectionData)
        .select()
        .single();

      if (error) {
        console.error('[CollectionService] Create collection error:', error);
        return {
          data: null,
          error: {
            code: 'CREATE_FAILED',
            message: 'Koleksiyon oluşturulamadı',
            details: error
          }
        };
      }

      return { data, error: null };
    } catch (err) {
      console.error('[CollectionService] Create collection error:', err);
      return {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'Beklenmeyen hata oluştu',
          details: err
        }
      };
    }
  }

  /**
   * Koleksiyonu günceller
   */
  async updateCollection(
    id: string,
    input: UpdateCollectionInput
  ): Promise<AnnotationServiceResponse<BookmarkCollection>> {
    try {
      const { data, error } = await supabase
        .from('bookmark_collections')
        .update(input)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('[CollectionService] Update collection error:', error);
        return {
          data: null,
          error: {
            code: 'UPDATE_FAILED',
            message: 'Koleksiyon güncellenemedi',
            details: error
          }
        };
      }

      return { data, error: null };
    } catch (err) {
      console.error('[CollectionService] Update collection error:', err);
      return {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'Beklenmeyen hata oluştu',
          details: err
        }
      };
    }
  }

  /**
   * Koleksiyonu siler
   */
  async deleteCollection(id: string): Promise<AnnotationServiceResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('bookmark_collections')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('[CollectionService] Delete collection error:', error);
        return {
          data: null,
          error: {
            code: 'DELETE_FAILED',
            message: 'Koleksiyon silinemedi',
            details: error
          }
        };
      }

      return { data: true, error: null };
    } catch (err) {
      console.error('[CollectionService] Delete collection error:', err);
      return {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'Beklenmeyen hata oluştu',
          details: err
        }
      };
    }
  }

  /**
   * Varsayılan koleksiyonu getirir
   */
  async getDefaultCollection(userId: string): Promise<AnnotationServiceResponse<BookmarkCollection>> {
    try {
      const { data, error } = await supabase
        .from('bookmark_collections')
        .select('*')
        .eq('user_id', userId)
        .eq('is_default', true)
        .single();

      if (error) {
        console.error('[CollectionService] Get default collection error:', error);
        return {
          data: null,
          error: {
            code: 'FETCH_FAILED',
            message: 'Varsayılan koleksiyon bulunamadı',
            details: error
          }
        };
      }

      return { data, error: null };
    } catch (err) {
      console.error('[CollectionService] Get default collection error:', err);
      return {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'Beklenmeyen hata oluştu',
          details: err
        }
      };
    }
  }

  /**
   * Kullanıcı için varsayılan koleksiyonları oluşturur
   */
  async createDefaultCollections(userId: string): Promise<AnnotationServiceResponse<BookmarkCollection[]>> {
    try {
      // Supabase fonksiyonunu çağır
      const { error } = await supabase.rpc('create_default_collections_for_user', {
        user_id: userId
      });

      if (error) {
        console.error('[CollectionService] Create default collections error:', error);
        return {
          data: null,
          error: {
            code: 'CREATE_DEFAULTS_FAILED',
            message: 'Varsayılan koleksiyonlar oluşturulamadı',
            details: error
          }
        };
      }

      // Oluşturulan koleksiyonları getir
      return this.getUserCollections(userId);
    } catch (err) {
      console.error('[CollectionService] Create default collections error:', err);
      return {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'Beklenmeyen hata oluştu',
          details: err
        }
      };
    }
  }

  /**
   * Koleksiyondaki bookmark sayısını getirir
   */
  async getCollectionBookmarkCount(collectionId: string): Promise<AnnotationServiceResponse<number>> {
    try {
      // M2M join tablosu üzerinden sayım
      const { count, error } = await supabase
        .from('bookmark_annotation_collections')
        .select('*', { count: 'exact', head: true })
        .eq('collection_id', collectionId);

      if (error) {
        console.error('[CollectionService] Get bookmark count error:', error);
        return {
          data: null,
          error: {
            code: 'COUNT_FAILED',
            message: 'Bookmark sayısı alınamadı',
            details: error
          }
        };
      }

      return { data: count || 0, error: null };
    } catch (err) {
      console.error('[CollectionService] Get bookmark count error:', err);
      return {
        data: null,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: 'Beklenmeyen hata oluştu',
          details: err
        }
      };
    }
  }

  /**
   * Çoklu koleksiyon desteği: Bir annotation'ın bağlı olduğu koleksiyonları getirir
   * Varsa join tablosundan okur; yoksa legacy `collection_id` alanına düşer (UI tarafında listeden tekli eşleşir)
   */
  async getCollectionsForAnnotation(annotationId: string): Promise<AnnotationServiceResponse<string[]>> {
    try {
      // Join tablosundan okumayı dene
      const { data, error } = await supabase
        .from('bookmark_annotation_collections')
        .select('collection_id')
        .eq('annotation_id', annotationId);

      if (error) {
        return { data: null, error: { code: 'FETCH_FAILED', message: 'Koleksiyonlar getirilemedi', details: error } };
      }

      const ids = (data || []).map(r => r.collection_id as string);
      return { data: ids, error: null };
    } catch (err) {
      return { data: null, error: { code: 'UNEXPECTED_ERROR', message: 'Beklenmeyen hata', details: err } };
    }
  }

  /**
   * Annotation'ı birden fazla koleksiyona bağlar (idempotent)
   */
  async linkAnnotationToCollections(annotationId: string, collectionIds: string[]): Promise<AnnotationServiceResponse<boolean>> {
    try {
      if (!collectionIds || collectionIds.length === 0) return { data: true, error: null };
      const rows = collectionIds.map(id => ({ annotation_id: annotationId, collection_id: id }));
      // Upsert desteklenmiyor; önce mevcutları silip sonra ekleyelim (idempotent davranış için yalnızca eksik olanları eklemek gerekirse önce select yapılabilir)
      // Basit yaklaşım: ekleme dene, conflict olursa yoksay
      const { error } = await supabase
        .from('bookmark_annotation_collections')
        .insert(rows, { onConflict: 'annotation_id,collection_id', ignoreDuplicates: true } as any);
      if (error) {
        return { data: null, error: { code: 'LINK_FAILED', message: 'Koleksiyon bağlanamadı', details: error } };
      }
      return { data: true, error: null };
    } catch (err) {
      return { data: null, error: { code: 'UNEXPECTED_ERROR', message: 'Beklenmeyen hata', details: err } };
    }
  }

  /**
   * Annotation'ı verilen koleksiyonlardan ayırır
   */
  async unlinkAnnotationFromCollections(annotationId: string, collectionIds: string[]): Promise<AnnotationServiceResponse<boolean>> {
    try {
      if (!collectionIds || collectionIds.length === 0) return { data: true, error: null };
      let query = supabase
        .from('bookmark_annotation_collections')
        .delete()
        .eq('annotation_id', annotationId);
      // Supabase .in filter
      // @ts-ignore
      query = query.in('collection_id', collectionIds);
      const { error } = await query;
      if (error) {
        return { data: null, error: { code: 'UNLINK_FAILED', message: 'Koleksiyon bağlantısı kaldırılamadı', details: error } };
      }
      return { data: true, error: null };
    } catch (err) {
      return { data: null, error: { code: 'UNEXPECTED_ERROR', message: 'Beklenmeyen hata', details: err } };
    }
  }
}

// Singleton instance
export const collectionService = new CollectionService();
