// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { annotationService } from '../../annotations/services/annotationService';
import { Annotation, AnnotationType } from '../../shared/types';

/**
 * Saved Content Service
 * Kullanıcının kaydettiği içerikleri (annotation, bookmark, highlight) yöneten servis
 */

export interface SavedContentSummary {
  recentItems: Annotation[];
  bookmarks: Annotation[];
  notes: Annotation[];
  highlights: Annotation[];
  totalCount: number;
}

export interface SavedContentFilters {
  limit?: number;
  type?: AnnotationType;
  bookId?: string;
  sectionId?: string;
}

export const savedContentService = {
  /**
   * Kullanıcının tüm kaydettiği içerikleri getirir
   */
  async getUserSavedContent(userId: string, filters?: SavedContentFilters): Promise<SavedContentSummary | null> {
    try {
      // Tüm annotation'ları getir
      const response = await annotationService.getAnnotations({
        user_id: userId,
        book_id: filters?.bookId,
        section_id: filters?.sectionId,
        annotation_type: filters?.type
      });

      if (response.error || !response.data) {
        console.error('[savedContentService] Error fetching saved content:', response.error);
        return null;
      }

      const allAnnotations = response.data;

      // Tarihe göre sırala (en yeni önce)
      const sortedByDate = [...allAnnotations].sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

      // Tiplere göre ayır
      const bookmarks = allAnnotations.filter(a => a.annotation_type === 'bookmark');
      const notes = allAnnotations.filter(a => a.annotation_type === 'note');
      const highlights = allAnnotations.filter(a => a.annotation_type === 'highlight');

      // Son eklenenler (limit uygula)
      const limit = filters?.limit || 10;
      const recentItems = sortedByDate.slice(0, limit);

      return {
        recentItems,
        bookmarks,
        notes,
        highlights,
        totalCount: allAnnotations.length
      };
    } catch (error) {
      console.error('[savedContentService] Exception:', error);
      return null;
    }
  },

  /**
   * Belirli bir tip için kaydettiği içerikleri getirir
   */
  async getSavedContentByType(userId: string, type: AnnotationType, limit?: number): Promise<Annotation[]> {
    try {
      const response = await annotationService.getAnnotations({
        user_id: userId,
        annotation_type: type
      });

      if (response.error || !response.data) {
        return [];
      }

      // Tarihe göre sırala ve limit uygula
      const sorted = response.data.sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

      return limit ? sorted.slice(0, limit) : sorted;
    } catch (error) {
      console.error('[savedContentService] Exception in getSavedContentByType:', error);
      return [];
    }
  },

  /**
   * Son kaydedilen içerikleri getirir
   */
  async getRecentSavedContent(userId: string, limit: number = 5): Promise<Annotation[]> {
    try {
      const response = await annotationService.getAnnotations({
        user_id: userId
      });

      if (response.error || !response.data) {
        return [];
      }

      // Tarihe göre sırala ve limit uygula
      return response.data
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('[savedContentService] Exception in getRecentSavedContent:', error);
      return [];
    }
  },

  /**
   * Kaydettiği içerik istatistiklerini getirir
   */
  async getSavedContentStats(userId: string) {
    try {
      const response = await annotationService.getAnnotationStats(userId);

      return response.data;
    } catch (error) {
      console.error('[savedContentService] Exception in getSavedContentStats:', error);
      return null;
    }
  },

  /**
   * Belirli bir kitaptaki kaydettiği içerikleri getirir
   */
  async getSavedContentByBook(userId: string, bookId: string): Promise<Annotation[]> {
    try {
      const response = await annotationService.getAnnotations({
        user_id: userId,
        book_id: bookId
      });

      if (response.error || !response.data) {
        return [];
      }

      return response.data.sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    } catch (error) {
      console.error('[savedContentService] Exception in getSavedContentByBook:', error);
      return [];
    }
  }
};
