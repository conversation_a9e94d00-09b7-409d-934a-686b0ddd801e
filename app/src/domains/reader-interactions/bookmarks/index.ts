/**
 * Bookmarks Domain
 * Yer imi sistemi - Koleksiyon bazlı yer imi yönetimi
 */

// Components
export { BookmarkBottomSheet } from './components/BookmarkBottomSheet';

// Hooks
export { useCollectionManager } from './hooks/useCollectionManager';

// Services
export { collectionService } from './services/collectionService';
export { savedContentService } from './services/savedContentService';

// Pages
export { default as SavedContentPage } from './pages/SavedContentPage';

// Types (re-export from shared)
export type {
  BookmarkCollection,
  CreateCollectionInput,
  UpdateCollectionInput
} from '../shared/types';
