import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { PageLayout, LoadingState, EmptyState } from '@shared/components';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { usePageLayout } from '@reader/hooks';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { filterByNormalizedQuery } from '@shared/utils/searchUtils';
import { savedContentService } from '../services/savedContentService';
import { Annotation, AnnotationType } from '@domains/reader-interactions/shared/types/types';
import { useBookService } from '@domains/library/hooks/useBookService';
import { useSurahs } from '@domains/reader/hooks/quran/useSurahs';
import { useCollectionManager } from '../hooks/useCollectionManager';
import { fetchData } from '@shared/utils/dataFetcher';
import { BarChart3, ChevronDown, Search, Calendar, MessageSquare, Filter } from 'lucide-react';
import { NewFilterSheet } from '../components/NewFilterSheet';
import { StructuredContentDisplay } from '../components/StructuredContentDisplay';
import MessageIcon from '@shared/components/Icons/SherhIcon';
import { supabase } from '@shared/utils/supabaseClient';
import BookmarkIcon from '@shared/components/Icons/BookmarkIcon';
import NoteIcon from '@shared/components/Icons/NoteIcon';
import HighlightIcon from '@shared/components/Icons/HighlightIcon';

// --- Type Definitions ---
type FilterType = 'all' | 'note' | 'sherh' | 'highlight' | 'bookmark';
type SortType = 'recent' | 'oldest' | 'alphabetical';


interface SavedContentStats {
  total_count: number;
  by_type: { note: number; sherh: number; highlight: number; bookmark: number };
  by_category: { quran: number; risale: number; tafsir: number };
  by_collection: Record<string, number>;
}

interface BookIndex {
  id: string;
  title: string;
  sections: { id: string; title: string }[];
}

// --- Constants ---
const FILTERS: { key: FilterType; label: string; icon: React.ElementType }[] = [
  { key: 'all', label: 'Tümü', icon: BarChart3 },
  { key: 'note', label: 'Notlar', icon: NoteIcon as any },
  { key: 'sherh', label: 'Şerhler', icon: MessageIcon as any },
  { key: 'highlight', label: 'Vurgular', icon: HighlightIcon as any },
  { key: 'bookmark', label: 'Kaydedilenler', icon: BookmarkIcon as any },
];



// --- Helper Components & Functions ---

const TruncatedText = ({ text, maxLength = 280 }: { text: string | null; maxLength?: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  if (!text) return null;

  if (text.length <= maxLength) {
    return <p className="text-sm whitespace-pre-wrap text-[var(--text-color)]/90">{text}</p>;
  }

  return (
    <>
      <p className="text-sm whitespace-pre-wrap text-[var(--text-color)]/90">
        {isExpanded ? text : `${text.substring(0, maxLength)}...`}
      </p>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="text-sm font-semibold text-[var(--color-primary)] mt-2 hover:underline"
      >
        {isExpanded ? 'Daha az göster' : 'Devamını oku'}
      </button>
    </>
  );
};

const getIconForType = (type: AnnotationType) => {
  switch (type) {
    case 'note':
      return NoteIcon as any;
    case 'sherh':
      return MessageIcon as any;
    case 'highlight':
      return HighlightIcon as any;
    case 'bookmark':
      return BookmarkIcon as any;
    default:
      return MessageSquare;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('tr-TR', { year: 'numeric', month: 'long', day: 'numeric' });
};

// --- Helper Functions ---
const calculateEnhancedStats = async (annotations: Annotation[], baseStats: any): Promise<SavedContentStats> => {
  const by_category = { quran: 0, risale: 0, tafsir: 0 };
  const by_collection: Record<string, number> = {};

  annotations.forEach(annotation => {
    // Category stats
    if (annotation.book_id === 'quran') {
      by_category.quran++;
    } else if (annotation.book_id.startsWith('tafsir')) {
      by_category.tafsir++;
    } else {
      by_category.risale++;
    }
  });

  // Collection stats - Gerçek verilerle
  try {
    const { data: collectionLinks } = await supabase
      .from('bookmark_annotation_collections')
      .select('collection_id, annotation_id')
      .in('annotation_id', annotations.filter(a => a.annotation_type === 'bookmark').map(a => a.id));

    if (collectionLinks) {
      collectionLinks.forEach((link: { collection_id: string; annotation_id: string }) => {
        by_collection[link.collection_id] = (by_collection[link.collection_id] || 0) + 1;
      });
    }
  } catch (error) {
    console.error('Collection stats error:', error);
  }

  return {
    total_count: baseStats?.total_count || annotations.length,
    by_type: baseStats?.by_type || { note: 0, sherh: 0, highlight: 0, bookmark: 0 },
    by_category,
    by_collection,
  };
};

// --- Custom Hook for Data Fetching & State Management ---
const useSavedContent = (userId: string | undefined) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [allContent, setAllContent] = useState<Annotation[]>([]);
  const [collectionMembership, setCollectionMembership] = useState<Record<string, string[]>>({}); // annotation_id -> [collection_id]
  const [stats, setStats] = useState<SavedContentStats>({
    total_count: 0,
    by_type: { note: 0, sherh: 0, highlight: 0, bookmark: 0 },
    by_category: { quran: 0, risale: 0, tafsir: 0 },
    by_collection: {},
  });

  useEffect(() => {
    if (!userId) {
      setLoading(false);
      return;
    }

    const fetchContent = async () => {
      setLoading(true);
      setError(null);
      try {
        const [notes, sherhs, highlights, bookmarks, statsData] = await Promise.all([
          savedContentService.getSavedContentByType(userId, 'note'),
          savedContentService.getSavedContentByType(userId, 'sherh'),
          savedContentService.getSavedContentByType(userId, 'highlight'),
          savedContentService.getSavedContentByType(userId, 'bookmark'),
          savedContentService.getSavedContentStats(userId),
        ]);
        
        const allAnnotations = [...notes, ...sherhs, ...highlights, ...bookmarks];
        setAllContent(allAnnotations);

        // Fetch collection membership for bookmark annotations
        const bookmarkIds = bookmarks.map(b => b.id);
        if (bookmarkIds.length > 0) {
          try {
            const { data: links } = await supabase
              .from('bookmark_annotation_collections')
              .select('collection_id, annotation_id')
              .in('annotation_id', bookmarkIds);
            if (links) {
              const map: Record<string, string[]> = {};
              links.forEach(l => {
                if (!map[l.annotation_id]) map[l.annotation_id] = [];
                map[l.annotation_id].push(l.collection_id);
              });
              setCollectionMembership(map);
            }
          } catch (cmErr) {
            console.warn('Collection membership fetch failed', cmErr);
          }
        } else {
          setCollectionMembership({});
        }

        // Calculate enhanced stats
        const enhancedStats = await calculateEnhancedStats(allAnnotations, statsData);
        setStats(enhancedStats);
      } catch (err) {
        console.error("Failed to fetch saved content:", err);
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, [userId]);

  return { loading, error, allContent, stats, collectionMembership };
};


// --- Main Component ---
const SavedContentPage = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { user, isAuthenticated } = useAuthStore();
  const { loading: contentLoading, error, allContent, stats, collectionMembership } = useSavedContent(user?.id);
  const { books, loading: booksLoading } = useBookService();
  const { data: surahs, loading: surahsLoading } = useSurahs();
  const { collections } = useCollectionManager();

  const [sectionNameMaps, setSectionNameMaps] = useState<Record<string, Record<string, string>>>({});
  const [loadingSections, setLoadingSections] = useState(true);

  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const [sortBy, setSortBy] = useState<SortType>('recent');
  const filterButtonRef = useRef<HTMLButtonElement | null>(null);

  // Advanced filter states
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedBooks, setSelectedBooks] = useState<string[]>([]);
  const [selectedSections, setSelectedSections] = useState<string[]>([]); // format: bookId::sectionId
  const [selectedCollections, setSelectedCollections] = useState<string[]>([]);

  const pageLayoutConfig = usePageLayout({ title: "Kaydettiklerim", isMobile });
  const bgColor = useAutoOverlay(6, 'var(--bg-color)');
  const borderColor = useAutoOverlay(20, 'var(--bg-color)');
  const inputBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const cardBgColor = useAutoOverlay(7, 'var(--bg-color)');
  const dividerColor = useAutoOverlay(14, 'var(--bg-color)');

  const bookMap = useMemo(() => {
    return books.reduce((acc, book) => {
      acc[book.id] = book.title;
      return acc;
    }, {} as Record<string, string>);
  }, [books]);

  const surahMap = useMemo(() => {
    if (!surahs) return {};
    return surahs.reduce((acc, surah) => {
      acc[String(surah.id)] = surah.name;
      return acc;
    }, {} as Record<string, string>);
  }, [surahs]);

  useEffect(() => {
    if (contentLoading || allContent.length === 0) {
      if (allContent.length === 0 && !contentLoading) setLoadingSections(false);
      return;
    }

    const fetchSectionNames = async () => {
      setLoadingSections(true);
      const uniqueBookIds = [...new Set(allContent.map(item => item.book_id).filter(id => id !== 'quran'))];
      
      if (uniqueBookIds.length === 0) {
        setLoadingSections(false);
        return;
      }

      const promises = uniqueBookIds.map(bookId => 
        fetchData<BookIndex>(`risalei_nur/${bookId}/index.json`)
          .then(indexData => ({ bookId, indexData }))
          .catch(err => {
            console.warn(`Could not fetch index for book ${bookId}:`, err);
            return null;
          })
      );

      const results = await Promise.all(promises);
      const newMaps: Record<string, Record<string, string>> = {};

      results.forEach(result => {
        if (result) {
          const { bookId, indexData } = result;
          newMaps[bookId] = indexData.sections.reduce((acc, section) => {
            acc[section.id] = section.title;
            return acc;
          }, {} as Record<string, string>);
        }
      });

      setSectionNameMaps(newMaps);
      setLoadingSections(false);
    };

    fetchSectionNames();
  }, [allContent, contentLoading]);

  const isLoading = contentLoading || booksLoading || surahsLoading || loadingSections;

  // Prepare filter options
  const categoryFilterOptions = useMemo(() => [
    { id: '1', label: "Kur'an-ı Kerim", count: stats.by_category.quran },
    { id: '2', label: "Risale-i Nur", count: stats.by_category.risale },
    { id: '4', label: "Tefsirler", count: stats.by_category.tafsir },
  ], [stats.by_category]);

  const bookFilterOptions = useMemo(() => {
    const bookCounts: Record<string, number> = {};
    allContent.forEach(item => {
      bookCounts[item.book_id] = (bookCounts[item.book_id] || 0) + 1;
    });

    return books.map(book => ({
      id: String(book.id),
      label: book.title,
      author: book.author,
      count: bookCounts[String(book.id)] || 0,
      categoryId: book.category_id
    })).filter(book => book.count > 0);
  }, [books, allContent]);

  const collectionFilterOptions = useMemo(() => {
    return collections.map(collection => ({
      id: collection.id,
      label: collection.name,
      count: stats.by_collection[collection.id] || 0
    })).filter(collection => collection.count > 0);
  }, [collections, stats.by_collection]);

  const filteredAndSortedContent = useMemo(() => {
    if (isLoading) return [];

    const filteredByType = allContent.filter(item => {
      // Type filter
      if (activeFilter !== 'all' && item.annotation_type !== activeFilter) return false;

      // Advanced category filter
      if (selectedCategories.length > 0) {
        const itemCategoryId = (() => {
          if (item.book_id === 'quran') return '1';
          const book = books.find(b => String(b.id) === item.book_id);
          return book ? String(book.category_id) : '2'; // Default to Risale
        })();
        if (!selectedCategories.includes(itemCategoryId)) return false;
      }

      // Advanced book filter
      if (selectedBooks.length > 0) {
        if (!selectedBooks.includes(item.book_id)) return false;
      }

      // Advanced section filter (apply to all non-empty selection keys)
      if (selectedSections.length > 0) {
        const key = `${item.book_id}::${item.section_id}`;
        if (!selectedSections.includes(key)) return false;
      }

      // Advanced collection filter (only bookmarks)
      if (selectedCollections.length > 0) {
        if (item.annotation_type === 'bookmark') {
          const memberships = collectionMembership[item.id] || [];
            if (!memberships.some(c => selectedCollections.includes(c))) return false;
        } else {
          // Non-bookmark annotations excluded if collection filter active
          return false;
        }
      }

      return true;
    });

    const getLocationName = (item: Annotation) => {
      if (item.book_id === 'quran') {
        return surahMap[item.section_id] || item.section_id;
      }
      return sectionNameMaps[item.book_id]?.[item.section_id] || item.section_id;
    };

    const searched = filterByNormalizedQuery(filteredByType, searchTerm, (item) => {
      const bookTitle = bookMap[item.book_id] || (item.book_id === 'quran' ? "Kur'an-ı Kerim" : item.book_id);
      return [
        item.selected_text,
        item.annotation_content || '',
        bookTitle,
        getLocationName(item),
      ];
    });

    return searched.sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'alphabetical':
          return a.selected_text.localeCompare(b.selected_text, 'tr');
        case 'recent':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });
  }, [allContent, activeFilter, selectedCategories, selectedBooks, selectedSections, selectedCollections, searchTerm, sortBy, bookMap, surahMap, sectionNameMaps, isLoading, books, collectionMembership]);

  // Section filter options (Risale & Tefsir)
  const sectionFilterOptions = useMemo(() => {
    const counts: Record<string, number> = {};
    allContent.forEach(a => {
      if (a.book_id === 'quran') return; // skip
      const key = `${a.book_id}::${a.section_id}`;
      counts[key] = (counts[key] || 0) + 1;
    });
    const options = Object.entries(counts).map(([key, count]) => {
      const [bookId, sectionId] = key.split('::');
      const sectionLabel = sectionNameMaps[bookId]?.[sectionId] || sectionId;
      const bookTitle = bookMap[bookId] || bookId;
      return {
        id: key,
        label: `${bookTitle} • ${sectionLabel}`,
        count
      };
    }).sort((a,b) => b.count - a.count);
    return options;
  }, [allContent, sectionNameMaps, bookMap]);


  const parseQuranSentenceId = (raw: string | string[]) => {
    const val = Array.isArray(raw) ? raw[0] : raw;
    // Beklenen format: JUZ_SURAH_VERSE veya SURAH_VERSE_INDEX gibi 1_1_0
    // Son sayı çoğu zaman verse index (0 bazlı olabilir). Ortadaki verseNumber olabilir.
    // Örnek: 1_1_0 -> surah=1, verse=1
    const parts = val.split('_').map(p => p.trim()).filter(Boolean);
    if (parts.length >= 2) {
      // Son parça 0 ise ignore edip ortadakini al, değilse sonu al
      const maybeSurah = parseInt(parts[0], 10);
      // İkinci parça çoğunlukla ayet numarası (1 bazlı)
      const verse = parseInt(parts[1], 10) || 1;
      return { surah: maybeSurah, verse };
    }
    // Fallback: sadece sayıysa
    const num = parseInt(val, 10);
    return { surah: NaN, verse: isNaN(num) ? 1 : num };
  };

  const resolveAnnotationRoute = (a: Annotation) => {
    if (a.book_id === 'quran') {
      const { surah, verse } = parseQuranSentenceId(a.sentence_id);
      // Route patterns in App: /kuran/:surahId and optional ?verse param (biz ekledik)
      const surahId = isNaN(surah) ? a.section_id : surah;
      return `/kuran/${surahId}?verse=${verse}`;
    }
    // Tefsir kitapları id pattern'i (varsayımsal)
    if (a.book_id.startsWith('tafsir')) {
      const verse = Array.isArray(a.sentence_id) ? a.sentence_id[0] : a.sentence_id;
      return `/tafsir/${a.book_id}/${a.section_id}?verse=${verse}`;
    }
    // Risale varsayılan
    const sentence = Array.isArray(a.sentence_id) ? a.sentence_id[0] : a.sentence_id;
    return `/risale/${a.book_id}/${a.section_id}?sentence=${sentence}`;
  };

  const navigateToReference = (annotation: Annotation) => {
    const target = resolveAnnotationRoute(annotation);
    navigate(target, { state: { highlight: annotation.id } });
  };

  // --- Quran Mode Popover State ---
  const [openQuranModeFor, setOpenQuranModeFor] = useState<string | null>(null);
  const [anchorRect, setAnchorRect] = useState<DOMRect | null>(null);
  const popoverRef = useRef<HTMLDivElement | null>(null);
  const modeButtonRefs = useRef<Record<string, HTMLButtonElement | null>>({});

  const QURAN_MODE_OPTIONS: { id: '1'|'2'|'3'|'4'; title: string }[] = [
    { id: '1', title: "Sadece Kur'an" },
    { id: '2', title: 'Kur\'an ve Meal' },
    { id: '3', title: 'Kelime Meali' },
    { id: '4', title: 'Sadece Meal' }
  ];

  const buildQuranPath = (surahId: string, verse: string | number | undefined, mode: '1'|'2'|'3'|'4') => {
    const baseMap: Record<string, string> = {
      '1': '/kuran',
      '2': '/mealli-kuran',
      '3': '/kelime-mealli-kuran',
      '4': '/kuran-meali'
    };
    let path = `${baseMap[mode]}/${surahId}`;
    if (verse) path += `?verse=${verse}`;
    return path;
  };

  const openModePopover = (annotation: Annotation, e?: React.MouseEvent) => {
    if (annotation.book_id !== 'quran') {
      navigateToReference(annotation);
      return;
    }
    setOpenQuranModeFor(prev => prev === annotation.id ? null : annotation.id);
    if (e) {
      const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      setAnchorRect(rect);
    } else if (modeButtonRefs.current[annotation.id]) {
      setAnchorRect(modeButtonRefs.current[annotation.id]!.getBoundingClientRect());
    }
  };

  const navigateWithMode = (annotation: Annotation, mode: '1'|'2'|'3'|'4') => {
    if (annotation.book_id !== 'quran') return;
    const { verse } = parseQuranSentenceId(annotation.sentence_id);
    const surahId = annotation.section_id;
    const target = buildQuranPath(String(surahId), verse, mode);
  setOpenQuranModeFor(null);
    navigate(target, { state: { highlight: annotation.id } });
  };

  // Outside click handler for popover
  useEffect(() => {
    if (!openQuranModeFor) return;
    const handler = (e: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(e.target as Node)) {
        // If click originated from trigger keep open toggle logic already handled
        const trigger = (e.target as Element).closest('[data-quran-mode-trigger]');
        if (!trigger) setOpenQuranModeFor(null);
      }
    };
    document.addEventListener('mousedown', handler);
    return () => document.removeEventListener('mousedown', handler);
  }, [openQuranModeFor]);

  const QuranModePopover: React.FC<{ annotation: Annotation }> = ({ annotation }) => {
    if (openQuranModeFor !== annotation.id || !anchorRect) return null;
    const viewportPadding = 8;
  const desiredWidth = 160; // narrower width
    // Default position to the right of anchor
    let top = anchorRect.top;
    let left = anchorRect.right + 8;
    const vw = window.innerWidth;
    const vh = window.innerHeight;
    const estimatedHeight = 8 + QURAN_MODE_OPTIONS.length * 36; // rough height
    // If off right edge, try left side
    if (left + desiredWidth + viewportPadding > vw) {
      left = anchorRect.left - desiredWidth - 8;
    }
    // If still off, clamp
    if (left < viewportPadding) left = viewportPadding;
    // Vertical adjust: if bottom clipped, shift up
    if (top + estimatedHeight + viewportPadding > vh) {
      top = Math.max(viewportPadding, vh - estimatedHeight - viewportPadding);
    }
    // On very narrow screens (< 480), center above anchor
    if (vw < 480) {
      left = Math.max(viewportPadding, Math.min(vw - desiredWidth - viewportPadding, anchorRect.left + (anchorRect.width/2) - desiredWidth/2));
      top = anchorRect.bottom + 6;
      if (top + estimatedHeight + viewportPadding > vh) {
        top = anchorRect.top - estimatedHeight - 6;
      }
    }
  const style: React.CSSProperties = { position: 'fixed', top, left, width: desiredWidth, zIndex: 90 };
    return (
      <div ref={popoverRef} style={style} className="animate-in fade-in zoom-in-95 duration-150 origin-top-left">
        <div className="rounded-xl shadow-xl border backdrop-blur-md overflow-hidden ring-1 ring-black/5"
          style={{ backgroundColor: useAutoOverlay(10,'var(--bg-color)'), borderColor: 'color-mix(in srgb, var(--text-color) 18%, transparent)' }}>
    <ul className="py-1.5">
            {QURAN_MODE_OPTIONS.map(opt => (
              <li key={opt.id}>
                <button
                  onClick={() => navigateWithMode(annotation, opt.id)}
      className="w-full text-left px-3 py-2 text-[13px] leading-snug hover:bg-[var(--text-color)]/10 text-[var(--text-color)] flex items-center gap-2 transition-colors rounded-md"
                >
                  <span className="truncate font-medium">{opt.title}</span>
                </button>
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return <LoadingState message="Kaydettiğiniz içerikler ve detaylar yükleniyor..." />;
    }
    if (!isAuthenticated) {
      return <EmptyState message="Bu sayfayı görüntülemek için giriş yapmalısınız." />;
    }
    if (error) {
      return <EmptyState message="İçerikler yüklenirken bir hata oluştu." action={<p className="text-sm opacity-80">{error.message}</p>} />;
    }
    if (allContent.length === 0) {
      return <EmptyState message="Henüz kaydettiğiniz bir içerik yok." action={<p className="text-sm opacity-80">Metinleri seçerek not alabilir, vurgulayabilir veya yer imi ekleyebilirsiniz.</p>} />;
    }
    if (filteredAndSortedContent.length === 0) {
      return <EmptyState message={`"${searchTerm}" için sonuç bulunamadı.`} action={<p className="text-sm opacity-80">Farklı bir arama terimi veya filtre deneyin.</p>} />;
    }

    return (
      <div className="space-y-4">
        {filteredAndSortedContent.map((annotation) => {
          const Icon = getIconForType(annotation.annotation_type);
          const bookTitle = bookMap[annotation.book_id] || (annotation.book_id === 'quran' ? "Kur'an-ı Kerim" : annotation.book_id);
          
          let locationName: string;
          if (annotation.book_id === 'quran') {
            locationName = surahMap[annotation.section_id] || annotation.section_id;
          } else {
            locationName = sectionNameMaps[annotation.book_id]?.[annotation.section_id] || annotation.section_id;
          }

          return (
            <div
              key={annotation.id}
              className="p-4 rounded-xl transition-colors hover:bg-[var(--bg-color)]/60 focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]/40 group"
              style={{ backgroundColor: cardBgColor }}
            >

              <StructuredContentDisplay 
                annotation={annotation}
                {...(annotation.book_id === 'quran' ? {
                  verseNumber: parseQuranSentenceId(annotation.sentence_id).verse,
                  surahName: surahMap[annotation.section_id] || undefined,
                  surahNumber: annotation.section_id,
                  onNavigate: (e: React.MouseEvent) => openModePopover(annotation, e)
                } : {})}
              />

              {annotation.annotation_content && (
                <div className="mt-3">
                  <TruncatedText text={annotation.annotation_content} />
                </div>
              )}

              <div className="flex items-center mt-4 pt-3 relative">
                <div
                  aria-hidden="true"
                  className="absolute left-0 right-0 top-0 h-px"
                  style={{ backgroundColor: dividerColor }}
                />
                <div className="flex items-center gap-2 text-[var(--text-color)]/70 min-w-0">
                  <Icon size={16} className="text-[var(--color-primary)] flex-shrink-0" />
                  <div className="flex items-center gap-1 text-[11px] md:text-xs text-[var(--text-color)]/55">
                    <Calendar size={12} />
                    <span className="whitespace-nowrap">{formatDate(annotation.created_at)}</span>
                  </div>
                </div>
                <button
                  ref={el => { modeButtonRefs.current[annotation.id] = el; }}
                  data-quran-mode-trigger
                  onClick={(e) => annotation.book_id === 'quran' ? openModePopover(annotation, e) : navigateToReference(annotation)}
                  className="ml-auto pl-3 text-xs text-[var(--text-color)]/70 text-right leading-snug hover:text-[var(--color-primary)] transition-colors truncate underline-offset-2 hover:underline inline-flex items-center gap-1 relative"
                  title={`${bookTitle} / ${locationName}`}
                >
                  <span className="truncate max-w-[14rem]">{bookTitle} / {locationName}</span>
                  <svg width="14" height="14" viewBox="0 0 24 24" className="opacity-70 group-hover:opacity-100 transition-opacity" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round"><path d="M7 17L17 7" /><path d="M7 7h10v10"/></svg>
                  {annotation.book_id === 'quran' && <QuranModePopover annotation={annotation} />}
                </button>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <>
      <PageLayout {...pageLayoutConfig}>
        <main className="container mx-auto px-4 py-6 max-w-4xl">
        {/* Üst Tür Filtresi: Tüm ekranlar için tek tip (mobil özelleştirmeler kaldırıldı) */}
        <div
          className="p-2.5 md:p-3 rounded-xl border mb-5 md:mb-6"
          style={{ backgroundColor: bgColor, borderColor }}
        >
          <div>
            <div className="flex flex-wrap items-center gap-x-3 gap-y-2">
              {FILTERS.map(({ key, label, icon: Icon }) => {
                const count = key === 'all' ? stats.total_count : stats.by_type[key as Exclude<FilterType,'all'>];
                const isActive = activeFilter === key;
                return (
                  <button
                    key={key}
                    onClick={() => setActiveFilter(key)}
                    aria-pressed={isActive}
                    className={`flex items-center gap-2 px-3.5 py-1.5 rounded-lg text-sm font-medium transition-colors ${isActive ? 'bg-[var(--bg-color)] shadow-sm text-[var(--color-primary)]' : 'opacity-70 hover:opacity-100 hover:bg-[var(--text-color)]/5'} `}
                  >
                    <Icon size={15} />
                    <span>{label}</span>
                    <span className="text-[11px] bg-[var(--text-color)]/10 px-1.5 py-0.5 rounded-full">{count}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Arama + Filtre/Sıralama - Desktop tek satır, mobil iki satır */}
        {/* Mobil */}
        <div className="md:hidden space-y-3 mb-6">
          <div className="relative">
            <Search size={18} className="absolute left-3.5 top-1/2 -translate-y-1/2 opacity-40" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Kaydettikleriniz içinde arayın..."
              className="w-full pl-10 pr-4 py-2.5 rounded-lg text-sm focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors"
              style={{ backgroundColor: inputBgColor }}
            />
          </div>
          <div className="flex gap-2">
            <button
              ref={filterButtonRef}
              onClick={() => setIsFilterSheetOpen(true)}
              className={`flex flex-1 items-center justify-center gap-2 px-4 py-2.5 rounded-lg text-sm font-medium transition-colors duration-150 border border-transparent focus:outline-none focus:border-[var(--color-primary)]/60 ${
                isFilterSheetOpen ? 'ring-1 ring-[var(--color-primary)]/40' : ''
              } ${(selectedCategories.length > 0 || selectedBooks.length > 0 || selectedSections.length > 0 || selectedCollections.length > 0)
                ? 'bg-[var(--text-color)]/6 hover:bg-[var(--text-color)]/10'
                : 'hover:bg-[var(--text-color)]/8'} `}
              style={{
                backgroundColor: isFilterSheetOpen
                  ? 'color-mix(in srgb, var(--text-color) 10%, var(--bg-color))'
                  : 'color-mix(in srgb, var(--text-color) 6%, var(--bg-color))'
              }}
              aria-pressed={isFilterSheetOpen}
            >
              <Filter size={16} />
              <span className="truncate">Filtreler</span>
              {(selectedCategories.length > 0 || selectedBooks.length > 0 || selectedSections.length > 0 || selectedCollections.length > 0) && (
                <span className="bg-[var(--text-color)]/15 px-1.5 py-0.5 rounded-full text-[10px]">
                  {selectedCategories.length + selectedBooks.length + selectedSections.length + selectedCollections.length}
                </span>
              )}
            </button>
            <div className="relative flex-1">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortType)}
                className="w-full appearance-none px-3.5 py-2.5 pr-9 rounded-lg border-2 border-transparent text-sm focus:outline-none focus:border-[var(--color-primary)] transition-colors"
                style={{ backgroundColor: inputBgColor, color: 'var(--text-color)' }}
              >
                <option value="recent">En Yeni</option>
                <option value="oldest">En Eski</option>
                <option value="alphabetical">Alfabetik</option>
              </select>
              <ChevronDown size={16} className="absolute right-3 top-1/2 -translate-y-1/2 opacity-50 pointer-events-none" />
            </div>
          </div>
        </div>
        {/* Desktop */}
        <div className="hidden md:flex items-stretch gap-3 mb-8">
          <div className="relative flex-1 min-w-[18rem]">
            <Search size={18} className="absolute left-3.5 top-1/2 -translate-y-1/2 opacity-40" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Kaydettikleriniz içinde arayın..."
              className="w-full pl-10 pr-4 py-2.5 rounded-lg text-sm focus:outline-none border-2 border-transparent focus:border-[var(--color-primary)] transition-colors"
              style={{ backgroundColor: inputBgColor }}
            />
          </div>
          <button
            ref={filterButtonRef}
            onClick={() => setIsFilterSheetOpen(true)}
            className={`flex items-center gap-2 px-4 py-2.5 rounded-lg text-sm font-medium transition-colors duration-150 border border-transparent focus:outline-none focus:border-[var(--color-primary)]/60 ${
              isFilterSheetOpen ? 'ring-1 ring-[var(--color-primary)]/40' : ''
            } ${(selectedCategories.length > 0 || selectedBooks.length > 0 || selectedSections.length > 0 || selectedCollections.length > 0)
              ? 'bg-[var(--text-color)]/6 hover:bg-[var(--text-color)]/10'
              : 'hover:bg-[var(--text-color)]/8'} `}
            style={{ backgroundColor: isFilterSheetOpen ? 'color-mix(in srgb, var(--text-color) 10%, var(--bg-color))' : 'color-mix(in srgb, var(--text-color) 6%, var(--bg-color))' }}
            aria-pressed={isFilterSheetOpen}
          >
            <Filter size={16} />
            <span>Filtreler</span>
            {(selectedCategories.length > 0 || selectedBooks.length > 0 || selectedSections.length > 0 || selectedCollections.length > 0) && (
              <span className="bg-[var(--text-color)]/15 px-1.5 py-0.5 rounded-full text-xs">
                {selectedCategories.length + selectedBooks.length + selectedSections.length + selectedCollections.length}
              </span>
            )}
          </button>
          <div className="relative w-48">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortType)}
              className="w-full appearance-none px-4 py-2.5 pr-10 rounded-lg border-2 border-transparent text-sm focus:outline-none focus:border-[var(--color-primary)] transition-colors"
              style={{ backgroundColor: inputBgColor, color: 'var(--text-color)' }}
            >
              <option value="recent">En Yeni</option>
              <option value="oldest">En Eski</option>
              <option value="alphabetical">Alfabetik</option>
            </select>
            <ChevronDown size={16} className="absolute right-3.5 top-1/2 -translate-y-1/2 opacity-50 pointer-events-none" />
          </div>
        </div>

        {renderContent()}
      </main>

      {/* Filter Sheet */}
      <NewFilterSheet
        isOpen={isFilterSheetOpen}
        onClose={() => setIsFilterSheetOpen(false)}
        categoryFilters={categoryFilterOptions}
        bookFilters={bookFilterOptions}
        sectionFilters={sectionFilterOptions}
        collectionFilters={collectionFilterOptions}
        selectedCategories={selectedCategories}
        selectedBooks={selectedBooks}
        selectedSections={selectedSections}
        selectedCollections={selectedCollections}
        onCategoryChange={setSelectedCategories}
        onBookChange={setSelectedBooks}
        onSectionChange={setSelectedSections}
        onCollectionChange={setSelectedCollections}
        onReset={() => {
          setSelectedCategories([]);
          setSelectedBooks([]);
          setSelectedSections([]);
          setSelectedCollections([]);
        }}
        anchorRef={filterButtonRef}
      />
      </PageLayout>
    </>
  );
};

export default SavedContentPage;
