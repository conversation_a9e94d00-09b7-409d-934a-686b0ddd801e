/**
 * Reader Interactions Domain - Main Export
 * 
 * 🎯 New Organized Structure:
 * - annotations/    → Şerh (Note) sistemi
 * - highlights/     → Vurgulama sistemi  
 * - bookmarks/      → Yer imi sistemi
 * - text-selection/ → Metin seçimi al<PERSON>ap<PERSON>sı
 * - shared/         → Ortak component'ler
 * 
 * 📖 Usage Examples:
 * 
 * // Annotation features
 * import { useAnnotationManager, AnnotationSheet } from '@domains/reader-interactions/annotations';
 * 
 * // Highlight features
 * import { AnnotationHighlighter, ColorPicker } from '@domains/reader-interactions/highlights';
 * 
 * // Bookmark features
 * import { BookmarkBottomSheet, useCollectionManager } from '@domains/reader-interactions/bookmarks';
 * 
 * // Text selection
 * import { TextSelectionHandler, useTextSelectionHandler } from '@domains/reader-interactions/text-selection';
 * 
 * // Shared utilities
 * import { AnnotationToast, useBottomSheet } from '@domains/reader-interactions/shared';
 */

// ============================================================================
// NEW ORGANIZED EXPORTS (Recommended)
// ============================================================================

// Sub-domain exports
export * as Annotations from './annotations';
export * as Highlights from './highlights';
export * as Bookmarks from './bookmarks';
export * as TextSelection from './text-selection';
export * as Shared from './shared';

// ============================================================================
// LEGACY EXPORTS (For Backward Compatibility)
// ============================================================================

// Main components (most commonly used)
export { TextSelectionHandler } from './text-selection';
export { AnnotationHighlighter, AnnotationHighlighterStyles } from './highlights';
export { AnnotationFloatingPanel } from './shared';

// Main hooks
export { useAnnotationManager } from './annotations';
export { useTextSelectionHandler } from './text-selection';

// Legacy exports from old structure
export * from './hooks';
export * from './shared/types';

// ============================================================================
// MIGRATION GUIDE
// ============================================================================

/**
 * 🚀 Migration Guide:
 * 
 * OLD:
 * import { TextSelectionHandler } from '@domains/reader-interactions/components/TextSelectionHandler';
 * import { useAnnotationManager } from '@domains/reader-interactions/hooks/useAnnotationManager';
 * 
 * NEW (Recommended):
 * import { TextSelectionHandler } from '@domains/reader-interactions/text-selection';
 * import { useAnnotationManager } from '@domains/reader-interactions/annotations';
 * 
 * OR (Namespace style):
 * import { TextSelection, Annotations } from '@domains/reader-interactions';
 * const { TextSelectionHandler } = TextSelection;
 * const { useAnnotationManager } = Annotations;
 */
