import { create } from 'zustand';
import { contentInteractionService, IFavoriteContent, IUserContentActivity } from '../services/contentInteractionService';
import { useAuthStore } from '@domains/auth/store/authStore';
import { IBook } from '@domains/library/models/types';

interface IContentInteractionState {
  favorites: IFavoriteContent[];
  recentActivities: IUserContentActivity[];
  isLoadingFavorites: boolean;
  isLoadingRecents: boolean;
  error: string | null;

  // Favorileri getirme
  fetchFavorites: () => Promise<void>;
  // Favori ekleme/kaldırma
  toggleFavorite: (book: IBook) => Promise<void>;
  // Bir kitabın favori olup olmadığını kontrol etme
  isFavorite: (bookId: string) => boolean;

  // Son aktiviteleri getirme
  fetchRecentActivities: (limit?: number) => Promise<void>;
  // Aktivite kaydetme
  recordActivity: (bookId: string) => Promise<void>;

  // Store'u temizleme
  clearInteractions: () => void;
  // Error'ı temizleme
  clearError: () => void;
}


export const useContentInteractionStore = create<IContentInteractionState>((set, get) => ({
  favorites: [],
  recentActivities: [],
  isLoadingFavorites: false,
  isLoadingRecents: false,
  error: null,

  fetchFavorites: async () => {
    const userId = useAuthStore.getState().user?.id;
    if (!userId) {
      set({ favorites: [] });
      return;
    }

    set({ isLoadingFavorites: true, error: null });
    try {
      const favoriteData = await contentInteractionService.getFavorites(userId);
      set({ favorites: favoriteData, isLoadingFavorites: false });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Bilinmeyen bir hata oluştu.';
      set({ error: errorMessage, isLoadingFavorites: false });
    }
  },

  toggleFavorite: async (book: IBook) => {
    const userId = useAuthStore.getState().user?.id;
    if (!userId) return;

    const bookId = String(book.id);
    const isCurrentlyFavorite = get().isFavorite(bookId);

    // Optimistic UI update
    if (isCurrentlyFavorite) {
      // Favorilerden kaldır (UI'da anında güncelle)
      set(state => ({
        favorites: state.favorites.filter(fav => fav.content_id !== bookId),
      }));
    } else {
      // Favorilere ekle (UI'da anında güncelle)
      const tempFavorite: IFavoriteContent = { 
        id: Math.random().toString(), // Geçici ID
        user_id: userId, 
        content_id: bookId, 
        content_type: 'book', 
        created_at: new Date().toISOString() 
      };
      set(state => ({
        favorites: [...state.favorites, tempFavorite],
      }));
    }

    try {
      if (isCurrentlyFavorite) {
        await contentInteractionService.removeFavorite(userId, bookId);
      } else {
        await contentInteractionService.addFavorite(userId, bookId);
      }
      // Başarılı olursa, sunucudan en güncel listeyi çekerek state'i senkronize et
      await get().fetchFavorites();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Favori işlemi sırasında hata oluştu.';
      set({ error: errorMessage });
      // Hata durumunda optimistic update'i geri al
      if (isCurrentlyFavorite) {
        // Favorilerden çıkarma başarısız oldu, geri ekle
        const tempFavorite: IFavoriteContent = {
          id: Math.random().toString(), // Geçici ID
          user_id: userId,
          content_id: bookId,
          content_type: 'book',
          created_at: new Date().toISOString()
        };
        set(state => ({
          favorites: [...state.favorites, tempFavorite]
        }));
      } else {
        // Favorilere ekleme başarısız oldu, çıkar
        set(state => ({
          favorites: state.favorites.filter(fav => fav.content_id !== bookId)
        }));
      }
    }
  },

  isFavorite: (bookId: string) => {
    const { favorites } = get();
    return favorites.some(fav => fav.content_id === bookId);
  },

  fetchRecentActivities: async (limit: number = 5) => {
    const userId = useAuthStore.getState().user?.id;
    if (!userId) {
        set({ recentActivities: [] });
        return;
    }
    set({ isLoadingRecents: true, error: null });
    try {
      const recentData = await contentInteractionService.getRecentActivities(userId, limit);
      set({ recentActivities: recentData, isLoadingRecents: false });
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Bilinmeyen bir hata oluştu.';
        set({ error: errorMessage, isLoadingRecents: false });
    }
  },

  recordActivity: async (bookId: string) => {
    const userId = useAuthStore.getState().user?.id;
    if (!userId) return;

    try {
      // Arka planda aktiviteyi kaydet, UI'ı bekletme
      await contentInteractionService.recordActivity(userId, bookId);
      // Aktivite kaydedildikten sonra listeyi sessizce yenile
      get().fetchRecentActivities();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Aktivite kaydedilirken hata oluştu.';
      set({ error: errorMessage });
    }
  },

  clearInteractions: () => {
    set({
      favorites: [],
      recentActivities: [],
      isLoadingFavorites: false,
      isLoadingRecents: false,
      error: null,
    });
  },

  clearError: () => {
    set({ error: null });
  },
}));
