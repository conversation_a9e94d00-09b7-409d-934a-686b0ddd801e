import React, { useState, useEffect, useMemo, useRef } from 'react';
import { X, Plus, Edit3, Trash2, Calendar, FileText, AlertTriangle, Users, User, ArrowUpDown } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { useAuthStore } from '@domains/auth/store/authStore';
import { AnnotationService } from '@domains/reader-interactions/services/AnnotationService';
import { useAnnotationManager } from '@domains/reader-interactions/annotations/hooks/useAnnotationManager';
import type { Annotation } from '@domains/reader-interactions/shared/types';
import { supabase } from '@shared/utils/supabaseClient';
import { LoadingState, EmptyState } from '@shared/components';
import { AnnotationErrorBoundary } from '@domains/reader-interactions/shared/components/AnnotationErrorBoundary';

// --- Helper Components for Public Notes List ---

const TruncatedText = ({ text, maxLength = 350 }: { text: string | null; maxLength?: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  if (!text) return null;
  if (text.length <= maxLength) {
    return <p className="text-sm whitespace-pre-wrap text-[var(--text-color)]">{text}</p>;
  }
  return (
    <>
      <p className="text-sm whitespace-pre-wrap text-[var(--text-color)]">
        {isExpanded ? text : `${text.substring(0, maxLength)}...`}
      </p>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="text-sm font-semibold text-[var(--color-primary)] mt-2 hover:underline"
      >
        {isExpanded ? 'Daha az göster' : 'Devamını oku'}
      </button>
    </>
  );
};

const getUserDisplay = (profile: { username?: string; display_name?: string } | undefined, userId: string) => {
  if (!profile) return `...${userId.slice(-6)}`;
  const { display_name, username } = profile;
  if (display_name && username) return `${display_name} @${username}`;
  if (username) return `@${username}`;
  return display_name || `...${userId.slice(-6)}`;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('tr-TR', { year: 'numeric', month: 'long', day: 'numeric' });
};

const PublicAnnotationCard = ({ annotation, profile, cardBgColor, borderColor }: {
  annotation: Annotation;
  profile: { username?: string; display_name?: string } | undefined;
  cardBgColor: string;
  borderColor: string;
}) => {
  const userDisplay = getUserDisplay(profile, annotation.user_id);
  return (
    <div className="p-4 rounded-lg" style={{ backgroundColor: cardBgColor }}>
      <div className="flex items-center justify-between mb-3 pb-2 border-b" style={{ borderColor }}>
        <div className="flex items-center gap-1.5 text-xs text-[var(--text-color)]/60">
          <User size={14} />
          <span>{userDisplay}</span>
        </div>
        <div className="flex items-center gap-1.5 text-xs text-[var(--text-color)]/60">
          <Calendar size={14} />
          <span>{formatDate(annotation.created_at)}</span>
        </div>
      </div>
      <TruncatedText text={annotation.annotation_content || null} />
    </div>
  );
};

// --- Main Component ---

interface NotesListSheetProps {
  isOpen: boolean;
  onClose: () => void;
  verseKey: string;
  surahName?: string;
  onAddNote: () => void;
  onEditNote: (annotation: Annotation) => void; 
}

type ActiveTab = 'my_notes' | 'public_notes';
type SortOption = 'newest' | 'oldest';

export const NotesListSheet: React.FC<NotesListSheetProps> = ({
  isOpen,
  onClose,
  verseKey,
  surahName,
  onAddNote,
  onEditNote
}) => {
  const [myNotes, setMyNotes] = useState<Annotation[]>([]);
  const [publicNotes, setPublicNotes] = useState<Annotation[]>([]);
  const [loading, setLoading] = useState(true);
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<ActiveTab>('my_notes');
  const [publicNotesLoading, setPublicNotesLoading] = useState(false);
  const [hasFetchedPublicNotes, setHasFetchedPublicNotes] = useState(false);
  
  // State for public notes
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [userProfiles, setUserProfiles] = useState<Record<string, { username?: string; display_name?: string }>>({});
  const fetchedUserIds = useRef<Set<string>>(new Set());

  const sheetBg = 'var(--bg-color)';
  const borderColor = useAutoOverlay(15, 'var(--bg-color)');
  const cardBg = useAutoOverlay(5, 'var(--bg-color)');
  const hoverBg = useAutoOverlay(8, 'var(--bg-color)');

  const { user } = useAuthStore();
  const { deleteAnnotation, loading: isProcessing } = useAnnotationManager();

  const loadMyNotes = async () => {
    setLoading(true);
    try {
      const myNotesData = await AnnotationService.getNotes(verseKey, user?.id);
      setMyNotes(myNotesData);
    } catch (error) {
      console.error('❌ NotesListSheet - Kişisel notlar yüklenirken hata:', error);
      setMyNotes([]);
    } finally {
      setLoading(false);
    }
  };

  const loadPublicNotes = async () => {
    if (hasFetchedPublicNotes) return; // Zaten yüklendiyse tekrar yükleme
    setPublicNotesLoading(true);
    try {
      const publicNotesData = await AnnotationService.getNotes(verseKey, undefined, user?.id);
      setPublicNotes(publicNotesData);
      setHasFetchedPublicNotes(true); // Yüklendi olarak işaretle
    } catch (error) {
      console.error('❌ NotesListSheet - Herkese açık notlar yüklenirken hata:', error);
      setPublicNotes([]);
    } finally {
      setPublicNotesLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && verseKey) {
      // Sheet açıldığında sadece kişisel notları yükle
      loadMyNotes();
    } else if (!isOpen) {
      // Reset states when closing
      setActiveTab('my_notes');
      setMyNotes([]);
      setPublicNotes([]);
      setHasFetchedPublicNotes(false); // Tekrar açıldığında public notların yüklenebilmesi için sıfırla
    }
  }, [isOpen, verseKey, user]);

  // Herkese açık notlar sekmesi açıldığında verileri yükle
  useEffect(() => {
    if (isOpen && activeTab === 'public_notes' && !hasFetchedPublicNotes) {
      loadPublicNotes();
    }
  }, [isOpen, activeTab, hasFetchedPublicNotes]);

  // Fetch profiles for public notes
  useEffect(() => {
    const fetchProfiles = async () => {
      const uniqueUserIds = Array.from(new Set(publicNotes.map(a => a.user_id)));
      const toFetch = uniqueUserIds.filter(id => !fetchedUserIds.current.has(id));
      if (toFetch.length === 0) return;
      
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, display_name')
        .in('id', toFetch);
      
      if (error) {
        console.error("Error fetching user profiles:", error);
      } else if (data) {
        const newProfiles: Record<string, { username?: string; display_name?: string }> = {};
        for (const p of data) {
          newProfiles[p.id] = { username: p.username, display_name: p.display_name };
          fetchedUserIds.current.add(p.id);
        }
        setUserProfiles(prev => ({ ...prev, ...newProfiles }));
      }
    };
    if (activeTab === 'public_notes' && publicNotes.length > 0) {
      fetchProfiles();
    }
  }, [activeTab, publicNotes]);

  const sortedPublicNotes = useMemo(() => {
    return [...publicNotes].sort((a, b) => {
      if (sortBy === 'newest') {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
      return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
    });
  }, [publicNotes, sortBy]);

  const handleDeleteNote = async (noteId: string) => {
    const success = await deleteAnnotation(noteId);
    if (success) {
      setMyNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));
      setNoteToDelete(null); 
    } else {
      setNoteToDelete(null);
    }
  };

  const formatDateForMyNotes = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric', month: 'short', year: 'numeric', hour: '2-digit', minute: '2-digit'
    });
  };

  if (!isOpen) return null;

  const [surahNum, verseNum] = verseKey ? verseKey.split('-') : [];
  const selectedText = verseNum 
    ? `${surahName || `Sûre ${surahNum}`}, ${verseNum}. Ayet`
    : 'Ayet bilgisi yükleniyor...';

  const renderMyNotes = () => (
    loading ? (
      <LoadingState message="Notlarınız yükleniyor..." />
    ) : myNotes.length > 0 ? (
      <div className="space-y-3">
        {myNotes.map((note) => (
          <div key={note.id} className="p-4 rounded-xl transition-all duration-200" style={{ backgroundColor: cardBg }}>
            <div className="flex justify-between items-start gap-3 group">
              <p className="flex-1 text-sm leading-relaxed whitespace-pre-wrap" style={{ color: 'var(--text-color)' }}>
                {note.annotation_content}
              </p>
              <div className="flex space-x-0.5 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button onClick={() => onEditNote(note)} className="p-2 rounded-full text-[var(--text-color)]/60 hover:text-blue-500 hover:bg-blue-500/10 transition-all duration-200"><Edit3 size={14} /></button>
                <button onClick={() => setNoteToDelete(note.id)} className="p-2 rounded-full text-[var(--text-color)]/60 hover:text-red-500 hover:bg-red-500/10 transition-all duration-200"><Trash2 size={14} /></button>
              </div>
            </div>
            <div className="flex items-center text-xs opacity-50 mt-3 pt-3 border-t" style={{ borderColor: hoverBg }}>
              <Calendar size={12} className="mr-1.5" />
              {formatDateForMyNotes(note.created_at)}
              {note.updated_at && note.updated_at !== note.created_at && (
                <span className="ml-2 opacity-80">(düzenlendi)</span>
              )}
            </div>
          </div>
        ))}
      </div>
    ) : (
      <EmptyState
        message="Bu ayetle ilgili ilk notu siz ekleyin."
      />
    )
  );

  const renderPublicNotes = () => (
    publicNotesLoading ? (
      <LoadingState message="Herkese açık notlar yükleniyor..." />
    ) : (
      <>
        <div className="flex items-center justify-between text-sm mb-4 px-2">
          <span className="text-[var(--text-color)]/70">
            {`${sortedPublicNotes.length} sonuç bulundu`}
          </span>
          <button
            onClick={() => setSortBy(prev => prev === 'newest' ? 'oldest' : 'newest')}
            className="flex items-center gap-2 text-sm p-2 rounded-lg hover:bg-[var(--text-color)]/10 transition-colors"
          >
            <ArrowUpDown size={14} className="text-[var(--text-color)]/60" />
            <span className="text-[var(--text-color)] font-medium">
              {sortBy === 'newest' ? 'Önce en yeni' : 'Önce en eski'}
            </span>
          </button>
        </div>
        {sortedPublicNotes.length === 0 ? (
          <EmptyState
            message="Bu ayet için henüz herkese açık bir not paylaşılmamış."
          />
        ) : (
          <AnnotationErrorBoundary>
            <div className="space-y-3">
              {sortedPublicNotes.map(annotation =>
                <PublicAnnotationCard
                  key={annotation.id}
                  annotation={annotation}
                  profile={userProfiles[annotation.user_id]}
                  cardBgColor={cardBg}
                  borderColor={hoverBg}
                />
              )}
            </div>
          </AnnotationErrorBoundary>
        )}
      </>
    )
  );

  return (
    <>
      <div className="fixed inset-0 bg-black/40 backdrop-blur-sm z-50" onClick={onClose} />

      {noteToDelete && (
        <div className="fixed inset-0 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-sm rounded-2xl p-6 shadow-2xl border" style={{ backgroundColor: sheetBg, borderColor }}>
            <div className="flex flex-col items-center text-center">
              <div className="w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center mb-4"><AlertTriangle className="w-6 h-6 text-red-500" /></div>
              <h3 className="text-lg font-semibold mb-2 text-[var(--text-color)]">Notu Sil</h3>
              <p className="text-sm text-[var(--text-color)]/70 mb-6">Bu notu kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
              <div className="w-full flex gap-3">
                <button onClick={() => setNoteToDelete(null)} className="flex-1 px-4 py-2.5 rounded-lg text-sm font-medium transition-colors" style={{ backgroundColor: hoverBg }} disabled={isProcessing}>İptal</button>
                <button onClick={() => handleDeleteNote(noteToDelete)} className="flex-1 px-4 py-2.5 rounded-lg text-sm font-medium bg-red-600 text-white hover:bg-red-700 transition-colors flex items-center justify-center" disabled={isProcessing}>
                  {isProcessing ? <div className="w-4 h-4 border-2 border-white/50 border-t-white rounded-full animate-spin" /> : 'Sil'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div
        className="fixed z-50 bottom-0 left-0 right-0 md:bottom-auto md:top-1/2 md:left-1/2 md:transform md:-translate-x-1/2 md:-translate-y-1/2 md:w-[520px] md:max-h-[90vh] md:rounded-3xl rounded-t-3xl shadow-2xl md:border flex flex-col max-h-[92vh] h-[80vh] md:h-[650px]"
        style={{ backgroundColor: sheetBg, borderColor }}
      >
        <div className="flex items-center gap-3 px-5 py-4">
          <div className="w-9 h-9 rounded-lg flex items-center justify-center relative overflow-hidden" style={{ backgroundColor: 'var(--text-color)/10' }}>
            <FileText size={16} style={{ color: 'var(--text-color)' }} />
          </div>
          <div className="flex-1 min-w-0">
            <h2 className="text-base font-medium text-[var(--text-color)]">Ayet Notları</h2>
            <p className="text-xs opacity-50 truncate text-[var(--text-color)]">{selectedText}</p>
          </div>
          <button onClick={onClose} className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-red-500/10 text-[var(--text-color)] hover:text-red-500 transition-all duration-200 group">
            <X size={14} className="group-hover:scale-110 transition-transform" />
          </button>
        </div>

        <div className="flex-1 flex flex-col p-4 pt-2 md:p-6 md:pt-2 min-h-0">
          <div className="flex mb-4 p-1 rounded-lg" style={{ backgroundColor: cardBg }}>
            <button onClick={() => setActiveTab('my_notes')} className={`flex-1 flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${activeTab === 'my_notes' ? 'shadow' : ''}`} style={{ backgroundColor: activeTab === 'my_notes' ? sheetBg : 'transparent', color: 'var(--text-color)' }}>
              <User size={14} /> Notlarım
            </button>
            <button onClick={() => setActiveTab('public_notes')} className={`flex-1 flex items-center justify-center gap-2 py-2 text-sm font-medium rounded-md transition-all ${activeTab === 'public_notes' ? 'shadow' : ''}`} style={{ backgroundColor: activeTab === 'public_notes' ? sheetBg : 'transparent', color: 'var(--text-color)' }}>
              <Users size={14} /> Herkese Açık
            </button>
          </div>

          <div className="flex-1 rounded-xl relative overflow-y-auto">
            {loading ? (
              <LoadingState message="Notlarınız yükleniyor..." />
            ) : (
              activeTab === 'my_notes' ? renderMyNotes() : renderPublicNotes()
            )}
          </div>
          
          {activeTab === 'my_notes' && (
            <div className="pt-4 mt-auto">
              <button
                onClick={onAddNote}
                className="w-full px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
                style={{ backgroundColor: 'var(--text-color)', color: 'var(--bg-color)', boxShadow: `0 6px 20px color-mix(in srgb, var(--text-color) 25%, transparent)` }}
              >
                <Plus size={18} />
                <span>Yeni Not Ekle</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
