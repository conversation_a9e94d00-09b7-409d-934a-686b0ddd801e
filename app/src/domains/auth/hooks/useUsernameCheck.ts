import { useState, useRef, useEffect, useCallback } from 'react';
import { authService } from '../services/authService';
import debounce from 'lodash/debounce';

/**
 * Kullanıcı adı kontrolü için custom hook
 * 
 * @param initialUsername - Başlangıçta kullanıcı adı (@ olmadan)
 * @param originalUsername - Kullanıcının mevcut kullanıcı adı (profil düzenleme için, @ olmadan)
 * @param debounceMs - Kontrol gecikmesi (ms)
 */
export const useUsernameCheck = (
  initialUsername: string = '',
  originalUsername: string = '',
  debounceMs: number = 800
) => {
  const [username, setUsername] = useState(initialUsername);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(null);
  const [usernameError, setUsernameError] = useState<string | null>(null);
  
  // Son kontrolü yapılan değeri takip etmek için ref
  const lastCheckedUsername = useRef(username);
  
  // Kullanıcı adı format kontrolü (uzunluk, karakter, boşluk vb.)
  const validateUsernameFormat = useCallback((value: string): string | null => {
    if (!value) return 'Kullanıcı adı gereklidir';
    
    // Uzunluk kontrolü - minimum 4 karakter (önceki 3'tü)
    if (value.length < 4) {
      return 'Kullanıcı adı en az 4 karakter olmalıdır';
    }
    
    // Boşluk kontrolü
    if (/\s/.test(value)) {
      return 'Kullanıcı adı boşluk içeremez';
    }
    
    // Sadece alfanumerik ve alt çizgi kontrolü
    if (!/^[a-zA-Z0-9_]+$/.test(value)) {
      return 'Kullanıcı adı sadece harf, rakam ve alt çizgi (_) içerebilir';
    }
    
    return null;
  }, []);
  
  // Kullanıcı adı kontrolü
  const checkUsername = useCallback(async (value: string) => {
    if (!value || value.trim() === '') {
      setUsernameAvailable(null);
      setUsernameError('Kullanıcı adı gereklidir');
      return;
    }
    
    const formatError = validateUsernameFormat(value);
    if (formatError) {
      setUsernameAvailable(null);
      setUsernameError(formatError);
      return;
    }
    
    // Kullanıcı kendi mevcut kullanıcı adını değiştirmiyorsa kontrol etmeye gerek yok
    if (value === originalUsername) {
      setUsernameAvailable(true);
      setUsernameError(null);
      return;
    }
    
    // Zaten kontrol ediliyorsa çıkın
    if (isCheckingUsername) return;
    
    // Kontrol başlasın
    setIsCheckingUsername(true);
    try {
      const result = await authService.checkUsernameAvailability(value);
      
      setUsernameAvailable(!result.exists);
      setUsernameError(result.exists ? 'Bu kullanıcı adı zaten kullanılıyor' : null);
      
      // Son kontrol edilen değeri güncelle
      lastCheckedUsername.current = value;
    } catch (error) {
      console.error('Username check error:', error);
      setUsernameAvailable(null);
      setUsernameError('Kullanıcı adı kontrolünde hata oluştu');
    } finally {
      setIsCheckingUsername(false);
    }
  }, [isCheckingUsername, originalUsername, validateUsernameFormat]);
  
  // Debounce ile kullanıcı adı kontrolü
  const debouncedCheckUsername = useRef(
    debounce((value: string) => {
      checkUsername(value);
    }, debounceMs)
  ).current;
  
  // Kullanıcı adı değiştiğinde işleme
  const handleUsernameChange = useCallback((e: React.ChangeEvent<HTMLInputElement> | string) => {
    // Event veya string olarak kullanmayı destekle
    const value = typeof e === 'string' ? e : e.target.value;
    
    // @ işaretlerini temizle
    const cleanedValue = value.replace(/^@/, '').replace(/@/g, '');
    setUsername(cleanedValue);
    
    // Format kontrolü
    const formatError = validateUsernameFormat(cleanedValue);
    setUsernameError(formatError);
    
    if (!formatError && cleanedValue) {
      // Eğer format doğruysa ve son kontrol edilen değerden farklıysa tekrar kontrol et
      if (cleanedValue !== lastCheckedUsername.current) {
        debouncedCheckUsername(cleanedValue);
      }
    } else {
      setUsernameAvailable(null);
    }
  }, [debouncedCheckUsername, validateUsernameFormat]);
  
  // Formun gönderilmesi sırasında değerin uygun olup olmadığını kontrol et
  const isUsernameValid = useCallback(() => {
    // Değer boşsa veya format geçersizse
    if (!username || validateUsernameFormat(username)) {
      return false;
    }
    
    // Kullanıcı kendi adını kullanıyorsa
    if (username === originalUsername) {
      return true;
    }
    
    // Değer alınamaz durumdaysa
    if (usernameAvailable === false) {
      return false;
    }
    
    // Kontrol hala devam ediyorsa
    if (isCheckingUsername) {
      return false;
    }
    
    return usernameAvailable === true;
  }, [username, originalUsername, usernameAvailable, isCheckingUsername, validateUsernameFormat]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      debouncedCheckUsername.cancel();
    };
  }, [debouncedCheckUsername]);
  
  return {
    username,                  // Kullanıcı adı (temiz, @ olmadan)
    setUsername,               // Kullanıcı adını manuel ayarla
    isCheckingUsername,        // Kontrol yapılıyor mu?
    usernameAvailable,         // Kullanıcı adı uygun mu?
    usernameError,             // Kullanıcı adı hatası (eğer varsa)
    handleUsernameChange,      // Input değişimini işleyecek fonksiyon
    isUsernameValid,           // Kullanıcı adı geçerli mi?
    formattedUsername: username ? `@${username}` : '',  // @ ile formatlanmış kullanıcı adı
  };
}; 