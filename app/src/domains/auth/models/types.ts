import { User } from '@supabase/supabase-js';

export interface IAuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
}

export interface ILoginData {
  email: string;
  password: string;
}

export interface ISignupData {
  email: string;
  password: string;
  confirmPassword: string;
  username?: string; // @kullaniciadi formatında benzersiz tanımlayıcı
  displayName?: string; // Görünen isim (ad-soyad)
}

export interface IAuthFormState {
  email: string;
  password: string;
  confirmPassword?: string;
  username?: string; // @kullaniciadi formatında benzersiz tanımlayıcı
  displayName?: string; // Görünen isim (ad-soyad)
  errors: {
    email?: string;
    password?: string;
    confirmPassword?: string;
    username?: string;
    displayName?: string;
    form?: string;
  };
  isSubmitting: boolean;
}

export interface IUserMetadata {
  username?: string; // @kullaniciadi formatında benzersiz tanımlayıcı
  displayName?: string; // Görünen isim (ad-soyad)
} 