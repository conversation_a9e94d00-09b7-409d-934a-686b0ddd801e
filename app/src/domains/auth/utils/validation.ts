import { USERNAME_VALIDATION } from '../constants/ui';

export const validateUsername = (username: string): string | null => {
  if (!username) {
    return '<PERSON><PERSON>ıcı adı gereklidir';
  }
  
  if (username.length < USERNAME_VALIDATION.minLength) {
    return USERNAME_VALIDATION.errorMessages.tooShort;
  }
  
  if (username.length > USERNAME_VALIDATION.maxLength) {
    return USERNAME_VALIDATION.errorMessages.tooLong;
  }
  
  if (!USERNAME_VALIDATION.pattern.test(username)) {
    return USERNAME_VALIDATION.errorMessages.invalidChars;
  }
  
  return null;
};

export const validateEmail = (email: string): string | null => {
  if (!email) {
    return 'E-posta adresi gereklidir';
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return 'G<PERSON><PERSON><PERSON><PERSON> bir e-posta adresi girin';
  }
  
  return null;
};

export const validatePassword = (password: string): string | null => {
  if (!password) {
    return '<PERSON><PERSON><PERSON> gereklidir';
  }
  
  if (password.length < 6) {
    return 'Şifre en az 6 karakter olmalıdır';
  }
  
  return null;
};

export const validatePasswordConfirmation = (password: string, confirmPassword: string): string | null => {
  if (!confirmPassword) {
    return 'Şifre tekrarı gereklidir';
  }
  
  if (password !== confirmPassword) {
    return 'Şifreler eşleşmiyor';
  }
  
  return null;
};

export const validateDisplayName = (displayName: string): string | null => {
  if (!displayName?.trim()) {
    return 'Görünen ad gereklidir';
  }
  
  if (displayName.trim().length < 2) {
    return 'Görünen ad en az 2 karakter olmalıdır';
  }
  
  return null;
};
