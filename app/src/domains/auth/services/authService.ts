import { supabase, supabaseUrl, supabaseAnonKey } from '@shared/utils/supabaseClient';
import { ILoginData, ISignupData } from '../models/types';
import { IUserProfile } from '../store/authStore';
import type { User } from '@supabase/supabase-js';

// Artık store'dan import ettiğimiz IUserProfile'ı kullanacağız
// export interface IUserProfile extends IUserMetadata {
//   id: string; // UUID
//   updated_at?: string;
//   avatar_url?: string;
// }

export const authService = {
  // Kullanıcı girişi
  async login({ email, password }: ILoginData) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) throw error;
    return data.user;
  },
  
  // Google ile giriş
  async signInWithGoogle() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: window.location.origin, // Uygulama kök URL'ine dön
        queryParams: {
          // Google'dan dönüşte profil kurulumunun gerekli olacağını belirtecek parametre
          prompt: 'consent', // Her zaman izin iste, bu şekilde her zaman taze bilgiler alırız
          access_type: 'offline', // Refresh token almak için gerekli
          include_granted_scopes: 'true', // Verilen tüm izinleri dahil et
          // Google'dan daha fazla bilgi almak için scope'ları genişletelim
          scope: 'openid email profile',
        }
      },
    });
    
    if (error) throw error;
    
    // Artık Google OAuth yönlendirmesi başlatıldı, geri kalan işlem callback'te yapılacak
    // useAuth hook'u dönüş sırasında username kontrol edecek ve isUsernameSetupRequired bayrağını ayarlayacak
    return data;
  },
  
  // Kullanıcı adı gerekli mi kontrol et (yeni Google ile giriş yapan kullanıcılar için)
  async isUsernameRequired() {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) return false;
    
    const profile = await this.getUserProfile();
    return !!userData.user && (!profile?.username || profile.username === '');
  },
  
  // Kullanıcı adı validasyonu (isteğe bağlı input parametresi)
  validateUsername(username: string): { isValid: boolean; error?: string } {
    // @ işaretini temizle
    const cleanedUsername = username.startsWith('@') ? username.substring(1) : username;
    
    // Boş kontrolü
    if (!cleanedUsername || cleanedUsername.trim() === '') {
      return { isValid: false, error: 'Kullanıcı adı boş olamaz' };
    }
    
    // Uzunluk kontrolü
    if (cleanedUsername.length < 3) {
      return { isValid: false, error: 'Kullanıcı adı en az 3 karakter olmalıdır' };
    }
    
    // Boşluk kontrolü
    if (/\s/.test(cleanedUsername)) {
      return { isValid: false, error: 'Kullanıcı adı boşluk içeremez' };
    }
    
    // Karakter kontrolü (sadece alfanumerik ve alt çizgi)
    if (!/^[a-zA-Z0-9_]+$/.test(cleanedUsername)) {
      return { isValid: false, error: 'Kullanıcı adı sadece harf, rakam ve alt çizgi (_) içerebilir' };
    }
    
    return { isValid: true };
  },
  
  // Google girişinden sonra kullanıcı adı kaydet
  async submitUsername(username: string, displayName: string) {
    const { data: userData } = await supabase.auth.getUser();
    if (!userData?.user) throw new Error('Kullanıcı oturum açmamış');
    
    // Username validasyonu
    const validation = this.validateUsername(username);
    if (!validation.isValid) {
      throw new Error(validation.error || 'Geçersiz kullanıcı adı');
    }
    
    // Boşlukları temizle (ekstra garanti olarak)
    const cleanUsername = username.replace(/\s+/g, '');
    
    // Profili güncelle
    return this.updateProfile(userData.user.id, {
      username: cleanUsername,
      displayName
    });
  },
  
  // Yeni kullanıcı kaydı (Metadata'yı trigger kullanacak)
  async signup({ email, password, username, displayName }: ISignupData) {
    // Username kontrolü ve validasyonu yap (eğer username belirtilmişse)
    if (username) {
      const validation = this.validateUsername(username);
      if (!validation.isValid) {
        throw new Error(validation.error || 'Geçersiz kullanıcı adı');
      }
    }
    
    // Boşlukları temizle
    const cleanUsername = username ? username.replace(/\s+/g, '') : undefined;
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          // Trigger bu verileri alıp profiles tablosuna yazacak
          username: cleanUsername, 
          display_name: displayName || '',
          updated_at: new Date().toISOString() // Güncel tarih ekleyelim
        },
      },
    });
    
    if (error) throw error;
    
    // Kullanıcı oluşturulduktan sonra profiles tablosuna da eklemeliyiz (Trigger çalışmama ihtimaline karşı)
    try {
      const user = data.user;
      if (user) {
        // İşlem zaman alabilir, bu nedenle kısa bir bekleme ekliyoruz
        setTimeout(async () => {
          try {
            // Önce profiles tablosunda bu kullanıcı var mı kontrol edelim
            const { data: profileData, error: profileCheckError } = await supabase
              .from('profiles')
              .select('id')
              .eq('id', user.id)
              .single();
              
            if (profileCheckError || !profileData) {
              // Profil yoksa oluşturalım
              console.log('[authService] Creating new profile record after signup:', user.id);
              
              await supabase
                .from('profiles')
                .insert({
                  id: user.id,
                  username: cleanUsername,
                  display_name: displayName || '',
                  updated_at: new Date().toISOString()
                });
            }
          } catch (err) {
            console.error('[authService] Error creating profile after signup:', err);
            // Bu hata kritik değil, kullanıcı yine de oluşturuldu
          }
        }, 1000); // 1 saniye bekle
      }
    } catch (profileError) {
      console.error('[authService] Exception handling profile creation:', profileError);
      // Bu hata kritik değil, kullanıcı yine de oluşturuldu
    }
    
    return data.user;
  },
  
  // Kullanıcı profilini güncelle (profiles tablosuna yaz)
  async updateProfile(userId: string, profileData: { username?: string, displayName?: string }) {
    const updateData: { username?: string | null, display_name?: string, updated_at?: string } = {};
    
    if (profileData.username !== undefined) {
      // Username validasyonu (boş değilse)
      if (profileData.username) {
        const validation = this.validateUsername(profileData.username);
        if (!validation.isValid) {
          throw new Error(validation.error || 'Geçersiz kullanıcı adı');
        }
      }
      
      // Boşlukları temizle
      updateData.username = profileData.username ? profileData.username.replace(/\s+/g, '') : null;
    }
    
    if (profileData.displayName !== undefined) {
      updateData.display_name = profileData.displayName;
    }
    
    if (Object.keys(updateData).length > 0) {
      updateData.updated_at = new Date().toISOString();
      
      console.log('[authService] Updating profile in database:', updateData);
      
      // 1. Profiles tablosunu güncelle
      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);
        
      if (error) {
        // Eğer profil bulunamadıysa, yeni bir kayıt oluştur
        if (error.code === '23505' || error.message?.includes('not found')) {
          console.log('[authService] Profile not found, creating new record');
          
          const { error: insertError } = await supabase
            .from('profiles')
            .insert({
              id: userId,
              ...updateData
            });
            
          if (insertError) {
            console.error("[authService] Error inserting profile:", insertError);
            throw insertError;
          }
        } else {
          console.error("[authService] Error updating profile:", error);
          throw error;
        }
      }
      
      // 2. Ayrıca auth metadata'sını güncelle - bu sayede Google login sonrası da metadata güncel olur
      try {
        const metadata: Record<string, string> = {};
        
        if (updateData.username) {
          metadata.username = updateData.username;
        }
        
        if (updateData.display_name) {
          metadata.display_name = updateData.display_name;
        }
        
        if (Object.keys(metadata).length > 0) {
          console.log('[authService] Updating auth metadata:', metadata);
          
          // User metadata güncelle
          const { error: metadataError } = await supabase.auth.updateUser({
            data: metadata
          });
          
          if (metadataError) {
            console.error("[authService] Error updating auth metadata:", metadataError);
            // Bu hata kritik değil, devam edebiliriz
          }
        }
      } catch (metadataError) {
        console.error("[authService] Exception updating auth metadata:", metadataError);
        // Bu hata kritik değil, devam edebiliriz
      }
    }
    
    return true;
  },
  
  // Kullanıcı profilini al (profiles tablosundan oku)
  async getUserProfile(): Promise<IUserProfile | null> {
    try {
      // Önce Supabase auth bilgilerini alalım
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError || !userData.user) {
        console.error('User data fetch error:', userError);
        return null;
      }
      
      // User metadata'dan profil bilgilerini al
      return {
        id: userData.user.id,
        username: userData.user.user_metadata?.username,
        displayName: userData.user.user_metadata?.display_name || userData.user.user_metadata?.displayName,
        avatar_url: userData.user.user_metadata?.avatar_url,
      };
    } catch (error) {
      console.error('Error fetching profile:', error);
      return null;
    }
  },
  
  // Kullanıcı adı uygunluk kontrolü
  async checkUsernameAvailability(username: string): Promise<{ exists: boolean; error?: string }> {
    try {
      // @ işaretini kaldır (varsa)
      const cleanedUsername = username.startsWith('@') ? username.substring(1) : username;
      
      // Edge fonksiyonuna API isteği gönder
      const response = await fetch(`${supabaseUrl}/functions/v1/check-username`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseAnonKey}`
        },
        body: JSON.stringify({ username: cleanedUsername })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Username check API error:', errorData);
        return { exists: false, error: errorData.error || 'Kullanıcı adı kontrol edilemedi' };
      }
      
      const result = await response.json();
      return { exists: result.exists };
    } catch (error) {
      console.error('Error checking username availability:', error);
      return { exists: false, error: 'Kullanıcı adı kontrol edilemedi' };
    }
  },
  
  // Çıkış yapma
  async logout() {
    try {
      console.log('[authService] Logging out user');
      
      // 1. Önce Supabase ile session sonlandırma - bu işlem token bazlı olduğu için önce yapılması daha iyi
      const { error } = await supabase.auth.signOut({
        scope: 'local', // Sadece bu cihazda çıkış yap
      });
      
      if (error) {
        console.error('[authService] Error during Supabase signOut:', error);
      }
      
      // 2. Bilinen tüm auth anahtarlarını localStorage'dan temizle
      try {
        const KNOWN_AUTH_KEYS = [
          'supabase.auth.token',
          'sb-refresh-token',
          'sb-access-token',
          'ikra-kitabe-auth',
          'ikra-auth-storage',
          'supabase.auth.expires_at',
          'sb-provider-token'
        ];
        
        KNOWN_AUTH_KEYS.forEach(key => {
          try {
            localStorage.removeItem(key);
            console.log(`[authService] Removed specific auth key: ${key}`);
          } catch (e) {
            console.warn(`[authService] Unable to remove key: ${key}`, e);
          }
        });
        
        // 3. Daha kapsamlı bir temizlik - bütün localStorage'ı tara
        Object.keys(localStorage).forEach(key => {
          if (
            key.includes('supabase') || 
            key.includes('sb-') || 
            key.includes('ikra-') ||
            key.includes('auth')
          ) {
            console.log(`[authService] Removing pattern-matched auth key: ${key}`);
            localStorage.removeItem(key);
          }
        });
        
        // 4. Temiz bir session başlatmak için sessionStorage da temizle
        sessionStorage.clear();
        
      } catch (storageError) {
        console.warn('[authService] Error clearing localStorage during logout:', storageError);
        // Kritik bir hata değil, temizlik işlemine devam edebiliriz
      }
      
      console.log('[authService] Logout completed');
      
      // 5. Kesin temizlik için sayfayı yenile (çok agresif ama etkili)
      // Bu sadece gerçekten çözüm bulamazsak kullanılmalı
      // window.location.reload();
      
    } catch (error) {
      console.error('[authService] Exception during logout:', error);
      throw error;
    }
  },
  
  // Mevcut kullanıcı bilgisini getir (Auth user)
  async getCurrentUser() {
    try {
      console.log('[authService] Checking for current user...');
      
      // 1. Önce direkt localStorage'dan oturum kontrolü (hızlı)
      try {
        // Özel storage anahtarındaki verileri kontrol et
        const localAuth = localStorage.getItem('ikra-kitabe-auth');
        console.log('[authService] localStorage check:', { hasLocalAuth: !!localAuth });
        
        if (!localAuth) {
          console.log('[authService] No local auth data found in localStorage');
        }
      } catch (storageError) {
        console.warn('[authService] Error checking localStorage:', storageError);
      }
      
      // 2. Session kontrolü (daha güvenilir)
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.warn('[authService] Error getting session:', sessionError);
        // Session hatası, temiz başlangıç
        await this.logout();
        return null;
      }
      
      // Session yoksa hemen null dön
      if (!sessionData?.session) {
        console.log('[authService] No active session found in getSession call');
        return null;
      }
      
      console.log('[authService] Active session found:', { 
        expiresAt: sessionData.session.expires_at ? 
          new Date(sessionData.session.expires_at * 1000).toISOString() : 'unknown',
        userId: sessionData.session.user?.id
      });
      
      // 3. Token süresi kontrolü - 15 dakikadan az kaldıysa yenile
      if (sessionData.session.expires_at) {
        const expiresAt = new Date(sessionData.session.expires_at * 1000);
        const now = new Date();
        const timeLeftMs = expiresAt.getTime() - now.getTime();
        
        if (timeLeftMs < 15 * 60 * 1000) { // 15 dakikadan az
          console.log(`[authService] Session expires soon (${Math.floor(timeLeftMs/1000/60)} minutes left), refreshing...`);
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
          
          if (refreshError) {
            console.warn('[authService] Failed to refresh session:', refreshError);
          } else if (refreshData?.session) {
            console.log('[authService] Session refreshed successfully');
            // Yenilenen oturumdan kullanıcı döndür
            return refreshData.user;
          }
        }
      }
      
      // 4. User kontrolü
      const { data, error } = await supabase.auth.getUser();
      
      if (error) {
        console.error('[authService] Error getting user after session check:', error);
        
        // Çeşitli token/session hatalarını ele al
        if (error.message.includes('Invalid Refresh Token') || 
            error.message.includes('JWT expired') ||
            error.message.includes('Invalid JWT') ||
            error.message.includes('Auth session missing') ||
            error.name === 'AuthSessionMissingError') {
          
          console.warn('[authService] JWT/session error detected, attempting refresh');
          
          // Son bir token yenileme denemesi
          try {
            const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
            
            if (refreshError) {
              console.warn('[authService] Final refresh attempt failed:', refreshError);
              await this.logout();
              return null;
            }
            
            if (refreshData?.user) {
              console.log('[authService] Final refresh successful, returning user');
              return refreshData.user;
            }
          } catch (refreshError) {
            console.error('[authService] Exception during final refresh:', refreshError);
          }
          
          // Yenileme başarısız, temiz çıkış
          await this.logout();
          return null;
        }
        
        // Diğer hatalar
        throw error;
      }
      
      // Kullanıcı başarıyla alındı
      if (data?.user) {
        console.log('[authService] Current user found:', data.user.id);
      } else {
        console.log('[authService] No user found in getUser response despite session');
      }
      
      return data?.user || null;
    } catch (error) {
      console.error('[authService] Unexpected error in getCurrentUser:', error);
      
      // AuthSessionMissingError için özel işlem
      if (error instanceof Error && 
          (error.name === 'AuthSessionMissingError' || 
          error.message.includes('Auth session missing'))) {
        console.warn('[authService] Auth session missing error, cleaning up');
        await this.logout();
        return null;
      }
      
      // Diğer beklenmeyen hatalar
      return null;
    }
  },
  
  // Auth durumunu dinle
  onAuthStateChange(callback: (user: User | null) => void) {
    const { data } = supabase.auth.onAuthStateChange((event, session) => {
      console.log(`[authService] Auth state changed: ${event}`);
      callback(session?.user || null);
    });
    
    return data.subscription;
  },
  
  // Şifre sıfırlama
  async resetPassword(email: string) {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      
      if (error) {
        // Özel hata mesajları
        switch(error.message) {
          case 'For security purposes, you can only request this once every 60 seconds':
            throw new Error('Güvenlik nedeniyle, her 60 saniyede bir kez bu işlemi yapabilirsiniz.');
          case 'Email rate limit exceeded':
            throw new Error('Çok fazla deneme yaptınız. Lütfen daha sonra tekrar deneyin.');
          case 'Email not found':
          case 'User not found':
            throw new Error('Bu e-posta adresi ile kayıtlı kullanıcı bulunamadı.');
          default:
            throw error;
        }
      }
    } catch (error) {
      console.error('Şifre sıfırlama hatası:', error);
      throw error;
    }
  },
};
