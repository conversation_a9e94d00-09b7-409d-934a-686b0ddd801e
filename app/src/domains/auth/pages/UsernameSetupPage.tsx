import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { Loader2, AlertCircle, Check } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { useUsernameCheck } from '../hooks/useUsernameCheck';

export const UsernameSetupPage: React.FC = () => {
  const navigate = useNavigate();
  const { 
    isAuthenticated, 
    isUsernameSetupRequired, 
    isLoading: storeIsLoading,
    error: storeError,
    submitUsername: storeSubmitUsername,
    profile,
    user
  } = useAuthStore();
  
  // Kullanıcı adı kontrolü için hook'u kullanıyoruz
  const {
    username,
    handleUsernameChange,
    isCheckingUsername,
    usernameAvailable,
    usernameError,
    isUsernameValid
  } = useUsernameCheck();
  
  const [displayName, setDisplayName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState({
    displayName: '',
    form: ''
  });
  
  // Stil değişkenleri
  const pageBgColor = useAutoOverlay(8, 'var(--bg-color)');
  const inputBgColor = useAutoOverlay(4, 'var(--bg-color)');
  
  // Google'dan gelen kullanıcı bilgilerini almak için
  useEffect(() => {
    if (profile?.displayName) {
      // Eğer profile'da displayName varsa direkt kullan
      setDisplayName(profile.displayName);
      console.log('[UsernameSetupPage] Display name from profile:', profile.displayName);
    } else if (user?.user_metadata) {
      // Kullanıcı metadatasından isim bilgisini alma
      const metaDisplayName = user.user_metadata.display_name || 
                              user.user_metadata.displayName || 
                              user.user_metadata.full_name || 
                              user.user_metadata.name || '';
      
      if (metaDisplayName) {
        setDisplayName(metaDisplayName);
        console.log('[UsernameSetupPage] Display name from user metadata:', metaDisplayName);
      } else {
        console.log('[UsernameSetupPage] No display name found in metadata');
      }
    }
  }, [profile, user]);
  
  // Authentication ve username durumunu kontrol et
  useEffect(() => {
    // Kullanıcı giriş yapmamışsa login sayfasına yönlendir
    if (!storeIsLoading && !isAuthenticated) {
      navigate('/giris');
    }
    
    // Username zaten ayarlanmışsa ana sayfaya yönlendir
    if (!storeIsLoading && isAuthenticated && !isUsernameSetupRequired) {
      navigate('/');
    }
  }, [isAuthenticated, isUsernameSetupRequired, storeIsLoading, navigate]);

  const handleDisplayNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setDisplayName(e.target.value);
    
    // Anlık hata kontrolü
    if (!e.target.value.trim()) {
      setFormErrors(prev => ({ ...prev, displayName: 'Ad Soyad gereklidir' }));
    } else if (e.target.value.length < 2) {
      setFormErrors(prev => ({ ...prev, displayName: 'Ad Soyad alanı en az 2 karakter olmalıdır' }));
    } else {
      setFormErrors(prev => ({ ...prev, displayName: '' }));
    }
  }, []);

  const validateForm = useCallback(() => {
    let isValid = true;
    
    // Kullanıcı adı validasyonu artık hook tarafından yapılıyor
    if (!isUsernameValid()) {
      isValid = false;
    }
    
    if (!displayName || displayName.length < 2) {
      setFormErrors(prev => ({ ...prev, displayName: 'Ad Soyad alanı en az 2 karakter olmalıdır' }));
      isValid = false;
    }
    
    return isValid;
  }, [displayName, isUsernameValid]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Yükleniyor durumunda veya kullanıcı adı kontrolü yapılıyorsa engelle
    if (isSubmitting || isCheckingUsername) return;
    
    // Form geçerli mi kontrol et
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    setFormErrors({ displayName: '', form: '' });
    
    try {
      // Store'daki submitUsername'i çağır - artık @ yok
      await storeSubmitUsername(username, displayName);
      // Başarılı submit sonrası `isUsernameSetupRequired` store'da false olacağı için useEffect yönlendirmeyi yapacak.
    } catch (err) {
      setFormErrors(prevErrors => ({ 
        ...prevErrors, 
        form: err instanceof Error ? err.message : 'Bir hata oluştu'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Yükleniyor durumları için gösterim
  if (!isAuthenticated || !isUsernameSetupRequired || storeIsLoading) {
    return (
      <div 
        className="flex flex-col items-center justify-center min-h-screen"
        style={{ backgroundColor: 'var(--bg-color)', color: 'var(--text-color)' }}
      >
        <div className="flex items-center space-x-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>
            {!isAuthenticated ? 'Giriş sayfasına yönlendiriliyor...' : 
             !isUsernameSetupRequired ? 'Ana sayfaya yönlendiriliyor...' : 
             'Kullanıcı durumu kontrol ediliyor...'}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="flex flex-col items-center justify-center min-h-screen"
      style={{ backgroundColor: 'var(--bg-color)' }}
    >
      {/* AuthSheet stiline benzer bir kartla kapsayalım */}
      <div
        className="w-full max-w-md p-8 rounded-2xl shadow-lg"
        style={{ 
          backgroundColor: pageBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
        }}
      >
        <div className="mb-6 text-center">
          <h2 className="text-xl font-bold" style={{ color: 'var(--text-color)' }}>
            Profili Tamamla
          </h2>
          <p className="mt-2 text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
            Devam etmek için bilgilerinizi belirleyin.
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-3.5">
          {/* Form hatası */}
          {(formErrors.form || storeError) && (
            <div className="bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400 px-4 py-3 rounded-xl text-sm">
              {formErrors.form || storeError}
            </div>
          )}
          
          {/* Kullanıcı adı alanı (SignupForm stili ile) */}
          <div className="space-y-1.5">
            <label 
              className="block text-sm font-medium"
              style={{ color: 'var(--text-color)' }}
              htmlFor="username"
            >
              Kullanıcı Adı
            </label>
            <div className="relative">
              <div 
                className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
                style={{ color: 'var(--text-color)' }}
              >
                @
              </div>
              <input
                id="username"
                name="username"
                type="text"
                value={username}
                onChange={handleUsernameChange}
                className={`w-full pl-8 pr-10 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border transition-all duration-200 ${
                  usernameError
                    ? 'focus:ring-red-500/30 border-red-500/50' 
                    : usernameAvailable === true
                      ? 'focus:ring-green-500/30 border-green-500/70 border-2'
                      : 'focus:ring-blue-500/30'
                }`}
                style={{ 
                  backgroundColor: inputBgColor,
                  borderColor: usernameError
                    ? 'var(--error-color, #ef4444)'
                    : usernameAvailable === true
                      ? 'var(--success-color, #22c55e)'
                      : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                  color: 'var(--text-color)',
                  boxShadow: usernameAvailable === true ? '0 0 0 1px rgba(34, 197, 94, 0.2)' : 'none'
                }}
                placeholder="kullaniciadi"
                required
                disabled={isCheckingUsername}
                aria-invalid={!!usernameError || usernameAvailable === false}
                pattern="^[a-zA-Z0-9_]+$"
                title="Kullanıcı adı sadece harf, rakam ve alt çizgi (_) içerebilir"
              />
              
              {/* Kullanıcı adı kontrol durumları */}
              {isCheckingUsername && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <Loader2 size={16} className="animate-spin text-blue-500" />
                </div>
              )}
              
              {!isCheckingUsername && usernameAvailable === true && username.length >= 3 && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <Check size={16} className="text-green-500" />
                </div>
              )}
              
              {!isCheckingUsername && (usernameAvailable === false || usernameError) && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <AlertCircle size={16} className="text-red-500" />
                </div>
              )}
            </div>
            
            {/* Kullanıcı adı mesajları */}
            {usernameError && (
              <p className="text-red-600 dark:text-red-400 text-xs mt-1">{usernameError}</p>
            )}
            
            {!usernameError && usernameAvailable === true && username && (
              <p className="text-green-600 dark:text-green-400 text-xs mt-1">
                <span className="flex items-center">
                  <Check size={12} className="mr-1" /> Bu kullanıcı adı kullanılabilir
                </span>
              </p>
            )}
            
            <p className="text-xs opacity-70" style={{ color: 'var(--text-color)' }}>
              <span className="flex items-start">
                <span className="mr-1">•</span>
                <span>Kullanıcı adınız <span className="font-medium">sadece harf, rakam ve alt çizgi (_) içerebilir</span></span>
              </span>
            </p>
          </div>
          
          {/* Görünen Ad alanı */}
          <div className="space-y-1.5">
            <label 
              className="block text-sm font-medium"
              style={{ color: 'var(--text-color)' }}
              htmlFor="displayName"
            >
              Görünen Ad
            </label>
            <div className="relative">
              <input
                id="displayName"
                name="displayName"
                type="text"
                value={displayName}
                onChange={handleDisplayNameChange}
                className={`w-full px-4 py-3 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-opacity-50 border ${
                  formErrors.displayName ? 'focus:ring-red-500/30 border-red-500/50' : 'focus:ring-blue-500/30'
                }`}
                style={{ 
                  backgroundColor: inputBgColor,
                  borderColor: formErrors.displayName ? 'var(--error-color, #ef4444)' : 'color-mix(in srgb, var(--text-color) 10%, transparent)',
                  color: 'var(--text-color)'
                }}
                placeholder="Adınız Soyadınız"
                required
              />
            </div>
            {formErrors.displayName && (
              <p className="text-red-600 dark:text-red-400 text-xs mt-1">{formErrors.displayName}</p>
            )}
          </div>

          {/* Submit butonu */}
          <div className="pt-2">
            <button
              type="submit"
              className="w-full py-3 px-4 rounded-xl font-medium transition-all shadow-lg hover:shadow-xl"
              style={{
                backgroundColor: 'var(--text-color)',
                color: 'var(--bg-color)',
                opacity: isSubmitting || isCheckingUsername ? 0.7 : 1,
                boxShadow: `0 6px 20px color-mix(in srgb, var(--text-color) 25%, transparent)`
              }}
              disabled={isSubmitting || isCheckingUsername}
            >
              {isSubmitting ? (
                <span className="flex items-center justify-center">
                  <Loader2 size={18} className="animate-spin mr-2" />
                  Kaydediliyor...
                </span>
              ) : (
                'Profili Tamamla'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UsernameSetupPage;
