import { create } from 'zustand';
import { User } from '@supabase/supabase-js';
import { supabase } from '@shared/utils/supabaseClient';
import { authService } from '../services/authService';

// Profil tipi
export interface IUserProfile {
  id: string;
  username?: string;
  displayName?: string;
  avatar_url?: string;
  updated_at?: string;
}

// Auth state - UI bileşenleriyle uyumlu
interface AuthState {
  user: User | null;
  profile: IUserProfile | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  isAuthModalOpen: boolean;
  authModalActiveTab: 'login' | 'signup';
  isProfileSheetOpen: boolean;
  isUsernameSetupRequired: boolean;
  
  // Login/Signup
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, username: string, displayName?: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
  
  // Profile
  setUserAndProfile: (user: User | null, profile: IUserProfile | null) => void;
  updateProfileInStore: (data: { displayName?: string; username?: string }) => Promise<void>;
  submitUsername: (username: string, displayName: string) => Promise<void>;
  completeUsernameSetup: () => void;

  // UI
  openLoginModal: () => void;
  openSignupModal: () => void;
  closeAuthModal: () => void;
  openProfileSheet: () => void;
  closeProfileSheet: () => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  profile: null,
  isLoading: false,
  error: null,
  isAuthenticated: false,
  isAuthModalOpen: false,
  authModalActiveTab: 'login',
  isProfileSheetOpen: false,
  isUsernameSetupRequired: false,
  
  // Basit ve anlaşılır login
  login: async (email: string, password: string) => {
    try {
      set({ isLoading: true, error: null });
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) throw error;
      
      // Profil bilgisini varsayılan olarak user_metadata'dan alıyoruz
      // Gerçek uygulamada veri tabanından da çekebilirsiniz
      const profile = data.user ? {
        id: data.user.id,
        username: data.user.user_metadata?.username,
        displayName: data.user.user_metadata?.display_name || data.user.user_metadata?.displayName,
        avatar_url: data.user.user_metadata?.avatar_url,
      } : null;
      
      set({ 
        user: data.user, 
        profile,
        isAuthenticated: !!data.user, 
        isLoading: false 
      });
      
    } catch (error) {
      console.error('Login error:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Giriş yapılırken bir hata oluştu', 
        isLoading: false,
        isAuthenticated: false
      });
    }
  },
  
  // Basit ve anlaşılır signup
  signup: async (email: string, password: string, username: string, displayName?: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // Username formatlaması
      const formattedUsername = username.startsWith('@') ? username : `@${username}`;
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: formattedUsername,
            display_name: displayName || username,
          },
          emailRedirectTo: `${window.location.origin}`,
        },
      });
      
      if (error) throw error;
      
      // Profil bilgisini user_metadata'dan al
      const profile = data.user ? {
        id: data.user.id,
        username: formattedUsername,
        displayName: displayName || username,
      } : null;
      
      // Email onayı gerektiğini bildir
      if (data?.user?.identities?.length === 0 || data?.user?.email_confirmed_at === null) {
        set({
          user: null,
          profile: null,
          isAuthenticated: false,
          isLoading: false,
          error: "Kayıt işleminiz tamamlandı. Lütfen e-posta adresinize gönderilen onay bağlantısına tıklayın. Bu olmadan giriş yapamazsınız."
        });
        return;
      }
      
      set({ 
        user: data.user, 
        profile,
        isAuthenticated: !!data.user, 
        isLoading: false 
      });
      
    } catch (error) {
      console.error('Signup error:', error);
      set({
        error: error instanceof Error ? error.message : 'Kayıt olurken bir hata oluştu',
        isLoading: false,
        isAuthenticated: false
      });
    }
  },
  
  // Google ile giriş
  signInWithGoogle: async () => {
    try {
      set({ isLoading: true, error: null });
      
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: window.location.origin,
          queryParams: {
            prompt: 'consent', // Her zaman izin iste
            access_type: 'offline', // Refresh token almak için gerekli
            include_granted_scopes: 'true', // Verilen tüm izinleri dahil et
            // Google'dan daha fazla bilgi almak için scope'ları genişletelim
            scope: 'openid email profile',
          }
        },
      });
      
      if (error) throw error;
      set({ isLoading: false });
      
    } catch (error) {
      console.error('Google login error:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Google ile giriş yapılırken bir hata oluştu', 
        isLoading: false 
      });
    }
  },
  
  // Net ve temiz logout
  logout: async () => {
    try {
      set({ isLoading: true });
      
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      set({ 
        user: null, 
        profile: null, 
        isAuthenticated: false, 
        isLoading: false, 
        isUsernameSetupRequired: false
      });
      
    } catch (error) {
      console.error('Logout error:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Çıkış yapılırken bir hata oluştu', 
        isLoading: false 
      });
    }
  },
  
  // Şifre sıfırlama
  resetPassword: async (email: string) => {
    try {
    set({ isLoading: true, error: null });
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      
      if (error) throw error;
      set({ isLoading: false });
      
    } catch (error) {
      console.error('Password reset error:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Şifre sıfırlama işlemi başarısız oldu', 
        isLoading: false 
      });
    }
  },
  
  // Basit ve etkili session kontrolü
  checkAuth: async () => {
    try {
      set({ isLoading: true });
      
      // 1. Hızlı ön kontrol - localStorage'dan token kontrolü
      try {
        const hasLocalStorageAuthTokens = 
          localStorage.getItem('sb-access-token') ||
          localStorage.getItem('supabase.auth.token') ||
          localStorage.getItem('ikra-kitabe-auth');
        
        // Eğer hiç token yoksa, hızlıca 'giriş yapmamış' durumuna geç
        if (!hasLocalStorageAuthTokens) {
          console.log('[authStore] No auth tokens found in localStorage, setting not authenticated state');
          set({ 
            user: null, 
            profile: null, 
            isAuthenticated: false, 
            isUsernameSetupRequired: false,
            isLoading: false 
          });
          return; // Daha fazla işlem yapma, çık
        }
      } catch (e) {
        console.warn('[authStore] Error checking localStorage:', e);
        // Sadece uyarı ver, işleme devam et
      }
      
      // 2. Supabase session kontrolü
      const { data } = await supabase.auth.getSession();
      const sessionExists = !!data.session;
      
      if (sessionExists) {
        const { data: userData } = await supabase.auth.getUser();
        
        // Google ile giriş mi?
        const isGoogleUser = userData.user?.app_metadata?.provider === 'google';
          
        // Profil bilgisini user_metadata'dan al
        let displayName = userData.user?.user_metadata?.display_name || 
                         userData.user?.user_metadata?.displayName || '';
        
        // Google kullanıcıları için özel işlem - name bilgisini kullan
        if (isGoogleUser && !displayName) {
          displayName = userData.user?.user_metadata?.full_name || 
                       userData.user?.user_metadata?.name || '';
          
          console.log('[authStore] Google user detected, using name from metadata:', displayName);
        }
                
        const profile = userData.user ? {
          id: userData.user.id,
          username: userData.user.user_metadata?.username,
          displayName: displayName,
          avatar_url: userData.user.user_metadata?.avatar_url,
        } : null;
        
        // Kullanıcı adı ayarlanması gerekiyor mu?
        // Google kullanıcıları için özel kontrol yapalım
        const needsUsername = isGoogleUser ? 
          !profile?.username || profile.username.trim() === '' : 
          false;
        
        console.log('[authStore] User checked:', { 
          isGoogleUser, 
          needsUsername, 
          username: profile?.username,
          displayName: profile?.displayName
        });
        
        set({ 
          user: userData.user, 
          profile, 
          isAuthenticated: true, 
          isUsernameSetupRequired: needsUsername,
          isLoading: false 
        });
      } else {
        set({ 
          user: null, 
          profile: null, 
          isAuthenticated: false, 
          isUsernameSetupRequired: false,
          isLoading: false 
        });
      }
      
    } catch (error) {
      console.error('Auth check error:', error);
      set({ 
        user: null, 
        profile: null, 
        isAuthenticated: false, 
        isUsernameSetupRequired: false,
        error: error instanceof Error ? error.message : 'Oturum kontrolünde bir hata oluştu', 
        isLoading: false 
      });
    }
  },
  
  // User ve profil bilgilerini set et
  setUserAndProfile: (user, profile) => {
    const needsUsername = user && (!profile?.username || profile.username.trim() === '');
    set({ 
      user, 
      profile, 
      isAuthenticated: !!user, 
      isUsernameSetupRequired: needsUsername || false
    });
  },
  
  // Kullanıcı adı ayarlama (Google login sonrası)
  submitUsername: async (username, displayName) => {
    try {
      set({ isLoading: true, error: null });
      
      // Username formatlaması
      const formattedUsername = username.startsWith('@') ? username : `@${username}`;
      
      const user = get().user;
      if (!user) throw new Error('Kullanıcı oturum açmamış');
      
      const userId = user.id;
      
      console.log('[authStore] Submitting username:', {
        userId, 
        username: formattedUsername,
        displayName
      });
      
      // authService üzerinden profil güncellemesi yap - bu hem profiles tablosunu hem de auth metadata'yı günceller
      await authService.updateProfile(userId, {
        username: formattedUsername, 
        displayName
      });
      
      // Profili güncelle
      const updatedProfile: IUserProfile = {
        id: userId,
        ...(get().profile || {}),
        username: formattedUsername,
        displayName: displayName,
      };
      
      set({ 
        profile: updatedProfile,
        isUsernameSetupRequired: false,
        isLoading: false
      });
      
    } catch (error) {
      console.error('Username submission error:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Kullanıcı adı ayarlanırken bir hata oluştu', 
        isLoading: false 
      });
    }
  },
  
  // Profil bilgilerini güncelle
  updateProfileInStore: async (data) => {
    try {
      set({ isLoading: true, error: null });
      
      const user = get().user;
      if (!user) throw new Error('Kullanıcı oturum açmamış');
      const userId = user.id;
      
      // authService'i kullan (bu hem auth.users hem de public.profiles'ı günceller)
      await authService.updateProfile(userId, {
        username: data.username,
        displayName: data.displayName
      });
      
      // Profili güncelle
      const currentProfile = get().profile;
      const formattedUsername = data.username ? 
        (data.username.startsWith('@') ? data.username : `@${data.username}`) : 
        undefined;
        
      const updatedProfile = currentProfile ? {
        ...currentProfile,
        ...(formattedUsername && { username: formattedUsername }),
        ...(data.displayName && { displayName: data.displayName }),
      } : {
        id: userId,
        ...(formattedUsername && { username: formattedUsername }),
        ...(data.displayName && { displayName: data.displayName }),
      };
      
      set({ 
        profile: updatedProfile,
        isLoading: false
      });
      
    } catch (error) {
      console.error('Profile update error:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Profil güncellenirken bir hata oluştu', 
        isLoading: false 
      });
    }
  },
  
  // Hata temizleme
  clearError: () => set({ error: null }),

  // UI aksiyonları
  openLoginModal: () => set({ isAuthModalOpen: true, authModalActiveTab: 'login' }),
  openSignupModal: () => set({ isAuthModalOpen: true, authModalActiveTab: 'signup' }),
  closeAuthModal: () => set({ isAuthModalOpen: false }),
  openProfileSheet: () => set({ isProfileSheetOpen: true }),
  closeProfileSheet: () => set({ isProfileSheetOpen: false }),
  completeUsernameSetup: () => set({ isUsernameSetupRequired: false }),
}));
