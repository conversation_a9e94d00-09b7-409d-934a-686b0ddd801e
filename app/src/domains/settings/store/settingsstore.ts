import { create } from 'zustand';
import {
  IAppSettings,
  IUserProfile,
  IUserPreferences,
  ISettingsLoadingState,
  ILocalizationSettings,
  INotificationSettings,
  ISyncSettings,
  IAccessibilitySettings,
  IHighlightColors
} from '../models/types';
import { settingsService } from '../services/settingsservice';
import { ThemeMode } from '@shared/theme/definitions';
import { persist } from 'zustand/middleware';

// Renkler için varsayılan de<PERSON>ler - Daha modern ve uyumlu renkler
const defaultHighlightColors: IHighlightColors = {
  // <PERSON>erh sarı, Not mavi, Yer imi yeşil ile hizalı
  highlight_color_1: '#fbbf24', // Sarı (Şerh)
  highlight_color_2: '#3b82f6', // Mavi (Not)
  highlight_color_3: '#10b981', // Yeşil (Yer imi)
  highlight_color_4: '#F06292', // Pembe (opsiyonel)
  highlight_color_5: 'AUTO_OVERLAY_15', // Dinamik renk - %15 overlay
  highlight_color_6: 'AUTO_OVERLAY_25', // Dinamik renk - %25 overlay
};

// Settings domain state'ini ve işlevlerini yöneten Zustand store
interface SettingsState {
  // State
  appSettings: IAppSettings;
  userProfile: IUserProfile | null;
  localizationSettings: ILocalizationSettings;
  notificationSettings: INotificationSettings;
  syncSettings: ISyncSettings;
  accessibilitySettings: IAccessibilitySettings;
  loading: ISettingsLoadingState;
  highlightColors: IHighlightColors; // 🎨 Renkleri state'e ekle
  settings: {
    fontSize: number;
    fontStyle: string;
    lineHeight: number;
    colorTheme: string;
    highlightColor: string;
  };
  
  // App settings actions
  fetchAppSettings: () => Promise<void>;
  updateAppSettings: (settings: Partial<IAppSettings>) => Promise<void>;
  
  // User profile actions
  fetchUserProfile: (userId: string) => Promise<void>;
  updateUserPreferences: (userId: string, preferences: Partial<IUserPreferences>) => Promise<void>;
  
  // 🎨 Renkler için yeni eylemler
  fetchHighlightColors: (userId: string) => Promise<void>;
  updateHighlightColor: (userId: string, colorKey: keyof IHighlightColors, colorValue: string) => Promise<void>;
  
  // Theme actions
  changeTheme: (theme: ThemeMode) => Promise<void>;
  
  // Language actions
  changeLanguage: (language: string) => Promise<void>;
  
  // Localization settings actions
  fetchLocalizationSettings: () => Promise<void>;
  updateLocalizationSettings: (settings: Partial<ILocalizationSettings>) => Promise<void>;
  
  // Notification settings actions
  fetchNotificationSettings: () => Promise<void>;
  updateNotificationSettings: (settings: Partial<INotificationSettings>) => Promise<void>;
  
  // Sync settings actions
  fetchSyncSettings: () => Promise<void>;
  updateSyncSettings: (settings: Partial<ISyncSettings>) => Promise<void>;
  
  // Accessibility settings actions
  fetchAccessibilitySettings: () => Promise<void>;
  updateAccessibilitySettings: (settings: Partial<IAccessibilitySettings>) => Promise<void>;
  
  // Reset all settings
  resetAllSettings: () => Promise<void>;

  // New settings actions
  setFontSize: (size: number) => void;
  setFontStyle: (style: string) => void;
  setLineHeight: (height: number) => void;
  setColorTheme: (theme: string) => void;
  setHighlightColor: (color: string) => void;
}

// Default loading state
const defaultLoadingState: ISettingsLoadingState = {
  appSettings: false,
  userProfile: false,
  localizationSettings: false,
  notificationSettings: false,
  syncSettings: false,
  accessibilitySettings: false
};

// Varsayılan uygulama ayarları - settingsService'den alınabilir
const defaultAppSettings: IAppSettings = {
  theme: ThemeMode.LIGHT,
  language: 'tr',
  showWelcomeScreen: true,
  enableNotifications: true,
  enableAutoSync: true,
  syncInterval: 24 * 60 * 60 * 1000, // 24 saat (ms cinsinden)
};

// Diğer varsayılan ayarlar da benzer şekilde tanımlanabilir
// Buraya settingsService'deki varsayılan değerleri de aktarabiliriz

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      appSettings: defaultAppSettings,
      userProfile: null,
      localizationSettings: {
        language: 'tr',
        dateFormat: 'DD.MM.YYYY',
        timeFormat: 'HH:mm',
        timezone: 'Europe/Istanbul',
        translationEnabled: true,
      },
      notificationSettings: {
        enable: true,
        dailyReminder: false,
        reminderTime: '20:00',
        newContentUpdates: true,
        systemAnnouncements: true,
        sound: true,
        vibration: true,
      },
      syncSettings: {
        autoSync: true,
        syncInterval: 24 * 60 * 60 * 1000,
        syncOnWifi: true,
        syncReadingProgress: true,
        syncBookmarks: true,
        syncNotes: true,
        syncSettings: true,
      },
      accessibilitySettings: {
        highContrast: false,
        largeText: false,
        reduceAnimation: false,
        screenReader: false,
      },
      loading: defaultLoadingState,
      highlightColors: defaultHighlightColors,
      settings: {
        fontSize: 1,
        fontStyle: 'serif',
        lineHeight: 1.5,
        colorTheme: 'light',
        highlightColor: 'yellow',
      },
      fetchAppSettings: async () => {
        set(state => ({ loading: { ...state.loading, appSettings: true } }));
        try {
          const settings = await settingsService.getAppSettings();
          set({ 
            appSettings: settings,
            loading: { ...get().loading, appSettings: false }
          });
        } catch (error) {
          console.error('Failed to fetch app settings:', error);
          set(state => ({ loading: { ...state.loading, appSettings: false } }));
        }
      },
      updateAppSettings: async (settings: Partial<IAppSettings>) => {
        set(state => ({ loading: { ...state.loading, appSettings: true } }));
        try {
          const updatedSettings = { ...get().appSettings, ...settings };
          await settingsService.saveAppSettings(updatedSettings);
          set({ 
            appSettings: updatedSettings,
            loading: { ...get().loading, appSettings: false }
          });
        } catch (error) {
          console.error('Failed to update app settings:', error);
          set(state => ({ loading: { ...state.loading, appSettings: false } }));
        }
      },
      fetchUserProfile: async (userId: string) => {
        set(state => ({ loading: { ...state.loading, userProfile: true } }));
        try {
          const profile = await settingsService.getUserProfile(userId);
          set({ 
            userProfile: profile,
            loading: { ...get().loading, userProfile: false }
          });
          if (profile) {
            get().fetchHighlightColors(userId);
          }
        } catch (error) {
          console.error('Failed to fetch user profile:', error);
          set(state => ({ loading: { ...state.loading, userProfile: false } }));
        }
      },
      updateUserPreferences: async (userId: string, preferences: Partial<IUserPreferences>) => {
        set(state => ({ loading: { ...state.loading, userProfile: true } }));
        try {
          const updatedPreferences = await settingsService.updateUserPreferences(userId, preferences);
          if (get().userProfile && updatedPreferences) {
            set(state => ({ 
              userProfile: {
                ...state.userProfile!,
                preferences: updatedPreferences
              },
              loading: { ...state.loading, userProfile: false }
            }));
          } else {
            set(state => ({ loading: { ...state.loading, userProfile: false } }));
          }
        } catch (error) {
          console.error('Failed to update user preferences:', error);
          set(state => ({ loading: { ...state.loading, userProfile: false } }));
        }
      },
      fetchHighlightColors: async (userId: string) => {
        try {
          const colors = await settingsService.getHighlightColors(userId);
          if (colors) {
            set({ highlightColors: { ...defaultHighlightColors, ...colors } });
          } else {
            set({ highlightColors: defaultHighlightColors });
          }
        } catch (error) {
          console.error('Failed to fetch highlight colors:', error);
          set({ highlightColors: defaultHighlightColors });
        }
      },
      updateHighlightColor: async (userId: string, colorKey: keyof IHighlightColors, colorValue: string) => {
        const previousColors = get().highlightColors;
        const newColors = { ...previousColors, [colorKey]: colorValue };
        set({ highlightColors: newColors });
        try {
          await settingsService.updateHighlightColors(userId, { [colorKey]: colorValue });
        } catch (error) {
          console.error(`Failed to update ${colorKey}:`, error);
          set({ highlightColors: previousColors });
        }
      },
      changeTheme: async (theme: ThemeMode) => {
        set(state => ({ loading: { ...state.loading, appSettings: true } }));
        try {
          await settingsService.changeTheme(theme);
          set(state => ({ 
            appSettings: { ...state.appSettings, theme },
            loading: { ...state.loading, appSettings: false }
          }));
        } catch (error) {
          console.error('Failed to change theme:', error);
          set(state => ({ loading: { ...state.loading, appSettings: false } }));
        }
      },
      changeLanguage: async (language: string) => {
        set(state => ({ loading: { ...state.loading, appSettings: true } }));
        try {
          await settingsService.changeLanguage(language);
          set(state => ({ 
            appSettings: { ...state.appSettings, language },
            loading: { ...state.loading, appSettings: false }
          }));
        } catch (error) {
          console.error('Failed to change language:', error);
          set(state => ({ loading: { ...state.loading, appSettings: false } }));
        }
      },
      fetchLocalizationSettings: async () => {
        set(state => ({ loading: { ...state.loading, localizationSettings: true } }));
        try {
          const settings = await settingsService.getLocalizationSettings();
          set({ 
            localizationSettings: settings,
            loading: { ...get().loading, localizationSettings: false }
          });
        } catch (error) {
          console.error('Failed to fetch localization settings:', error);
          set(state => ({ loading: { ...state.loading, localizationSettings: false } }));
        }
      },
      updateLocalizationSettings: async (settings: Partial<ILocalizationSettings>) => {
        set(state => ({ 
          localizationSettings: { ...state.localizationSettings, ...settings } 
        }));
      },
      fetchNotificationSettings: async () => {
        set(state => ({ loading: { ...state.loading, notificationSettings: true } }));
        try {
          const settings = await settingsService.getNotificationSettings();
          set({ 
            notificationSettings: settings,
            loading: { ...get().loading, notificationSettings: false }
          });
        } catch (error) {
          console.error('Failed to fetch notification settings:', error);
          set(state => ({ loading: { ...state.loading, notificationSettings: false } }));
        }
      },
      updateNotificationSettings: async (settings: Partial<INotificationSettings>) => {
        set(state => ({ 
          notificationSettings: { ...state.notificationSettings, ...settings } 
        }));
      },
      fetchSyncSettings: async () => {
        set(state => ({ loading: { ...state.loading, syncSettings: true } }));
        try {
          const settings = await settingsService.getSyncSettings();
          set({ 
            syncSettings: settings,
            loading: { ...get().loading, syncSettings: false }
          });
        } catch (error) {
          console.error('Failed to fetch sync settings:', error);
          set(state => ({ loading: { ...state.loading, syncSettings: false } }));
        }
      },
      updateSyncSettings: async (settings: Partial<ISyncSettings>) => {
        set(state => ({ 
          syncSettings: { ...state.syncSettings, ...settings } 
        }));
      },
      fetchAccessibilitySettings: async () => {
        set(state => ({ loading: { ...state.loading, accessibilitySettings: true } }));
        try {
          const settings = await settingsService.getAccessibilitySettings();
          set({ 
            accessibilitySettings: settings,
            loading: { ...get().loading, accessibilitySettings: false }
          });
        } catch (error) {
          console.error('Failed to fetch accessibility settings:', error);
          set(state => ({ loading: { ...state.loading, accessibilitySettings: false } }));
        }
      },
      updateAccessibilitySettings: async (settings: Partial<IAccessibilitySettings>) => {
        set(state => ({ 
          accessibilitySettings: { ...state.accessibilitySettings, ...settings } 
        }));
      },
      resetAllSettings: async () => {
        set(state => ({ 
          loading: { 
            ...state.loading, 
            appSettings: true,
            localizationSettings: true,
            notificationSettings: true,
            syncSettings: true,
            accessibilitySettings: true
          } 
        }));
        try {
          await settingsService.resetAllSettings();
          await get().fetchAppSettings();
          await get().fetchLocalizationSettings();
          await get().fetchNotificationSettings();
          await get().fetchSyncSettings();
          await get().fetchAccessibilitySettings();
        } catch (error) {
          console.error('Failed to reset all settings:', error);
          set(state => ({ 
            loading: { 
              ...state.loading, 
              appSettings: false,
              localizationSettings: false,
              notificationSettings: false,
              syncSettings: false,
              accessibilitySettings: false
            } 
          }));
        }
      },
      setFontSize: (size) => set((state) => ({ settings: { ...state.settings, fontSize: size } })),
      setFontStyle: (style) => set((state) => ({ settings: { ...state.settings, fontStyle: style } })),
      setLineHeight: (height) => set((state) => ({ settings: { ...state.settings, lineHeight: height } })),
      setColorTheme: (theme) => set((state) => ({ settings: { ...state.settings, colorTheme: theme } })),
      setHighlightColor: (color) => set((state) => ({ settings: { ...state.settings, highlightColor: color } })),
    }),
    {
      name: 'settings-storage',
      getStorage: () => localStorage,
    }
  )
); 