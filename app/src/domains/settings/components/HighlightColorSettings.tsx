import React, { useState, useEffect } from 'react';
import { Palette, RotateCcw } from 'lucide-react';
import { useSettingsStore } from '../store/settingsstore';
import { useReaderInteractionStyles } from '@domains/reader-interactions/shared/hooks/useReaderInteractionStyles';
import { supabase } from '@shared/utils/supabaseClient';
import type { IHighlightColors } from '../models/types';

interface UserSession {
  user?: {
    id?: string;
  } | null;
}

interface ColorInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
}

const ColorInput: React.FC<ColorInputProps> = ({ label, value, onChange }) => {
  const { bgColors, borderColors } = useReaderInteractionStyles();

  return (
    <div
      className="flex items-center justify-between p-3 rounded-lg border"
      style={{
        backgroundColor: bgColors.card,
        borderColor: borderColors.light,
      }}
    >
      <div className="flex items-center gap-3">
        <div
          className="w-8 h-8 rounded-lg border-2 border-white shadow-sm"
          style={{ backgroundColor: value }}
        />
        <div>
          <p className="font-medium text-sm" style={{ color: 'var(--text-color)' }}>
            {label}
          </p>
          <p className="text-xs opacity-60" style={{ color: 'var(--text-color)' }}>
            {value.toUpperCase()}
          </p>
        </div>
      </div>

      <input
        type="color"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-10 h-10 rounded-lg border cursor-pointer"
        style={{ borderColor: borderColors.default }}
      />
    </div>
  );
};

const HighlightColorSettings: React.FC = () => {
  const { highlightColors, updateHighlightColor, fetchHighlightColors } = useSettingsStore();
  const { bgColors, borderColors } = useReaderInteractionStyles();
  const [session, setSession] = useState<UserSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setSession(session);
        if (session?.user?.id) {
          fetchHighlightColors(session.user.id);
        }
      }
    );

    // Mevcut session'ı kontrol et
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (session?.user?.id) {
        fetchHighlightColors(session.user.id);
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [fetchHighlightColors]);

  const handleColorChange = async (colorKey: keyof IHighlightColors, newColor: string) => {
    if (!session?.user?.id) return;

    setIsLoading(true);
    try {
      await updateHighlightColor(session.user.id, colorKey, newColor);
    } catch (error) {
      console.error('Renk güncellenirken hata oluştu:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetToDefaults = async () => {
    if (!session?.user?.id) return;

    setIsLoading(true);
    try {
      const defaultColors: IHighlightColors = {
        highlight_color_1: '#FEF3C7',
        highlight_color_2: '#DBEAFE',
        highlight_color_3: '#D1FAE5',
        highlight_color_4: '#FCE7F3',
      };

      // Tüm renkleri varsayılan değerlere sıfırla
      for (const [key, value] of Object.entries(defaultColors)) {
        await updateHighlightColor(session.user.id, key as keyof IHighlightColors, value);
      }
    } catch (error) {
      console.error('Renkler sıfırlanırken hata oluştu:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!session) {
    return (
      <div
        className="p-6 rounded-lg border text-center"
        style={{
          backgroundColor: bgColors.card,
          borderColor: borderColors.light,
        }}
      >
        <Palette size={48} className="mx-auto mb-4 opacity-50" style={{ color: 'var(--text-color)' }} />
        <p className="text-lg font-medium mb-2" style={{ color: 'var(--text-color)' }}>
          Renk Ayarları
        </p>
        <p className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
          Renk ayarlarını değiştirmek için giriş yapmanız gerekmektedir.
        </p>
      </div>
    );
  }

  return (
    <div
      className="p-6 rounded-lg border"
      style={{
        backgroundColor: bgColors.card,
        borderColor: borderColors.light,
      }}
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Palette size={24} style={{ color: 'var(--text-color)' }} />
          <div>
            <h2 className="text-lg font-bold" style={{ color: 'var(--text-color)' }}>
              Vurgu Renkleri
            </h2>
            <p className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
              Metinleri vurgularken kullanılacak renkleri özelleştirin
            </p>
          </div>
        </div>

        <button
          onClick={resetToDefaults}
          disabled={isLoading}
          className="flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50"
          style={{
            backgroundColor: bgColors.hover,
            borderColor: borderColors.default,
            color: 'var(--text-color)',
          }}
        >
          <RotateCcw size={16} />
          <span className="text-sm font-medium">Sıfırla</span>
        </button>
      </div>

      <div className="space-y-4">
        <ColorInput
          label="Vurgu Rengi 1"
          value={highlightColors.highlight_color_1}
          onChange={(value) => handleColorChange('highlight_color_1', value)}
        />
        <ColorInput
          label="Vurgu Rengi 2"
          value={highlightColors.highlight_color_2}
          onChange={(value) => handleColorChange('highlight_color_2', value)}
        />
        <ColorInput
          label="Vurgu Rengi 3"
          value={highlightColors.highlight_color_3}
          onChange={(value) => handleColorChange('highlight_color_3', value)}
        />
        <ColorInput
          label="Vurgu Rengi 4"
          value={highlightColors.highlight_color_4}
          onChange={(value) => handleColorChange('highlight_color_4', value)}
        />
      </div>

      <div
        className="mt-6 p-4 rounded-lg border-l-4"
        style={{
          backgroundColor: bgColors.hover,
          borderLeftColor: '#3B82F6',
        }}
      >
        <p className="text-sm" style={{ color: 'var(--text-color)' }}>
          <strong>💡 İpucu:</strong> Seçtiğiniz renkler sadece arka plan rengini belirler. 
          Metin rengi otomatik olarak tema ile uyumlu şekilde ayarlanır.
        </p>
      </div>

      {isLoading && (
        <div className="mt-4 text-center">
          <p className="text-sm opacity-70" style={{ color: 'var(--text-color)' }}>
            Renkler güncelleniyor...
          </p>
        </div>
      )}
    </div>
  );
};

export default HighlightColorSettings;
