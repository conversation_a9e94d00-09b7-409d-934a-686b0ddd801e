import { ThemeMode } from '@shared/theme/definitions';

// Uygulama ayarları
export interface IAppSettings {
  theme: ThemeMode;
  language: string;
  showWelcomeScreen: boolean;
  enableNotifications: boolean;
  enableAutoSync: boolean;
  syncInterval: number; // milliseconds
  lastSyncDate?: Date;
}

// Kullanıcı profili
export interface IUserProfile {
  id: number;
  username: string;
  email?: string;
  name?: string;
  avatar?: string;
  createdAt: Date;
  lastLoginAt?: Date;
  preferences: IUserPreferences;
}

// 🎨 Kullanıcıya özel vurgu renkleri için arayüz
export interface IHighlightColors {
  highlight_color_1: string;
  highlight_color_2: string;
  highlight_color_3: string;
  highlight_color_4: string;
  highlight_color_5: string; // Dinamik renk - Auto overlay oranlı
  highlight_color_6: string; // Dinamik renk - Auto overlay oranlı
}

/**
 * Kullanıcı tercihlerini tanımlar
 */
export interface IUserPreferences {
  // Bu <PERSON>, IHighlightColors'ı genişletir
  someOtherPreference?: boolean; // Örnek olarak başka bir tercih
}

// Yerelleştirme ayarları
export interface ILocalizationSettings {
  language: string;
  dateFormat: string;
  timeFormat: string;
  timezone: string;
  translationEnabled: boolean;
}

// Bildirim ayarları
export interface INotificationSettings {
  enable: boolean;
  dailyReminder: boolean;
  reminderTime: string; // HH:MM format
  newContentUpdates: boolean;
  systemAnnouncements: boolean;
  sound: boolean;
  vibration: boolean;
}

// Yedekleme ve senkronizasyon ayarları
export interface ISyncSettings {
  autoSync: boolean;
  syncInterval: number; // milliseconds
  syncOnWifi: boolean;
  syncReadingProgress: boolean;
  syncBookmarks: boolean;
  syncNotes: boolean;
  syncSettings: boolean;
  lastSyncDate?: Date;
}

// Erişilebilirlik ayarları
export interface IAccessibilitySettings {
  highContrast: boolean;
  largeText: boolean;
  reduceAnimation: boolean;
  screenReader: boolean;
}

// UI için loading state'leri
export interface ISettingsLoadingState {
  appSettings: boolean;
  userProfile: boolean;
  localizationSettings: boolean;
  notificationSettings: boolean;
  syncSettings: boolean;
  accessibilitySettings: boolean;
}

export interface SettingsState {
  settings: {
    fontSize: number;
    fontStyle: string;
    lineHeight: number;
    colorTheme: string;
    highlightColor: string;
  };
  setFontSize: (size: number) => void;
  setFontStyle: (style: string) => void;
  setLineHeight: (height: number) => void;
  setColorTheme: (theme: string) => void;
  setHighlightColor: (color: string) => void;
} 