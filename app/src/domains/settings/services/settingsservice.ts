import { supabase } from '@shared/utils/supabaseClient'; //  supabase client'ı ekle
import { 
  IAppSettings, 
  IUserProfile, 
  IUserPreferences,
  ILocalizationSettings, 
  INotificationSettings, 
  ISyncSettings, 
  IAccessibilitySettings,
  IHighlightColors
} from '../models/types';
import { ThemeMode } from '@shared/theme/definitions';

// Varsayılan uygulama ayarları
const DEFAULT_APP_SETTINGS: IAppSettings = {
  theme: ThemeMode.LIGHT,
  language: 'tr',
  showWelcomeScreen: true,
  enableNotifications: true,
  enableAutoSync: true,
  syncInterval: 24 * 60 * 60 * 1000, // 24 saat (ms cinsinden)
};

// Varsayılan yerelleştirme ayarları
const DEFAULT_LOCALIZATION_SETTINGS: ILocalizationSettings = {
  language: 'tr',
  dateFormat: 'DD.MM.YYYY',
  timeFormat: 'HH:mm',
  timezone: 'Europe/Istanbul',
  translationEnabled: true,
};

// Varsayılan bildirim ayarları
const DEFAULT_NOTIFICATION_SETTINGS: INotificationSettings = {
  enable: true,
  dailyReminder: false,
  reminderTime: '20:00',
  newContentUpdates: true,
  systemAnnouncements: true,
  sound: true,
  vibration: true,
};

// Varsayılan senkronizasyon ayarları
const DEFAULT_SYNC_SETTINGS: ISyncSettings = {
  autoSync: true,
  syncInterval: 24 * 60 * 60 * 1000, // 24 saat (ms cinsinden)
  syncOnWifi: true,
  syncReadingProgress: true,
  syncBookmarks: true,
  syncNotes: true,
  syncSettings: true,
};

// Varsayılan erişilebilirlik ayarları
const DEFAULT_ACCESSIBILITY_SETTINGS: IAccessibilitySettings = {
  highContrast: false,
  largeText: false,
  reduceAnimation: false,
  screenReader: false,
};

/**
 * Ayarlar servisi - Uygulamanın ve kullanıcının tüm ayarlarını yönetir
 */
export const settingsService = {
  /**
   * Uygulama ayarlarını getirir
   */
  async getAppSettings(): Promise<IAppSettings> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'dan okuyoruz
    try {
      const storedSettings = localStorage.getItem('app_settings');
      if (storedSettings) {
        const settings = JSON.parse(storedSettings);
        // Date objesi olarak dönüştür
        if (settings.lastSyncDate) {
          settings.lastSyncDate = new Date(settings.lastSyncDate);
        }
        return settings;
      }
      return DEFAULT_APP_SETTINGS;
    } catch (error) {
      console.error('Failed to get app settings:', error);
      return DEFAULT_APP_SETTINGS;
    }
  },
  
  /**
   * Uygulama ayarlarını kaydeder
   */
  async saveAppSettings(settings: IAppSettings): Promise<IAppSettings> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'a kaydediyoruz
    try {
      localStorage.setItem('app_settings', JSON.stringify(settings));
      return settings;
    } catch (error) {
      console.error('Failed to save app settings:', error);
      return settings;
    }
  },
  
  /**
   * Kullanıcı profilini getirir
   */
  async getUserProfile(userId: string): Promise<IUserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data as IUserProfile;
    } catch (error) {
      console.error('Failed to get user profile:', error);
      return null;
    }
  },
  
  /**
   * Kullanıcı tercihlerini günceller (upsert mantığı ile)
   */
  async updateUserPreferences(userId: string, preferences: Partial<IUserPreferences>): Promise<IUserPreferences | null> {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .upsert({ user_id: userId, ...preferences }, { onConflict: 'user_id' })
        .select()
        .single();

      if (error) throw error;
      return data as IUserPreferences;
    } catch (error) {
      console.error('Failed to update user preferences:', error);
      return null;
    }
  },

  /**
   * Kullanıcının vurgu renklerini getirir
   */
  async getHighlightColors(userId: string): Promise<IHighlightColors | null> {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('highlight_color_1, highlight_color_2, highlight_color_3, highlight_color_4')
        .eq('user_id', userId)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') return null; // No settings found, not an error
        throw error;
      }
      
      // Column'ları IHighlightColors formatına çevir
      if (data) {
        return {
          highlight_color_1: data.highlight_color_1,
          highlight_color_2: data.highlight_color_2,
          highlight_color_3: data.highlight_color_3,
          highlight_color_4: data.highlight_color_4,
        };
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get highlight colors:', error);
      return null;
    }
  },

  /**
   * Kullanıcının vurgu renklerini günceller (upsert mantığı ile)
   */
  async updateHighlightColors(userId: string, colors: Partial<IHighlightColors>): Promise<IHighlightColors | null> {
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .upsert({ 
          user_id: userId, 
          ...colors 
        }, { 
          onConflict: 'user_id' 
        })
        .select('highlight_color_1, highlight_color_2, highlight_color_3, highlight_color_4')
        .single();

      if (error) throw error;
      
      // Column'ları IHighlightColors formatına çevir
      if (data) {
        return {
          highlight_color_1: data.highlight_color_1,
          highlight_color_2: data.highlight_color_2,
          highlight_color_3: data.highlight_color_3,
          highlight_color_4: data.highlight_color_4,
        };
      }
      
      return null;
    } catch (error) {
      console.error('Failed to update highlight colors:', error);
      return null;
    }
  },
  
  /**
   * Tema ayarını değiştirir
   */
  async changeTheme(theme: ThemeMode): Promise<ThemeMode> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'a kaydediyoruz ve doküman kök elementine tema sınıfını ekliyoruz
    try {
      const appSettings = await this.getAppSettings();
      appSettings.theme = theme;
      localStorage.setItem('app_settings', JSON.stringify(appSettings));
      
      // Tema sınıfını doküman kök elementine ekle
      document.documentElement.classList.remove('light', 'dark', 'krem');
      document.documentElement.classList.add(theme);
      
      // Tema değişkenleri için CSS değişkenlerini güncelle
      if (theme === 'dark') {
        document.documentElement.style.setProperty('--bg-color', '#121212');
        document.documentElement.style.setProperty('--text-color', '#ffffff');
      } else if (theme === 'light') {
        document.documentElement.style.setProperty('--bg-color', '#ffffff');
        document.documentElement.style.setProperty('--text-color', '#121212');
      } else if (theme === 'krem') {
        document.documentElement.style.setProperty('--bg-color', '#f8f3e6');
        document.documentElement.style.setProperty('--text-color', '#332b1f');
      }
      
      return theme;
    } catch (error) {
      console.error('Failed to change theme:', error);
      return theme;
    }
  },
  
  /**
   * Dil ayarını değiştirir
   */
  async changeLanguage(language: string): Promise<string> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'a kaydediyoruz
    try {
      const appSettings = await this.getAppSettings();
      appSettings.language = language;
      localStorage.setItem('app_settings', JSON.stringify(appSettings));
      
      // Dil değişikliği için gereken diğer işlemleri yap (i18n kütüphanesi vb.)
      
      return language;
    } catch (error) {
      console.error('Failed to change language:', error);
      return language;
    }
  },
  
  /**
   * Yerelleştirme ayarlarını getirir
   */
  async getLocalizationSettings(): Promise<ILocalizationSettings> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'dan okuyoruz
    try {
      const storedSettings = localStorage.getItem('localization_settings');
      if (storedSettings) {
        return JSON.parse(storedSettings);
      }
      return DEFAULT_LOCALIZATION_SETTINGS;
    } catch (error) {
      console.error('Failed to get localization settings:', error);
      return DEFAULT_LOCALIZATION_SETTINGS;
    }
  },
  
  /**
   * Bildirim ayarlarını getirir
   */
  async getNotificationSettings(): Promise<INotificationSettings> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'dan okuyoruz
    try {
      const storedSettings = localStorage.getItem('notification_settings');
      if (storedSettings) {
        return JSON.parse(storedSettings);
      }
      return DEFAULT_NOTIFICATION_SETTINGS;
    } catch (error) {
      console.error('Failed to get notification settings:', error);
      return DEFAULT_NOTIFICATION_SETTINGS;
    }
  },
  
  /**
   * Senkronizasyon ayarlarını getirir
   */
  async getSyncSettings(): Promise<ISyncSettings> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'dan okuyoruz
    try {
      const storedSettings = localStorage.getItem('sync_settings');
      if (storedSettings) {
        const settings = JSON.parse(storedSettings);
        // Date objesi olarak dönüştür
        if (settings.lastSyncDate) {
          settings.lastSyncDate = new Date(settings.lastSyncDate);
        }
        return settings;
      }
      return DEFAULT_SYNC_SETTINGS;
    } catch (error) {
      console.error('Failed to get sync settings:', error);
      return DEFAULT_SYNC_SETTINGS;
    }
  },
  
  /**
   * Erişilebilirlik ayarlarını getirir
   */
  async getAccessibilitySettings(): Promise<IAccessibilitySettings> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'dan okuyoruz
    try {
      const storedSettings = localStorage.getItem('accessibility_settings');
      if (storedSettings) {
        return JSON.parse(storedSettings);
      }
      return DEFAULT_ACCESSIBILITY_SETTINGS;
    } catch (error) {
      console.error('Failed to get accessibility settings:', error);
      return DEFAULT_ACCESSIBILITY_SETTINGS;
    }
  },
  
  /**
   * Tüm ayarları varsayılan değerlerine sıfırlar
   */
  async resetAllSettings(): Promise<void> {
    // İleride API çağrısı olacak
    // Şimdilik localStorage'dan siliyoruz
    try {
      localStorage.removeItem('app_settings');
      localStorage.removeItem('localization_settings');
      localStorage.removeItem('notification_settings');
      localStorage.removeItem('sync_settings');
      localStorage.removeItem('accessibility_settings');
      
      // Öğelerimizi varsayılanlarıyla kaydediyoruz
      localStorage.setItem('app_settings', JSON.stringify(DEFAULT_APP_SETTINGS));
      localStorage.setItem('localization_settings', JSON.stringify(DEFAULT_LOCALIZATION_SETTINGS));
      localStorage.setItem('notification_settings', JSON.stringify(DEFAULT_NOTIFICATION_SETTINGS));
      localStorage.setItem('sync_settings', JSON.stringify(DEFAULT_SYNC_SETTINGS));
      localStorage.setItem('accessibility_settings', JSON.stringify(DEFAULT_ACCESSIBILITY_SETTINGS));
      
      // Tema değişikliğini uygula
      await this.changeTheme(DEFAULT_APP_SETTINGS.theme);
    } catch (error) {
      console.error('Failed to reset all settings:', error);
    }
  }
}; 