import { 
  createContext, 
  useContext, 
  useEffect, 
  ReactNode,
  FC
} from 'react';
import { 
  ThemeMode, 
  getThemeById, 
  THEME_CYCLE,
  Theme
} from '../theme/definitions';
import { useSettingsStore } from '@domains/settings/store/settingsstore';

// LocalStorage anahtarı - Artık store tarafından yönetilecek, kaldırılabilir veya sadece referans olarak kalabilir
// const THEME_STORAGE_KEY = 'ikra-kitabe-theme';

// Context için tip tanımı
interface ThemeContextType {
  currentTheme: Theme;
  themeMode: ThemeMode;
  setTheme: (theme: ThemeMode) => void;
  cycleTheme: () => void;
}

// Varsayılan context değeri (Başlangıç değeri store'dan gelecek)
const defaultContextValue: ThemeContextType = {
  currentTheme: getThemeById(ThemeMode.DARK), // Default will be quickly overridden by store state
  themeMode: ThemeMode.DARK,                // Default will be quickly overridden by store state
  setTheme: () => { console.warn("ThemeProvider not ready yet"); },
  cycleTheme: () => { console.warn("ThemeProvider not ready yet"); }
};

// Context oluşturma
export const ThemeContext = createContext<ThemeContextType>(defaultContextValue);

// Context hook'u
export const useTheme = () => useContext(ThemeContext);

// Provider props tipi (initialTheme artık gereksiz)
interface ThemeProviderProps {
  children: ReactNode;
  // initialTheme?: ThemeMode; // Remove initialTheme prop
}

// CSS değişkenlerini güncelleme fonksiyonu
const updateCssVariables = (theme: Theme) => {
  const { colors } = theme;
  document.documentElement.style.setProperty('--text-color', colors.text);
  document.documentElement.style.setProperty('--bg-color', colors.background);
  document.documentElement.style.setProperty('--color-accent', colors.accent);
};

// ThemeProvider bileşeni
export const ThemeProvider: FC<ThemeProviderProps> = ({ 
  children, 
  // initialTheme // Remove initialTheme prop
}) => {
  // Store'dan tema state'ini ve action'ı al
  const themeMode = useSettingsStore((state) => state.appSettings.theme);
  const changeThemeAction = useSettingsStore((state) => state.changeTheme);
  // Store'dan başlangıç temasını yükleme (isteğe bağlı, eğer ilk yüklemede async ise)
  // useEffect(() => {
  //   useSettingsStore.getState().fetchAppSettings(); // Veya benzeri bir action
  // }, []);

  // Yerel state'i kaldır
  // const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
  //   const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
  //   if (savedTheme && Object.values(ThemeMode).includes(savedTheme as ThemeMode)) {
  //     return savedTheme as ThemeMode;
  //   }
  //   return initialTheme || ThemeMode.DARK;
  // });
  
  // Tema nesnesi (Store'dan gelen themeMode'u kullan)
  const currentTheme = getThemeById(themeMode);
  
  // Tema değiştirme fonksiyonu (Store action'ını çağır)
  const setTheme = (newTheme: ThemeMode) => {
    changeThemeAction(newTheme);
    // localStorage.setItem(THEME_STORAGE_KEY, theme); // Store halleder
  };
  
  // Tema döngüsü fonksiyonu (Store action'ını çağır)
  const cycleTheme = () => {
    const nextTheme = THEME_CYCLE[themeMode];
    setTheme(nextTheme); // Bu zaten store action'ını çağıracak
  };
  
  // Tema değiştiğinde CSS değişkenlerini güncelle (Store'dan gelen currentTheme'i izle)
  useEffect(() => {
    updateCssVariables(currentTheme);
  }, [currentTheme]);
  
  // Context değeri
  const value = {
    currentTheme,
    themeMode,
    setTheme,
    cycleTheme
  };
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}; 