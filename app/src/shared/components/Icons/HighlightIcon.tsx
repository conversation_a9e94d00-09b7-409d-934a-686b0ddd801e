import React from 'react';

type HighlightIconProps = {
  size?: number;
  color?: string;
  strokeWidth?: number;
  className?: string;
  style?: React.CSSProperties;
};

/**
 * Highlight (Pencil) Icon
 * Matches the inline SVG used in highlights rendering for visual consistency.
 */
const HighlightIcon: React.FC<HighlightIconProps> = ({
  size = 16,
  color = 'currentColor',
  strokeWidth = 2,
  className,
  style,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      style={style}
      aria-hidden="true"
      focusable="false"
    >
      <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z" />
      <path d="M12 20h9" />
    </svg>
  );
};

export default HighlightIcon;


