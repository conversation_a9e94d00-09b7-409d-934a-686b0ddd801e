import { useState, useEffect, ReactNode } from 'react';

interface NetworkStatusMonitorProps {
  children: ReactNode;
  offlineMessage?: string;
  showOfflineMessage?: boolean;
}

/**
 * Ağ bağlantısı durumunu izleyen ve bağlantı kesildiğinde kullanıcıya bildirim gösteren bileşen.
 * 
 * Kullanım:
 * <NetworkStatusMonitor>
 *   <YourApp />
 * </NetworkStatusMonitor>
 */
const NetworkStatusMonitor = ({
  children,
  offlineMessage = 'İnternet bağlantınız kesildi. Bazı özellikler çalışmayabilir.',
  showOfflineMessage = true
}: NetworkStatusMonitorProps) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    // Ağ durumu değişikliklerini dinle
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Temizleme
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <>
      {children}
      
      {/* Çevrimdışı bildirim */}
      {!isOnline && showOfflineMessage && (
        <div 
          className="fixed bottom-4 left-0 right-0 mx-auto w-11/12 max-w-md bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 rounded shadow-lg z-50 flex items-center justify-between"
          role="alert"
          aria-live="assertive"
        >
          <div className="flex items-center">
            <svg className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <span>{offlineMessage}</span>
          </div>
        </div>
      )}
    </>
  );
};

export default NetworkStatusMonitor;
