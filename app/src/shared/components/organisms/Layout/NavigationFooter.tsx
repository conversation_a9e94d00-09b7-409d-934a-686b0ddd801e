import { ReactNode, memo } from 'react';
import NavigationButton from '@shared/components/atoms/Button/NavigationButton';
import ScrollToTop from '@shared/components/atoms/Button/ScrollToTop';

interface NavigationFooterProps {
  prevSection?: {
    id: string;
    title: string;
    onClick: () => void;
    disabled?: boolean;
  };
  nextSection?: {
    id: string;
    title: string;
    onClick: () => void;
    disabled?: boolean;
  };
  middleContent?: ReactNode;
  className?: string;
}

const NavigationFooter = ({
  prevSection,
  nextSection,
  middleContent = <ScrollToTop />,
  className = ''
}: NavigationFooterProps) => {
  return (
    <div className={`flex items-center justify-between pt-8 pb-12 ${className}`}>
      {prevSection ? (
        <NavigationButton
          direction="prev"
          onClick={prevSection.onClick}
          title="Önceki"
          subtitle={prevSection.title}
          disabled={prevSection.disabled}
        />
      ) : <div />}

      {middleContent}

      {nextSection ? (
        <NavigationButton
          direction="next"
          onClick={nextSection.onClick}
          title="Sonraki"
          subtitle={nextSection.title}
          disabled={nextSection.disabled}
        />
      ) : <div />}
    </div>
  );
};

export default memo(NavigationFooter);
export { NavigationFooter }; 