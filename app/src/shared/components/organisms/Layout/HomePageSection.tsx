import React, { ReactNode } from 'react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface HomePageSectionProps {
  children: ReactNode;
  className?: string;
}

export const HomePageSection: React.FC<HomePageSectionProps> = ({ children, className = '' }) => {
  const sectionBgColor = useAutoOverlay(5, 'var(--bg-color)');
  const borderColor = useAutoOverlay(15, 'var(--bg-color)');

  return (
    <div
      className={`rounded-xl p-4 ${className}`}
      style={{
        backgroundColor: sectionBgColor,
      }}
    >
      {children}
    </div>
  );
};

export default HomePageSection;
