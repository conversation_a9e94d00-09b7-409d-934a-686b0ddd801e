import { ReactNode, FC } from 'react';
import { AppNavbar } from '@shared/components';

interface PageLayoutProps {
  title?: string;
  children: ReactNode;
  navbarActions?: ReactNode;
  navbarCenterContent?: ReactNode;
  secondRowContent?: ReactNode;
  showBackButton?: boolean;
  showNavbar?: boolean;
  showLogo?: boolean;
  navbarTwoRows?: boolean;
  showLoginButton?: boolean;
  showThemeToggle?: boolean;
  showMoreOptions?: boolean;
  onBackClick?: () => void;
  rtl?: boolean;
}

export const PageLayout: FC<PageLayoutProps> = ({
  title,
  children,
  navbarActions,
  navbarCenterContent,
  secondRowContent,
  showBackButton = true,
  showNavbar = true,
  showLogo,
  navbarTwoRows = false,
  showLoginButton = true,
  showThemeToggle = true,
  showMoreOptions = true,
  onBackClick,
  rtl = false,
}) => {
  return (
    <div className="flex flex-col min-h-screen">
      {showNavbar && (
        <AppNavbar
          title={title}
          actions={navbarActions}
          centerContent={navbarCenterContent}
          secondRowContent={secondRowContent}
          showBackButton={showBackButton}
          showLogo={showLogo}
          navbarTwoRows={navbarTwoRows}
          showLoginButton={showLoginButton}
          showThemeToggle={showThemeToggle}
          showMoreOptions={showMoreOptions}
          onBackClick={onBackClick}
          rtl={rtl}
        />
      )}
      <main className={`flex-grow ${navbarTwoRows ? 'pt-[64px]' : 'pt-11'}`}>
        {children}
      </main>
    </div>
  );
};
