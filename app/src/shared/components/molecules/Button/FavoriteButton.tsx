import { FC, memo } from 'react';
import { useAuthStore } from '@domains/auth/store/authStore';
import { useContentInteractionStore } from '@domains/reader-interactions/store/contentInteractionStore';
import { useFavoritableContent } from '@shared/hooks/useFavoritableContent';
import Star from '../../Icons/Star';

const FavoriteButton: FC = () => {
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const openLoginModal = useAuthStore(state => state.openLoginModal);
  const { isFavorite, toggleFavorite } = useContentInteractionStore();
  const content = useFavoritableContent();

  if (!content) {
    return null;
  }

  const isBookFavorite = isAuthenticated ? isFavorite(String(content.id)) : false;

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isAuthenticated) {
      openLoginModal();
      return;
    }
    toggleFavorite(content);
  };

  return (
    <button
      onClick={handleFavoriteClick}
      className={`relative p-2 border-none bg-transparent rounded cursor-pointer transition-all duration-200 hover:scale-110 ${
        isBookFavorite
          ? 'text-[var(--text-color)] hover:text-[var(--text-color)]/80'
          : 'text-[var(--text-color)]/70 hover:text-[var(--text-color)]'
      }`}
      aria-label={isBookFavorite ? 'Favorilerden kaldır' : 'Favorilere ekle'}
    >
      <Star filled={isBookFavorite} size={19} />
    </button>
  );
};

export default memo(FavoriteButton);
