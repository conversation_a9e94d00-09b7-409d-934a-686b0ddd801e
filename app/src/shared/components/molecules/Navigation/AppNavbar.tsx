import { ReactNode, memo, FC } from 'react';
import { useAutoOverlay } from '@shared/hooks';
import { useIsMobile } from '@shared/hooks/useIsMobile';
import { BackButton, AppLogo, ThemeToggle, LoginButton, MoreOptionsButton, FavoriteButton } from '@shared/components';

interface AppNavbarProps {
  title?: string;
  actions?: ReactNode;
  centerContent?: ReactNode;
  secondRowContent?: ReactNode;
  showBackButton?: boolean;
  showLogo?: boolean;
  showThemeToggle?: boolean;
  showMoreOptions?: boolean;
  showLoginButton?: boolean;
  navbarTwoRows?: boolean;
  onBackClick?: () => void;
  rtl?: boolean;
}

const AppNavbar: FC<AppNavbarProps> = ({
  title,
  actions,
  centerContent,
  secondRowContent,
  showBackButton = true,
  showLogo = true,
  showThemeToggle = true,
  showMoreOptions = true,
  showLoginButton = true,
  navbarTwoRows = false,
  onBackClick,
  rtl = false,
}: AppNavbarProps) => {
  const navBgColor = useAutoOverlay(10, 'var(--bg-color)');
  const isMobile = useIsMobile();

  const StartItems = (
    <div className="flex items-center justify-start h-full space-x-1">
      {showBackButton && <BackButton onClick={onBackClick} rtl={rtl} />}
      {showLogo && <AppLogo />}
    </div>
  );

  const EndItems = (
    <div className="flex items-center justify-end h-full space-x-1">
      {actions}
      <FavoriteButton />
      {showLoginButton && <LoginButton />}
      {showThemeToggle && <ThemeToggle />}
      {showMoreOptions && <MoreOptionsButton />}
    </div>
  );

  return (
    <nav
      id="page-layout-navbar"
      className="fixed top-0 z-40 w-full shadow-md"
      style={{ 
        backgroundColor: navBgColor, 
        borderBottom: `1px solid color-mix(in srgb, var(--text-color) 10%, transparent)`
      }}
    >
      <div className="w-full mx-auto px-4 md:px-6 lg:px-8 relative">
        <div className="flex items-center justify-between h-10">
          <div className="flex items-center">
            {rtl ? EndItems : StartItems}
          </div>
          
          <div className={`absolute left-1/2 top-1/2 -translate-x-1/2 flex items-center justify-center h-full px-2 ${isMobile ? '-translate-y-3/4' : '-translate-y-1/2'}`}>
            {centerContent ? (
              centerContent
            ) : title ? (
              <h1 className="text-base font-medium truncate m-0 leading-none" style={{ color: 'var(--text-color)' }}>
                {title}
              </h1>
            ) : null}
          </div>

          <div className="flex items-center">
            {rtl ? StartItems : EndItems}
          </div>
        </div>
        
        {navbarTwoRows && secondRowContent && (
          <div 
            id="page-layout-second-row"
            className="flex items-center justify-center h-6 pb-1 -mt-1">
            {secondRowContent}
          </div>
        )}
      </div>
    </nav>
  );
};

export default memo(AppNavbar);
export { AppNavbar };
