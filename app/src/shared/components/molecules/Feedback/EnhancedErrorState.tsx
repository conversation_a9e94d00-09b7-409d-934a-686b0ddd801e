import { ReactNode } from 'react';

interface EnhancedErrorStateProps {
  error: Error | string | null;
  errorType?: 'network' | 'content' | 'auth' | 'permission' | 'general';
  onRetry?: () => void;
  customMessage?: string;
  customAction?: ReactNode;
  showHomeButton?: boolean;
}

/**
 * Gelişmiş hata durumu bileşeni.
 * Farklı hata türlerine göre özelleştirilmiş mesajlar ve eylemler sunar.
 */
export const EnhancedErrorState = ({
  error,
  errorType = 'general',
  onRetry,
  customMessage,
  customAction,
  showHomeButton = true
}: EnhancedErrorStateProps) => {
  // Hata mesajını belirle
  const errorMessage = typeof error === 'string' ? error : error?.message || 'Bilinmeyen bir hata oluştu';
  
  // Hata türüne göre başlık ve açıklama belirle
  let title = 'Bir Hata Oluştu';
  let description = customMessage || errorMessage;
  let icon = '⚠️';

  switch (errorType) {
    case 'network':
      title = 'Bağlantı Hatası';
      description = customMessage || 'İnternet bağlantınızda bir sorun oluştu. Lütfen bağlantınızı kontrol edip tekrar deneyin.';
      icon = '📶';
      break;
    case 'content':
      title = 'İçerik Yüklenemedi';
      description = customMessage || 'İstediğiniz içerik şu anda yüklenemiyor. Bu geçici bir sorun olabilir.';
      icon = '📚';
      break;
    case 'auth':
      title = 'Oturum Hatası';
      description = customMessage || 'Oturumunuzla ilgili bir sorun oluştu. Lütfen tekrar giriş yapın.';
      icon = '🔐';
      break;
    case 'permission':
      title = 'Erişim Reddedildi';
      description = customMessage || 'Bu içeriğe erişim izniniz bulunmuyor.';
      icon = '🚫';
      break;
    default:
      // Varsayılan değerler yukarıda tanımlandı
      break;
  }

  return (
    <div className="min-h-[50vh] flex flex-col items-center justify-center p-4 text-center" style={{ color: 'var(--text-color)' }}>
      <div className="text-6xl mb-4">{icon}</div>
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <p className="mb-6 max-w-md opacity-80">{description}</p>
      
      <div className="flex flex-wrap gap-3 justify-center">
        {onRetry && (
          <button
            onClick={onRetry}
            className="px-4 py-2 rounded-full bg-[var(--color-accent)] text-white hover:opacity-90 transition-opacity"
            aria-label="Tekrar dene"
          >
            Tekrar Dene
          </button>
        )}
        
        {showHomeButton && (
          <a
            href="/"
            className="px-4 py-2 rounded-full border border-[var(--text-color)] hover:bg-[var(--text-color)]/5 transition-colors"
            aria-label="Ana sayfaya dön"
          >
            Ana Sayfaya Dön
          </a>
        )}
        
        {customAction}
      </div>
      
      {/* Teknik hata detayları (geliştirici için) */}
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-8 text-left w-full max-w-md">
          <summary className="cursor-pointer text-sm opacity-60 hover:opacity-100">Teknik Detaylar</summary>
          <pre className="mt-2 p-3 bg-[var(--text-color)]/5 rounded text-xs overflow-auto">
            {errorMessage}
          </pre>
        </details>
      )}
    </div>
  );
};

export default EnhancedErrorState;
