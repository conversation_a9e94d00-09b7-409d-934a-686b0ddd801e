interface ErrorStateProps {
  message: string;
  onRetry?: () => void;
}

export const ErrorState = ({ message, onRetry }: ErrorStateProps) => {
  return (
    <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-color)' }}>
      <div className="text-center space-y-4">
        <div style={{ color: 'var(--text-color)' }}>{message}</div>
        {onRetry && (
          <button
            onClick={onRetry}
            className="flex items-center gap-2 px-4 py-2 rounded hover:bg-[var(--text-color)]/5"
            style={{ color: 'var(--text-color)' }}
          >
            <span>Tekrar Dene</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorState; 