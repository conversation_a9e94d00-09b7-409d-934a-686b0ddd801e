import React from 'react';
import { X, AlertTriangle } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Onayla',
  cancelText = 'İptal',
  isLoading = false,
}) => {
  const modalBgColor = useAutoOverlay(10, 'var(--bg-color)');
  const borderColor = useAutoOverlay(20, 'var(--bg-color)');

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div
        className="relative w-full max-w-md rounded-2xl shadow-2xl border flex flex-col"
        style={{ backgroundColor: modalBgColor, borderColor: borderColor }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6 text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold mt-4" style={{ color: 'var(--text-color)' }}>
            {title}
          </h3>
          <p className="text-sm mt-2" style={{ color: 'var(--text-color)', opacity: 0.7 }}>
            {message}
          </p>
        </div>
        <div className="flex gap-3 p-4 bg-black/10 rounded-b-2xl">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 py-2.5 px-4 border rounded-lg disabled:opacity-50 transition-colors font-medium hover:bg-gray-500/10"
            style={{ borderColor: borderColor, color: 'var(--text-color)' }}
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className="flex-1 flex items-center justify-center py-2.5 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold bg-red-600 text-white hover:bg-red-700"
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
            ) : (
              confirmText
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
