import { FC, useEffect, useState } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';

interface IErrorToastProps {
  message: string;
  onClose: () => void;
  duration?: number; // ms
}

const ErrorToast: FC<IErrorToastProps> = ({ message, onClose, duration = 5000 }) => {
  const [isVisible, setIsVisible] = useState(true);
  const bgColor = useAutoOverlay(15, 'var(--bg-color)');
  const borderColor = useAutoOverlay(25, 'var(--bg-color)');

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300); // Animation duration
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300);
  };

  return (
    <div
      className={`fixed top-4 right-4 z-50 max-w-sm w-full transition-all duration-300 ${
        isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
      }`}
    >
      <div
        className="flex items-start gap-3 p-4 rounded-lg border shadow-lg"
        style={{
          backgroundColor: bgColor,
          borderColor: borderColor,
        }}
      >
        <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
        <div className="flex-1 min-w-0">
          <p className="text-sm text-[var(--text-color)] break-words">
            {message}
          </p>
        </div>
        <button
          onClick={handleClose}
          className="flex-shrink-0 p-1 hover:bg-[var(--text-color)]/10 rounded transition-colors"
          aria-label="Kapat"
        >
          <X className="w-4 h-4 text-[var(--text-color)]/70" />
        </button>
      </div>
    </div>
  );
};

export default ErrorToast;
