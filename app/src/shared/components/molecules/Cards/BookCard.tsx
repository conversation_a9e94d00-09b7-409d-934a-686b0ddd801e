import { memo, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { IBook, ICategory } from '@domains/library/models/types';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { useContentInteractionStore } from '@domains/reader-interactions/store/contentInteractionStore';
import { useAuthStore } from '@domains/auth/store/authStore';

interface IBookCardProps {
  /** Gösterilecek kitap */
  book: IBook;
  /** Kitabın ait olduğu kategori */
  category: ICategory;
  /** Card'a tıklandığında tetiklenecek fonksiyon */
  onClick?: (book: IBook) => void;
  /** Özel CSS sınıfı */
  className?: string;
  /** Kitap detay sayfasına gitmek yerine özel işleme */
  disableNavigation?: boolean;
}

const BookCard = ({ book, category, onClick, className, disableNavigation }: IBookCardProps) => {
  const mixedBgColor = useAutoOverlay(15, 'var(--bg-color)');
  const { recordActivity } = useContentInteractionStore();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  // Kullanıcı kitaba tıkladığında aktiviteyi kaydet - memoized
  const handleCardClick = useMemo(() => () => {
    // Sadece giriş yapmış kullanıcılar için aktivite kaydet
    if (isAuthenticated) {
      recordActivity(String(book.id));
    }
    if (onClick) {
      onClick(book);
    }
  }, [isAuthenticated, recordActivity, book.id, onClick, book]);
  
  // Memoize the gradient style calculation
  const gradientStyle = useMemo(() => ({
    backgroundImage: `
      linear-gradient(rgba(0,0,0,0.15), rgba(0,0,0,0.15)),
      linear-gradient(145deg, 
        ${category.gradient_start || '#4A5568'} 0%,
        ${category.gradient_start || '#4A5568'} 40%,
        ${category.gradient_end || '#2D3748'} 99%,
        ${category.gradient_end || '#2D3748'} 100%
      )
    `,
    backgroundBlendMode: 'multiply',
    color: '#FFFFFF',
    border: 'none',
    borderRadius: '10px 10px 0 0',
    cursor: 'pointer',
    height: '21px'
  }), [category.gradient_start, category.gradient_end]);

  // Determine navigation path based on book category
  const navigationPath = useMemo(() => {
    if (category.id === 1) { // Kur'an kategorisi
      // Kitap ID'sine göre farklı sure sayfalarına yönlendir (Ancak ID'yi URL'e ekleme!)
      switch (book.id) {
        case 1:
          return `/kuran-sureler`;  // Sadece base path
        case 2:
          return `/mealli-kuran-sureler`; // Sadece base path
        case 3:
          return `/kelime-mealli-kuran-sureler`; // Sadece base path
        case 4:
          return `/kuran-meali-sureler`; // Sadece base path
        default:
          return `/kuran-sureler`; // Varsayılan
      }
    } else if (category.id === 2 || category.id === 3) {
      return `/risale/${book.id}`; // Risale için ID gerekli
    } else if (category.id === 4) { // Risale için ID gerekli
      return `/tafsir/${book.id}`;
    }
    return `/book/${book.id}`; // Diğer kitaplar için ID gerekli olabilir
  }, [category.id, book.id]);

  const cardContent = (
    <>
      {/* Upper gradient section */}
      <div
        className="gradient-section rounded-t-lg"
        style={gradientStyle}
        aria-hidden="true"
      />
      {/* Lower text section */}
      <div
        className="text-section p-[10px_12px] rounded-b-lg flex-1 flex flex-col justify-center"
        style={{
          backgroundColor: mixedBgColor,
          color: 'var(--text-color)'
        }}
      >
        <h3 className="m-0 text-base font-medium leading-snug">
          {book.title}
        </h3>
        {book.arabic_title && (
          <p className="m-0 mt-1 text-base font-arabic opacity-80 text-right">
            {book.arabic_title}
          </p>
        )}
            {book.author !== 'Unknown' && (
              <div className="mt-1 text-[0.8rem] opacity-80">
                <p className="mb-0">{book.author}</p>
                {book.arabic_author && (
                  <p className="mb-0 font-arabic text-right text-sm">{book.arabic_author}</p>
                )}
              </div>
            )}
      </div>
    </>
  );

  return (
    <div 
      className={`book-card-wrapper w-full max-w-[400px] flex flex-col ${className || ''}`}
    >
      {disableNavigation ? (
        <div
          className="book-card w-full p-0 m-0 rounded-lg overflow-hidden cursor-pointer bg-transparent flex flex-col flex-1"
          onClick={handleCardClick}
        >
          {cardContent}
        </div>
      ) : (
        <Link
          to={navigationPath}
          className="book-card w-full p-0 m-0 rounded-lg overflow-hidden cursor-pointer bg-transparent flex flex-col flex-1 no-underline"
          aria-label={`${book.title}${book.author !== 'Unknown' ? `, Yazar: ${book.author}` : ''}`}
          onClick={handleCardClick} // Tıklama olayını buraya bağla
        >
          {cardContent}
        </Link>
      )}
    </div>
  );
};

export default memo(BookCard);
export { BookCard }; 