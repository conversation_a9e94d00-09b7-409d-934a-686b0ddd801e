import React, { useMemo } from 'react';
import type { CSSProperties } from 'react';
import { useNavigate } from 'react-router-dom';

interface TagsExploreCardProps {
  className?: string;
}

const TagsExploreCard: React.FC<TagsExploreCardProps> = ({ className = '' }) => {
  const navigate = useNavigate();

  const handleCardClick = () => {
    navigate('/tags');
  };

  // Zenginleştirilmiş, çok katmanlı ve filtreli gradyan stili
  const cardStyle = useMemo<CSSProperties>(() => ({
    backgroundImage: `
      linear-gradient(rgba(0,0,0,0.35), rgba(0,0,0,0.35)),
      radial-gradient(circle at top left, color-mix(in srgb, var(--text-color) 60%, transparent) 0%, transparent 50%),
      radial-gradient(circle at bottom right, color-mix(in srgb, var(--text-color) 40%, transparent) 0%, transparent 40%),
      linear-gradient(145deg, 
        color-mix(in srgb, var(--text-color) 15%, var(--bg-color)) 0%,
        color-mix(in srgb, var(--text-color) 40%, var(--bg-color)) 100%
      )
    `,
    backgroundBlendMode: 'multiply',
    color: 'white',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    cursor: 'pointer',
  }) as CSSProperties, []);

  return (
    <div
      className={`card-base h-full w-full rounded-lg overflow-hidden p-3 ${className}`}
      style={cardStyle}
      onClick={handleCardClick}
      aria-label="Etiketleri keşfet"
    >
      <div style={{ textShadow: '0 1px 4px rgba(0,0,0,0.6)' }}>
        <h3 className="m-0 text-base font-medium leading-snug">Etiketleri keşfet</h3>
      </div>
    </div>
  );
};

export default TagsExploreCard;
