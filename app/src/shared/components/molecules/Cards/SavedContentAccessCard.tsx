import { memo, useMemo } from 'react';
import { Link } from 'react-router-dom';

interface SavedContentAccessCardProps {
  className?: string;
}

const SavedContentAccessCard = ({ className = '' }: SavedContentAccessCardProps) => {
  // Zenginleştirilmiş, çok katmanlı ve filtreli gradyan stili
  const cardStyle = useMemo(() => ({
    backgroundImage: `
      linear-gradient(rgba(0,0,0,0.35), rgba(0,0,0,0.35)),
      radial-gradient(circle at top left, color-mix(in srgb, var(--text-color) 60%, transparent) 0%, transparent 50%),
      radial-gradient(circle at bottom right, color-mix(in srgb, var(--text-color) 40%, transparent) 0%, transparent 40%),
      linear-gradient(145deg, 
        color-mix(in srgb, var(--text-color) 15%, var(--bg-color)) 0%,
        color-mix(in srgb, var(--text-color) 40%, var(--bg-color)) 100%
      )
    `,
    backgroundBlendMode: 'multiply',
    color: 'white',
    display: 'flex',
    flexDirection: 'column' as const,
    justifyContent: 'flex-start', // İçeriği yukarı yasla
    textDecoration: 'none',
  }), []);

  return (
    <Link
      to="/saved-content"
      className={`card-base h-full w-full rounded-lg overflow-hidden p-3 ${className}`}
      style={cardStyle}
      aria-label="Kaydettiğim İçerikler"
    >
      <div 
        style={{ textShadow: '0 1px 4px rgba(0,0,0,0.6)' }}
      >
        <h3 className="m-0 text-base font-medium leading-snug">
          Kaydettiğim İçerikler
        </h3>
      </div>
    </Link>
  );
};

export default memo(SavedContentAccessCard);
export { SavedContentAccessCard };
