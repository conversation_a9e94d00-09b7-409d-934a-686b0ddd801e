import React, { useMemo } from 'react';
import { useAutoOverlay } from '@shared/hooks/autooverlay'; // Corrected casing

interface TooltipProps {
  content: React.ReactNode;
  position: { top: number; left: number } | null;
  isVisible: boolean;
  onClose?: () => void; // Optional close handler
}

const Tooltip: React.FC<TooltipProps> = ({ content, position, isVisible, onClose }) => {
  // Use the autoOverlay hook to get a dynamic background color
  // Changed overlay percentage to 21%
  const dynamicBgColor = useAutoOverlay(21, 'var(--bg-color)');

  if (!isVisible || !position) {
    return null;
  }

  // Smart placement: prefer above if space; else below. Anchor to top when above, bottom when below.
  const { tooltipStyle, arrowStyle } = useMemo(() => {
    const left = position.left;
    const triggerTop = (position as any).triggerTop ?? position.top;
    const triggerBottom = (position as any).triggerBottom ?? position.top;
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    const scrollY = window.scrollY || window.pageYOffset;
    const estimatedHeight = 120;
    const margin = 12;

    const spaceAbove = triggerTop - scrollY;
    const spaceBelow = viewportHeight - (triggerBottom - scrollY);

    let showAbove: boolean;
    if (spaceAbove >= estimatedHeight + margin) {
      showAbove = true;
    } else if (spaceBelow >= estimatedHeight + margin) {
      showAbove = false;
    } else {
      showAbove = spaceAbove >= spaceBelow;
    }

    const anchorTop = showAbove ? triggerTop : triggerBottom;

    // Adaptive width: allow wider tooltip for long text, but cap within viewport
    const maxWidthPx = Math.min(viewportWidth - 2 * margin, viewportWidth >= 1024 ? 560 : viewportWidth >= 640 ? 480 : 360);

    // Horizontal clamping to avoid overflow when width grows
    const halfWidth = maxWidthPx / 2;
    let adjustedLeft = left;
    if (left - halfWidth < margin) {
      adjustedLeft = margin + halfWidth;
    } else if (left + halfWidth > viewportWidth - margin) {
      adjustedLeft = viewportWidth - margin - halfWidth;
    }

    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      top: `${anchorTop}px`,
      left: `${adjustedLeft}px`,
      transform: showAbove ? 'translate(-50%, calc(-100% - 8px))' : 'translate(-50%, 8px)',
      zIndex: 1000,
      backgroundColor: dynamicBgColor || 'var(--bg-color)',
      color: 'var(--tooltip-text-color, var(--text-color))',
      padding: '0.6rem 0.9rem',
      borderRadius: '6px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
      display: 'inline-block',
      maxWidth: `${maxWidthPx}px`,
      fontSize: '0.9rem',
      lineHeight: '1.5',
      wordBreak: 'break-word',
      overflowWrap: 'anywhere',
    };

    const arrowCommon: React.CSSProperties = {
      position: 'absolute',
      left: '50%',
      transform: 'translateX(-50%)',
      width: 0,
      height: 0,
      borderLeft: '11px solid transparent',
      borderRight: '11px solid transparent',
    };

    const arrow = showAbove
      ? { ...arrowCommon, bottom: '-11px', borderTop: `11px solid ${dynamicBgColor || 'var(--bg-color)'}` }
      : { ...arrowCommon, top: '-11px', borderBottom: `11px solid ${dynamicBgColor || 'var(--bg-color)'}` };

    return { tooltipStyle: baseStyle, arrowStyle: arrow } as const;
  }, [position.top, position.left, dynamicBgColor]);

  // arrowStyle computed above

  // Style for the overlay to close the tooltip
  const overlayStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 999, // Below tooltip, above content
  };

  return (
    <>
      {/* Overlay for closing */} 
      {onClose && <div style={overlayStyle} onClick={onClose} />} 
      
      {/* Tooltip Container (Relative for Arrow Positioning) */}
      {/* Note: The outer div seems unnecessary now, applying positioning directly */} 
      <div style={tooltipStyle}> 
        {content}
        {/* Arrow Element */}
        <div style={arrowStyle}></div>
      </div>
    </>
  );
};

export default Tooltip; 