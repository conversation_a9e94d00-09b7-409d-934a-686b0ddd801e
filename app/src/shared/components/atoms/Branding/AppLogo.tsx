import { Link } from 'react-router-dom';

interface AppLogoProps {
  size?: 'small' | 'medium' | 'large';
}

export const AppLogo = ({ size = 'medium' }: AppLogoProps) => {
  const fontSizes = {
    small: '0.875rem',
    medium: '1rem',
    large: '1.25rem'
  };
  
  return (
    <Link
      to="/"
      className="font-bold ml-2 transition-opacity hover:opacity-80"
      style={{ color: 'var(--text-color)', fontSize: fontSizes[size] }}
    >
      İkra Kitabe
    </Link>
  );
};

export default AppLogo; 