import { memo } from 'react';
import { useTheme } from '@shared/context/ThemeContext';

interface ThemeToggleProps {
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const ThemeToggle = ({ 
  size = 'medium',
  className = ''
}: ThemeToggleProps) => {
  const { currentTheme, cycleTheme } = useTheme();
  const ThemeIcon = currentTheme.icon;
  
  const sizeMap = {
    small: 16,
    medium: 19,
    large: 24,
  };
  
  return (
    <button
      onClick={cycleTheme}
      className={`p-2 rounded-full transition-colors hover:bg-[var(--text-color)]/10 text-[var(--text-color)] ${className}`}
      aria-label="Tema Değiştir"
      title={currentTheme.name}
    >
      <ThemeIcon size={sizeMap[size]} strokeWidth={2.2} />
    </button>
  );
};

export default memo(ThemeToggle);
export { ThemeToggle }; 