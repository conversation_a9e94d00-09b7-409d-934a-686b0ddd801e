import { Settings } from 'lucide-react';

interface SettingsButtonProps {
  onClick: () => void;
  size?: number;
}

export const SettingsButton = ({ onClick, size = 18 }: SettingsButtonProps) => {
  return (
    <button
      onClick={onClick}
      className="p-2 rounded-full transition-colors"
      style={{ color: 'var(--text-color)' }}
      aria-label="Settings"
    >
      <Settings size={size} />
    </button>
  );
};

export default SettingsButton; 