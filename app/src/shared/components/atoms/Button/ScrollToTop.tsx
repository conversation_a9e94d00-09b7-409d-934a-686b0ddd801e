import { ChevronUp } from 'lucide-react';

interface ScrollToTopProps {
  label?: string;
}

export const ScrollToTop = ({ label = "Başa Dön" }: ScrollToTopProps) => {
  return (
    <button
      onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
      className="flex flex-col items-center gap-1 px-4 py-2 rounded hover:bg-[var(--text-color)]/5"
      style={{ color: 'var(--text-color)' }}
      aria-label={label}
    >
      <ChevronUp size={20} />
      <div className="text-sm">{label}</div>
    </button>
  );
};

export default ScrollToTop; 