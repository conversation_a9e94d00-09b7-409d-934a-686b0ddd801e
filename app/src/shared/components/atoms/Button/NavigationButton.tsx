import { memo } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface NavigationButtonProps {
  direction: 'prev' | 'next';
  onClick: () => void;
  title: string;
  subtitle?: string;
  disabled?: boolean;
  isRtl?: boolean;
}

const NavigationButton = ({ 
  direction = 'next', 
  onClick, 
  title, 
  subtitle,
  disabled = false,
  isRtl = false
}: NavigationButtonProps) => {
  const isNext = direction === 'next';
  const textAlignClass = isNext ? 'text-right' : 'text-left';
  const finalTextAlign = isRtl ? (isNext ? 'text-left' : 'text-right') : textAlignClass;

  const PrevIcon = isRtl ? ChevronRight : ChevronLeft;
  const NextIcon = isRtl ? ChevronLeft : ChevronRight;

  return (
    <button 
      onClick={onClick}
      disabled={disabled}
      className={`flex items-center gap-2 px-4 py-2 rounded hover:bg-[var(--text-color)]/5 ${finalTextAlign} text-[var(--text-color)] disabled:opacity-50 disabled:cursor-not-allowed`}
    >
      {isNext ? null : <PrevIcon size={20} />}
      <div>
        {subtitle ? (
          <>
            <div className="text-sm text-[var(--text-color)]">{title}</div>
            <div className="text-lg">{subtitle}</div>
          </>
        ) : (
          <div className="text-lg">{title}</div>
        )}
      </div>
      {isNext ? <NextIcon size={20} /> : null}
    </button>
  );
};

export default memo(NavigationButton);
export { NavigationButton }; 