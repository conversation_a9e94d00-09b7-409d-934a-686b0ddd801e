import { forwardRef, ButtonHTMLAttributes, useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@shared/context/ThemeContext';
import { Moon, Sun, Coffee, Search, Palette } from 'lucide-react';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import { ThemeMode } from '@shared/theme/definitions';

interface MoreOptionsButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  // Add any specific props if needed, otherwise inherit from ButtonHTMLAttributes
  onClick?: () => void; // Keep onClick optional if it is
}

// Wrap the component with forwardRef
const MoreOptionsButton = forwardRef<HTMLButtonElement, MoreOptionsButtonProps>((
  { onClick, ...props }, 
  ref // Receive the ref
) => {
  const [isThemeSheetOpen, setIsThemeSheetOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { currentTheme, setTheme } = useTheme();
  const navigate = useNavigate();
  
  // Özel renklerin aktif olup olmadığını takip et
  const [hasCustomColors, setHasCustomColors] = useState(false);

  const navBgColor = useAutoOverlay(11, 'var(--bg-color)');

  // Sayfa yüklendiğinde kayıtlı renkleri uygula (sadece özel renkler varsa)
  useEffect(() => {
    const savedTextColor = localStorage.getItem('custom-text-color');
    const savedBgColor = localStorage.getItem('custom-bg-color');
    
    // Özel renkler varsa ve tema değişmemişse uygula
    if (savedTextColor || savedBgColor) {
      setHasCustomColors(true);
      if (savedTextColor) {
        document.documentElement.style.setProperty('--text-color', savedTextColor);
      }
      if (savedBgColor) {
        document.documentElement.style.setProperty('--bg-color', savedBgColor);
      }
    }
  }, []); // Sadece ilk yüklemede çalışsın

  // Tema değiştiğinde özel renkleri temizle
  useEffect(() => {
    if (hasCustomColors) {
      // Tema değişti, özel renkleri temizle
      localStorage.removeItem('custom-text-color');
      localStorage.removeItem('custom-bg-color');
      document.documentElement.style.removeProperty('--text-color');
      document.documentElement.style.removeProperty('--bg-color');
      setHasCustomColors(false);
    }
  }, [currentTheme.id]); // Tema ID'si değiştiğinde çalışsın

  const handleThemeChange = (theme: ThemeMode) => {
    setTheme(theme);
    setIsThemeSheetOpen(false);
    // Manuel tema değişiminde özel renkleri temizle
    localStorage.removeItem('custom-text-color');
    localStorage.removeItem('custom-bg-color');
    document.documentElement.style.removeProperty('--text-color');
    document.documentElement.style.removeProperty('--bg-color');
    setHasCustomColors(false);
  };

  const handleAnnotationSearch = () => {
    navigate('/annotations/search');
    setIsThemeSheetOpen(false);
  };

  const handleTextColorPicker = () => {
    const input = document.createElement('input');
    input.type = 'color';
    input.onchange = (e) => {
      const color = (e.target as HTMLInputElement).value;
      document.documentElement.style.setProperty('--text-color', color);
      localStorage.setItem('custom-text-color', color);
      setHasCustomColors(true); // Özel renk aktif edildi
    };
    input.click();
    setIsThemeSheetOpen(false);
  };

  const handleBgColorPicker = () => {
    const input = document.createElement('input');
    input.type = 'color';
    input.onchange = (e) => {
      const color = (e.target as HTMLInputElement).value;
      document.documentElement.style.setProperty('--bg-color', color);
      localStorage.setItem('custom-bg-color', color);
      setHasCustomColors(true); // Özel renk aktif edildi
    };
    input.click();
    setIsThemeSheetOpen(false);
  };

  const handleClick = () => {
    setIsThemeSheetOpen(!isThemeSheetOpen);
    if (onClick) onClick();
  };

  return (
    <>
      <button
        ref={ref || buttonRef} // Use passed ref or local ref
        onClick={handleClick}
        className="p-2 rounded-full transition-colors hover:bg-[var(--text-color)]/5"
        style={{ color: 'var(--text-color)' }}
        aria-label="Daha Fazla Seçenek"
        {...props} // Spread other props
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="5" cy="12" r="1"></circle>
          <circle cx="12" cy="12" r="1"></circle>
          <circle cx="19" cy="12" r="1"></circle>
        </svg>
      </button>

      {/* Tema Seçim Sheet */}
      {isThemeSheetOpen && (
        <>
          {/* Arka Plan Overlay */}
          <div 
            className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
            onClick={() => setIsThemeSheetOpen(false)}
          />
          
          {/* Tema Seçim Paneli */}
          <div 
            className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[320px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20 overflow-hidden flex flex-col"
            style={{
              backgroundColor: navBgColor,
              borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
            }}
          >
            {/* Pull indicator for mobile */}
            <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15 flex-shrink-0" />
            
            {/* Panel Header */}
            <div className="flex items-center justify-between p-3 border-b flex-shrink-0"
              style={{ borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}
            >
              <h3 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>Seçenekler</h3>
            </div>

            {/* Seçenekler */}
            <div className="p-4 space-y-2">
              {/* Şerh Arama */}
              <button
                className="w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 hover:bg-[var(--text-color)]/5 transition-colors"
                style={{ color: 'var(--text-color)' }}
                onClick={handleAnnotationSearch}
              >
                <Search size={20} />
                <span>Şerh Arama</span>
              </button>

              {/* Renk Seçenekleri */}
              <button
                className="w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 hover:bg-[var(--text-color)]/5 transition-colors"
                style={{ color: 'var(--text-color)' }}
                onClick={handleTextColorPicker}
              >
                <Palette size={20} />
                <span>Metin Rengi</span>
              </button>

              <button
                className="w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 hover:bg-[var(--text-color)]/5 transition-colors"
                style={{ color: 'var(--text-color)' }}
                onClick={handleBgColorPicker}
              >
                <Palette size={20} />
                <span>Arkaplan Rengi</span>
              </button>

              {/* Divider */}
              <div
                className="h-px mx-2 my-3"
                style={{ backgroundColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}
              />

              {/* Tema Başlığı */}
              <div className="px-4 py-2">
                <h4 className="text-sm font-medium opacity-70" style={{ color: 'var(--text-color)' }}>
                  Tema Seçin
                </h4>
              </div>
              <button 
                className={`w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 ${currentTheme.id === ThemeMode.LIGHT ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'}`}
                style={{ color: 'var(--text-color)' }}
                onClick={() => handleThemeChange(ThemeMode.LIGHT)}
              >
                <Sun size={20} />
                <span>Aydınlık Tema</span>
              </button>
              
              <button 
                className={`w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 ${currentTheme.id === ThemeMode.DARK ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'}`}
                style={{ color: 'var(--text-color)' }}
                onClick={() => handleThemeChange(ThemeMode.DARK)}
              >
                <Moon size={20} />
                <span>Karanlık Tema</span>
              </button>
              
              <button 
                className={`w-full text-left px-4 py-3 rounded-xl flex items-center gap-3 ${currentTheme.id === ThemeMode.KREM ? 'bg-[var(--text-color)]/10' : 'hover:bg-[var(--text-color)]/5'}`}
                style={{ color: 'var(--text-color)' }}
                onClick={() => handleThemeChange(ThemeMode.KREM)}
              >
                <Coffee size={20} />
                <span>Krem Tema</span>
              </button>
            </div>
          </div>
        </>
      )}
    </>
  );
});

MoreOptionsButton.displayName = 'MoreOptionsButton'; // Add display name for DevTools

export default MoreOptionsButton; 