import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface BackButtonProps {
  onClick?: () => void;
  rtl?: boolean;
}

const BackButton = ({ onClick, rtl = false }: BackButtonProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(-1);
    }
  };

  const Icon = rtl ? ChevronRight : ChevronLeft;

  return (
    <button
      onClick={handleClick}
      className="p-2 rounded-full hover:bg-gray-200 transition-colors duration-200"
      aria-label="Geri dön"
    >
      <Icon size={24} />
    </button>
  );
};

export default BackButton; 