import React, { useState, useEffect } from 'react';
import { User, Loader2 } from 'lucide-react';
import { useAuthStore } from '@domains/auth/store/authStore';

interface LoginButtonProps {
  onClick?: () => void;
}

const LoginButton: React.FC<LoginButtonProps> = ({ onClick }) => {
  const openLoginModal = useAuthStore(state => state.openLoginModal);
  const openProfileSheet = useAuthStore(state => state.openProfileSheet);
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const isLoading = useAuthStore(state => state.isLoading);
  const [showContent, setShowContent] = useState(false);

  // We add a small delay before showing the button content
  // This helps prevent the "Giriş Yap" flicker on page load
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowContent(true);
    }, 100); // Very short delay to reduce flash
    
    return () => clearTimeout(timer);
  }, []);
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (isAuthenticated) {
      openProfileSheet();
    } else {
      openLoginModal();
    }
  };
  
  // Don't show anything until we've had a chance to check auth status
  if (!showContent && isLoading) {
    return (
      <button
        aria-label="Loading"
        className="flex items-center justify-center p-1.5 rounded-lg"
        style={{ color: 'var(--text-color)' }}
        disabled
      >
        <Loader2 size={18} className="animate-spin" />
      </button>
    );
  }
  
  return (
    <button
      aria-label={isAuthenticated ? "Open Profile" : "Login"}
      className="flex items-center justify-center p-1.5 rounded-lg hover:bg-[var(--text-color)]/10 transition-colors duration-150"
      style={{ color: 'var(--text-color)' }}
      onClick={handleClick}
    >
      {isLoading ? (
        <Loader2 size={18} className="animate-spin" />
      ) : isAuthenticated ? (
        <User size={19} strokeWidth={2.2} />
      ) : (
        <span className="text-sm px-1">Giriş Yap</span>
      )}
    </button>
  );
};

export default LoginButton; 