import React from 'react';
import { useHighlightOptions } from '@shared/color/hooks/useHighlightOptions';

interface CompactBgColorPickerProps {
  selected?: string;
  onSelect: (identifier: string) => void;
  className?: string;
  includeNone?: boolean; // <PERSON><PERSON><PERSON>
}

const CompactBgColorPicker: React.FC<CompactBgColorPickerProps> = ({ selected, onSelect, className = '', includeNone = false }) => {
  const all = useHighlightOptions();

  // Sadece arka plan renkleri: HIGHLIGHT_COLOR_* (1..6) ve AUTO_BG_OVERLAY_*
  const bgOptions = all.filter(opt =>
    (typeof opt.value === 'string' &&
      (opt.value.startsWith('HIGHLIGHT_COLOR_') || opt.value.startsWith('AUTO_BG_OVERLAY_')))
  );

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {includeNone && (
        <button
          key="NO_COLOR"
          type="button"
          title="Renk Yok"
          onClick={() => onSelect('NO_COLOR')}
          className={`w-7 h-7 rounded-lg border-2 transition-all duration-150 ${selected === 'NO_COLOR' ? 'ring-2 ring-offset-1' : ''}`}
          style={{
            backgroundColor: 'transparent',
            borderColor: selected === 'NO_COLOR' ? 'var(--text-color)' : 'color-mix(in srgb, var(--text-color) 30%, transparent)'
          }}
        />
      )}
      {bgOptions.map((opt) => {
        const isSelected = selected === opt.value;
        return (
          <button
            key={opt.value}
            type="button"
            title={opt.name}
            onClick={() => onSelect(opt.value)}
            className={`w-7 h-7 rounded-lg border-2 transition-all duration-150 ${isSelected ? 'ring-2 ring-offset-1' : ''}`}
            style={{
              backgroundColor: opt.displayColor,
              borderColor: isSelected ? 'var(--text-color)' : 'transparent'
            }}
          />
        );
      })}
    </div>
  );
};

export default CompactBgColorPicker;


