import { memo, useEffect, useState } from 'react';
import { Check } from 'lucide-react';
import { useReaderInteractionStyles } from '@domains/reader-interactions/shared/hooks/useReaderInteractionStyles';
import { useTheme } from '@shared/context/ThemeContext';
import { supabase } from '@shared/utils/supabaseClient';
import { useHighlightOptions, ColorOption } from '../hooks/useHighlightOptions';
import type { IHighlightColors } from '@domains/settings/models/types';

interface UserSession {
  user?: {
    id?: string;
  } | null;
}

interface ColorPickerProps {
  selectedColor?: string;
  selectedStyle?: 'background' | 'text';
  onColorSelect: (color: string, style: 'background' | 'text', colorKey?: keyof IHighlightColors | 'auto_text_40' | 'auto_text_65' | 'auto_bg_25' | 'auto_bg_45') => void;
  className?: string;
}

const ColorPicker: React.FC<ColorPickerProps> = ({
  selectedColor,
  selectedStyle = 'background',
  onColorSelect,
  className = '',
}) => {
  const { bgColors, borderColors } = useReaderInteractionStyles();
  const { currentTheme } = useTheme(); // Re-renders on theme change
  const [session, setSession] = useState<UserSession | null>(null);
  
  // Merkezi hook'tan tüm renk seçeneklerini al
  const allColors = useHighlightOptions();

  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (_event, session: UserSession | null) => {
        setSession(session);
      }
    );
    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  const userId = session?.user?.id;

  const handleColorSelect = (color: ColorOption, style: 'background' | 'text') => {
    if (color.key && userId) {
      // Future: Implement color picker logic here
    }
    onColorSelect(color.value, style, color.key);
  };

  // Metin rengi için seçenekleri filtrele (şimdilik sadece statik renkler)
  const textColors = allColors.filter(c => c.key?.toString().startsWith('highlight_'));
  
  // Arka plan rengi için seçenekleri filtrele (statik + dinamik arka plan)
  const backgroundColors = allColors.filter(c => c.key?.toString().startsWith('highlight_') || c.key?.toString().startsWith('auto_bg'));

  const renderColorButtons = (colors: ColorOption[], style: 'text' | 'background') => {
    return colors.map((color) => {
      const isSelected = selectedColor === color.value && selectedStyle === style;
      const key = `${style}-${color.key || color.value}`;

      if (style === 'text') {
        return (
          <button
            key={key}
            title={color.name}
            className="relative w-7 h-7 rounded-lg box-border transition-colors duration-200 flex items-center justify-center"
            style={{
              backgroundColor: 'var(--bg-color)',
              borderColor: isSelected ? color.displayColor : 'transparent',
              borderWidth: '2px'
            }}
            onClick={() => handleColorSelect(color, 'text')}
          >
            <span 
              className="text-sm font-bold" 
              style={{ 
                color: color.displayColor
              }}
            >
              A
            </span>
            {isSelected && (
              <div className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-blue-500 flex items-center justify-center border-2 border-white dark:border-gray-800">
                <Check size={10} className="text-white" />
              </div>
            )}
          </button>
        );
      }

      // Arka plan stili
      return (
        <button
          key={key}
          title={color.name}
          className="relative w-7 h-7 rounded-lg border-2 transition-all duration-200 hover:scale-110 flex items-center justify-center"
          style={{
            backgroundColor: color.displayColor,
            borderColor: isSelected ? 'var(--text-color)' : 'transparent',
            boxShadow: `0 0 0 2px var(--bg-color)`,
          }}
          onClick={() => handleColorSelect(color, 'background')}
        >
          {isSelected && (
            <Check size={16} className="text-white drop-shadow-md" />
          )}
        </button>
      );
    });
  };

  return (
    <div
      className={`color-picker flex flex-col p-3 border shadow-lg rounded-lg ${className}`}
      style={{
        backgroundColor: bgColors.menu,
        borderColor: borderColors.default,
        zIndex: 9999, // ✅ En yüksek z-index
      }}
    >
      <div className="flex flex-col gap-2">
        {/* Metin Rengi Section */}
        <div className="flex flex-col gap-2">
          <h4 className="text-xs font-medium opacity-70" style={{ color: 'var(--text-color)' }}>
            Metin Rengi
          </h4>
          <div className="flex mx-auto gap-1" style={{ width: 124 }}>
            {renderColorButtons(textColors, 'text')}
          </div>
        </div>

        {/* Arka Plan Rengi Section */}
        <div className="flex flex-col gap-2">
          <h4 className="text-xs font-medium opacity-70" style={{ color: 'var(--text-color)' }}>
            Arka Plan Rengi
          </h4>
          <div className="grid grid-cols-3 gap-2">
            {renderColorButtons(backgroundColors, 'background')}
          </div>
        </div>
      </div>


    </div>
  );
};

export default memo(ColorPicker);
