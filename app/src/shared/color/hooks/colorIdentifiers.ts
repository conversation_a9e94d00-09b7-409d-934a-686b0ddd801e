/**
 * Color Identifier System
 * Database'de hesaplanmış renkler yerine identifier'lar sa<PERSON>
 * tema değişimlerinde tutarlılık sağlar
 */
import { rgbToHsl, parseColor } from './colorUtils';
import { useSettingsStore } from '@domains/settings/store/settingsstore';

function calculateAlchemicHue(baseColor: string): string {
  const baseRgb = parseColor(baseColor);
  if (!baseRgb) return baseColor;

  // Karıştırılacak renk: turuncu
  const orangeRgb = { r: 255, g: 165, b: 0 };

  // Adım 1: color-mix simülasyonu
  const mixedRgb = {
    r: baseRgb.r * 0.49 + orangeRgb.r * 0.51,
    g: baseRgb.g * 0.49 + orangeRgb.g * 0.51,
    b: baseRgb.b * 0.49 + orangeRgb.b * 0.51,
  };

  // Adım 2: HSL'e çevir
  const [mixedH, mixedS, mixedL] = rgbToHsl(mixedRgb.r, mixedRgb.g, mixedRgb.b);

  // Adım 3: hue-rotate(175deg) simülasyonu
  const finalHue = (mixedH + 175) % 360;

  return `hsl(${finalHue.toFixed(1)}, ${mixedS.toFixed(1)}%, ${mixedL.toFixed(1)}%)`;
}

// Refactored accent calculation functions for a more vibrant, diverse, and brighter color palette
// Sadece COLOR_ACCENT_1 için gerekli olan fonksiyon

export enum ColorIdentifier {
  // Sadece başka yerlerde kullanılan accent color kalsın
  COLOR_ACCENT_1 = 'COLOR_ACCENT_1', // AlchemicHue (turuncu karışım) - sistem için gerekli
  
  // Highlight colors
  HIGHLIGHT_COLOR_1 = 'HIGHLIGHT_COLOR_1',
  HIGHLIGHT_COLOR_2 = 'HIGHLIGHT_COLOR_2', 
  HIGHLIGHT_COLOR_3 = 'HIGHLIGHT_COLOR_3',
  HIGHLIGHT_COLOR_4 = 'HIGHLIGHT_COLOR_4',
  HIGHLIGHT_COLOR_5 = 'HIGHLIGHT_COLOR_5', // AUTO_OVERLAY_15
  HIGHLIGHT_COLOR_6 = 'HIGHLIGHT_COLOR_6', // AUTO_OVERLAY_25
  
  // Dynamic colors
  AUTO_OVERLAY_15 = 'AUTO_OVERLAY_15',
  AUTO_OVERLAY_25 = 'AUTO_OVERLAY_25',
  AUTO_TEXT_OVERLAY_40 = 'AUTO_TEXT_OVERLAY_40',
  AUTO_TEXT_OVERLAY_65 = 'AUTO_TEXT_OVERLAY_65',
  AUTO_BG_OVERLAY_25 = 'AUTO_BG_OVERLAY_25',
  AUTO_BG_OVERLAY_45 = 'AUTO_BG_OVERLAY_45',
}

/**
 * Color Identifier'ını gerçek CSS rengine çevirir
 */
// Cache için global değişkenler
let cachedTextColor: string | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 100; // 100ms cache

/**
 * Cache'i temizle - tema değişikliğinde kullan
 */
export function clearColorCache(): void {
  cachedTextColor = null;
  cacheTimestamp = 0;
}

function getCurrentTextColor(): string {
  const now = Date.now();
  
  // Cache geçerliyse onu kullan
  if (cachedTextColor && (now - cacheTimestamp) < CACHE_DURATION) {
    return cachedTextColor;
  }
  
  // CSS variable'ı farklı isimlerle dene
  let currentTextColor = getComputedStyle(document.documentElement)
    .getPropertyValue('--text-color').trim();
    
  if (!currentTextColor) {
    currentTextColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--text-primary').trim();
  }
  
  if (!currentTextColor) {
    currentTextColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--foreground').trim();
  }
  
  // Cache'le
  cachedTextColor = currentTextColor;
  cacheTimestamp = now;
  
  return currentTextColor;
}

export function resolveColorIdentifier(identifier: string): string {
  // 🎨 Kullanıcıya özel renkleri store'dan al
  const { highlightColors } = useSettingsStore.getState();

  // Highlight color sistemi için mapping
  const highlightColorMap: Record<string, string> = {
    'HIGHLIGHT_COLOR_1': highlightColors.highlight_color_1,
    'HIGHLIGHT_COLOR_2': highlightColors.highlight_color_2,
    'HIGHLIGHT_COLOR_3': highlightColors.highlight_color_3,
    'HIGHLIGHT_COLOR_4': highlightColors.highlight_color_4,
    'HIGHLIGHT_COLOR_5': highlightColors.highlight_color_5,
    'HIGHLIGHT_COLOR_6': highlightColors.highlight_color_6,
  };

  // Önce highlight color'lara bak
  if (highlightColorMap[identifier]) {
    const color = highlightColorMap[identifier];
    
    // Eğer dinamik renk ise (AUTO_OVERLAY formatında)
    if (color.startsWith('AUTO_OVERLAY_')) {
      const overlayPercentage = parseInt(color.split('_')[2]);
      // Auto overlay ile dinamik renk oluştur
      if (typeof document !== 'undefined') {
        const computedStyle = getComputedStyle(document.documentElement);
        const bgColor = computedStyle.getPropertyValue('--bg-color').trim() || '#ffffff';
        return `color-mix(in srgb, var(--text-color) ${overlayPercentage}%, ${bgColor})`;
      }
      // Fallback renkler
      return overlayPercentage === 15 ? '#00000026' : '#00000042';
    }
    
    return color;
  }

  // Dinamik renk identifiers
  if (identifier === 'AUTO_OVERLAY_15') {
    if (typeof document !== 'undefined') {
      const computedStyle = getComputedStyle(document.documentElement);
      const bgColor = computedStyle.getPropertyValue('--bg-color').trim() || '#ffffff';
      return `color-mix(in srgb, var(--text-color) 15%, ${bgColor})`;
    }
    return '#00000026'; // Fallback
  }
  
  if (identifier === 'AUTO_OVERLAY_25') {
    if (typeof document !== 'undefined') {
      const computedStyle = getComputedStyle(document.documentElement);
      const bgColor = computedStyle.getPropertyValue('--bg-color').trim() || '#ffffff';
      return `color-mix(in srgb, var(--text-color) 25%, ${bgColor})`;
    }
    return '#00000042'; // Fallback
  }

  if (identifier === 'AUTO_TEXT_OVERLAY_40') {
    if (typeof document !== 'undefined') {
      const bgColor = getComputedStyle(document.documentElement).getPropertyValue('--bg-color').trim() || '#ffffff';
      return `color-mix(in srgb, var(--text-color) 60%, ${bgColor})`;
    }
    return 'rgba(0,0,0,0.60)'; // Fallback
  }

  if (identifier === 'AUTO_TEXT_OVERLAY_65') {
    if (typeof document !== 'undefined') {
      const bgColor = getComputedStyle(document.documentElement).getPropertyValue('--bg-color').trim() || '#ffffff';
      return `color-mix(in srgb, var(--text-color) 35%, ${bgColor})`;
    }
    return 'rgba(0,0,0,0.35)'; // Fallback
  }

  if (identifier === 'AUTO_BG_OVERLAY_25') {
    if (typeof document !== 'undefined') {
      const textColor = getCurrentTextColor() || '#000000';
      return `color-mix(in srgb, ${textColor} 25%, var(--bg-color))`;
    }
    return 'rgba(0,0,0,0.25)'; // Fallback
  }

  if (identifier === 'AUTO_BG_OVERLAY_45') {
    if (typeof document !== 'undefined') {
      const textColor = getCurrentTextColor() || '#000000';
      return `color-mix(in srgb, ${textColor} 45%, var(--bg-color))`;
    }
    return 'rgba(0,0,0,0.45)'; // Fallback
  }

  const currentTextColor = getCurrentTextColor();
  
  // Sadece COLOR_ACCENT_1 için dinamik renk hesapla
  if (identifier === ColorIdentifier.COLOR_ACCENT_1) {
    if (!currentTextColor) {
      return '#3b82f6'; // Fallback color
    }
    return calculateAlchemicHue(currentTextColor);
  }
      
  // Eski sistem ile geriye dönük uyumluluk
  return identifier;
}

/**
 * Rengin dinamik (tema uyumlu) olup olmadığını kontrol eder
 */
export function isDynamicColorIdentifier(identifier: string): boolean {
  return identifier === ColorIdentifier.COLOR_ACCENT_1;
}
