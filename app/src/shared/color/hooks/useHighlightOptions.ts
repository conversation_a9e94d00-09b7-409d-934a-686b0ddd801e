import { useSettingsStore } from '@domains/settings/store/settingsstore';
import { useAutoOverlay } from '@shared/hooks/autooverlay';
import type { IHighlightColors } from '@domains/settings/models/types';

// ColorPicker'ın kullanacağı standart renk seçeneği formatı
export type ColorOption = {
  name: string;
  value: string;
  displayColor: string;
  key?: keyof IHighlightColors | 'auto_text_40' | 'auto_text_65' | 'auto_bg_25' | 'auto_bg_45';
};

/**
 * ColorPicker için statik ve dinamik renk seçeneklerini üreten merkezi hook.
 */
export const useHighlightOptions = (): ColorOption[] => {
  const { highlightColors } = useSettingsStore();

  // Dinamik renklerin önizlemesi için overlay hook'unu kullan
  const autoTextColor40 = useAutoOverlay(40, 'var(--text-color)');
  const autoTextColor65 = useAutoOverlay(65, 'var(--text-color)');
  const autoBgColor25 = useAutoOverlay(25, 'var(--bg-color)');
  const autoBgColor45 = useAutoOverlay(45, 'var(--bg-color)');

  // 1. Statik renkleri ayarlar'dan al ve formatla
  const staticOptions: ColorOption[] = Object.keys(highlightColors)
    .filter(key => {
      // Sadece ilk 4 statik rengi al
      const colorNumber = parseInt(key.split('_')[2], 10);
      return !isNaN(colorNumber) && colorNumber <= 4;
    })
    .map(key => {
      const colorKey = key as keyof IHighlightColors;
      const colorNumber = key.split('_')[2];
      const colorValue = highlightColors[colorKey];

      return {
        name: `Vurgu Rengi ${colorNumber}`,
        value: `HIGHLIGHT_COLOR_${colorNumber}`,
        displayColor: colorValue,
        key: colorKey,
      };
    });

  // 2. Dinamik (auto overlay) renk seçeneklerini oluştur
  const dynamicOptions: ColorOption[] = [
    // Text Colors
    {
      name: 'Dinamik Metin Rengi (%40)',
      value: 'AUTO_TEXT_OVERLAY_40',
      displayColor: autoTextColor40,
      key: 'auto_text_40',
    },
    {
      name: 'Dinamik Metin Rengi (%65)',
      value: 'AUTO_TEXT_OVERLAY_65',
      displayColor: autoTextColor65,
      key: 'auto_text_65',
    },
    // Background Colors
    {
      name: 'Dinamik Arka Plan Rengi (%25)',
      value: 'AUTO_BG_OVERLAY_25',
      displayColor: autoBgColor25,
      key: 'auto_bg_25',
    },
    {
      name: 'Dinamik Arka Plan Rengi (%45)',
      value: 'AUTO_BG_OVERLAY_45',
      displayColor: autoBgColor45,
      key: 'auto_bg_45',
    },
  ];

  // Normalde statik ve dinamik renkler birleştirilir,
  // ancak şimdilik sadece statik renkleri ve yeni dinamik arka plan rengini
  // ColorPicker'da ayrı ayrı göstermek daha mantıklı olabilir.
  // Bu hook şimdilik tüm opsiyonları hazırlasın, ColorPicker hangisini
  // kullanacağına karar verebilir.
  // Şimdilik hepsini birleştirelim.
  return [...staticOptions, ...dynamicOptions];
};
