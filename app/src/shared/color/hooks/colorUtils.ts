/**
 * Modern HSL-based color utilities
 * Replaces the chaotic autoHue approach with controlled, predictable color transformations
 */

/**
 * Background highlight için doğru opacity formatı oluştur
 */
export function createBackgroundColor(color: string, opacity: number = 0.6): string {
  if (color.startsWith('hsl(') && color.endsWith(')')) {
    // HSL'i HSLA'ya çevir: hsl(213, 72%, 35%) -> hsla(213, 72%, 35%, 0.6)
    const hslContent = color.slice(4, -1);
    return `hsla(${hslContent}, ${opacity})`;
  } else if (color.startsWith('#')) {
    // HEX color için hex opacity ekle
    const hexOpacity = Math.round(opacity * 255).toString(16).padStart(2, '0');
    return color + hexOpacity;
  }
  return color;
}

/**
 * RGB'yi HSL'e çevirir
 */
export function rgbToHsl(r: number, g: number, b: number): [number, number, number] {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return [h * 360, s * 100, l * 100];
}

/**
 * CSS renk değerini parse eder ve RGB değerlerini döndürür
 */
export function parseColor(color: string): { r: number; g: number; b: number } | null {
  // CSS değişkeni ise varsayılan bir değer kullan
  if (color.startsWith('var(')) {
    // Dark mode için varsayılan: beyazımsı
    // Light mode için varsayılan: siyahımsı
    const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    return isDarkMode ? { r: 230, g: 230, b: 230 } : { r: 30, g: 30, b: 30 };
  }

  // RGB/RGBA format
  const rgbMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
  if (rgbMatch) {
    return {
      r: parseInt(rgbMatch[1]),
      g: parseInt(rgbMatch[2]),
      b: parseInt(rgbMatch[3])
    };
  }

  // HEX format
  const hexMatch = color.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
  if (hexMatch) {
    return {
      r: parseInt(hexMatch[1], 16),
      g: parseInt(hexMatch[2], 16),
      b: parseInt(hexMatch[3], 16)
    };
  }

  return null;
}

