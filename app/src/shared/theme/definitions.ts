import { LucideIcon } from 'lucide-react';
import { Sun, Moon, Coffee } from 'lucide-react';

// <PERSON><PERSON> tü<PERSON> enum olarak tanımlama
export enum ThemeMode {
  DARK = 'dark',
  LIGHT = 'light',
  KREM = 'krem'
}

// Tema renkleri - basit versiyon + vurgu
export interface ThemeColors {
  text: string;
  background: string;
  accent: string; // Vurgu rengi eklendi
}

// Tema tanımı
export interface Theme {
  id: ThemeMode;
  name: string;
  icon: LucideIcon;
  colors: ThemeColors;
}

// Tema renkleri
const themeColors: Record<ThemeMode, ThemeColors> = {
  [ThemeMode.DARK]: {
    text: '#ffffff',
    background: '#0d0d0d',
    accent: '#3b82f6' // Mavi vurgu rengi (blue-500)
  },
  [ThemeMode.LIGHT]: {
    text: '#333333',
    background: '#ffffff',
    accent: '#3b82f6' // Mavi vurgu rengi (blue-500)
  },
  [ThemeMode.KREM]: {
    text: '#5c4b32',
    background: '#f5ecd8',
    accent: '#3b82f6' // Mavi vurgu rengi (blue-500)
  }
};

// Tema tanımlamaları
export const THEMES: Theme[] = [
  {
    id: ThemeMode.DARK,
    name: 'Karanlık Tema',
    icon: Sun, // Güneş ikonu = Aydınlık temaya geçiş
    colors: themeColors[ThemeMode.DARK]
  },
  {
    id: ThemeMode.LIGHT,
    name: 'Aydınlık Tema',
    icon: Coffee, // Kahve ikonu = Krem temaya geçiş
    colors: themeColors[ThemeMode.LIGHT]
  },
  {
    id: ThemeMode.KREM,
    name: 'Krem Tema',
    icon: Moon, // Ay ikonu = Karanlık temaya geçiş
    colors: themeColors[ThemeMode.KREM]
  }
];

// Tema döngüsü tanımlaması
export const THEME_CYCLE: Record<ThemeMode, ThemeMode> = {
  [ThemeMode.DARK]: ThemeMode.LIGHT,
  [ThemeMode.LIGHT]: ThemeMode.KREM,
  [ThemeMode.KREM]: ThemeMode.DARK
};

// Tema bulma yardımcı fonksiyonu
export const getThemeById = (id: ThemeMode): Theme => {
  return THEMES.find(theme => theme.id === id) || THEMES[0];
}; 