/**
 * R2 Client utility
 * Handles authenticated access to R2 storage via Cloudflare Worker
 */

// Use relative path to hit the same-origin Worker in prod and dev (Vite proxy)
const API_BASE = '/api/r2';

/**
 * Fetches a file from R2 via the Cloudflare Worker
 * @param path The path to the file in R2
 * @returns The file content
 * @throws Error if the request fails
 */
export async function fetchFromR2(path: string): Promise<Response> {
  // No API key required: public read with server-side allowlist & rate limit

  // Normalize path (remove leading slash if present)
  const normalizedPath = path.replace(/^\/+/, '');

  // Fetch from worker URL with required API key
  const response = await fetch(`${API_BASE}/${normalizedPath}`, {
    method: 'GET',
  });

  // Handle errors
  if (!response.ok) {
    throw new Error(`Request failed with status ${response.status}: ${response.statusText}`);
  }

  return response;
}

/**
 * Fetches JSON data from R2 via the Cloudflare Worker
 * @param path The path to the JSON file in R2
 * @returns The parsed JSON data
 * @throws Error if the request fails or if parsing fails
 */
export async function fetchJsonFromR2<T>(path: string): Promise<T> {
  const response = await fetchFromR2(path);
  return await response.json() as T;
}

/**
 * Fetches text data from R2 via the Cloudflare Worker
 * @param path The path to the text file in R2
 * @returns The text content
 * @throws Error if the request fails
 */
export async function fetchTextFromR2(path: string): Promise<string> {
  const response = await fetchFromR2(path);
  return await response.text();
}

/**
 * Checks if a file exists in R2
 * @param path The path to check
 * @returns true if the file exists, false otherwise
 */
export async function existsInR2(path: string): Promise<boolean> {
  try {
    const normalizedPath = path.replace(/^\/+/, '');

    const response = await fetch(`${API_BASE}/${normalizedPath}`, {
      method: 'HEAD',
    });

    return response.ok;
  } catch (error) {
    console.error('Error checking if file exists:', error);
    return false;
  }
}