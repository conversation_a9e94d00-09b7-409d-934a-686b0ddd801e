import { fetchJsonFromR2 } from './r2Client';

// Cache Map - shared across all calls to fetchData in this module instance
const cache = new Map<string, unknown>();

/**
 * Fetches JSON data from R2, utilizing an in-memory cache to avoid redundant requests.
 *
 * @param path The path to the JSON file in the R2 bucket (e.g., 'library/books.json').
 * @returns A promise that resolves with the parsed JSON data of type T.
 * @throws An error if the fetch from R2 fails.
 */
export async function fetchData<T>(path: string): Promise<T> {
  // 1. Check cache using the R2 path as the key
  if (cache.has(path)) {
    console.log(`[fetchData] Cache hit for R2 path: ${path}`);
    return Promise.resolve(cache.get(path) as T);
  }

  console.log(`[fetchData] Cache miss, fetching from R2: ${path}`);

  // 2. Fetch from R2 if not in cache
  try {
    const data = await fetchJsonFromR2<T>(path);

    // 3. Add to cache
    cache.set(path, data);
    console.log(`[fetchData] Successfully fetched and cached from R2: ${path}`);

    // 4. Return data
    return data;

  } catch (error) {
    console.error(`[fetchData] Error fetching from R2 for path ${path}:`, error);
    // Re-throw the error to be handled by the caller
    throw error;
  }
}

// Optional: Function to clear the entire cache
export function clearDataCache() {
  console.log('[fetchData] Clearing data cache.');
  cache.clear();
}

// Optional: Function to invalidate a specific cache entry
export function invalidateCacheEntry(path: string) {
   if (cache.has(path)) {
     cache.delete(path);
     console.log(`[fetchData] Invalidated cache for R2 path: ${path}`);
     return true;
   }
   return false;
} 