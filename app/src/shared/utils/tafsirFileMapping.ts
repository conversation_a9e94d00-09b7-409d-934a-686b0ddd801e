// Tafsir dosya mapping sistemi - dinamik dosya yapısı için
// Her surenin hangi dosyada bulunduğunu belirler

export interface TafsirFileMapping {
  surahId: number;
  fileName: string;
  startVerse: number;
  endVerse: number;
}

// Static mappings kaldırıldı - artık pattern-based dynamic mapping kullanılıyor

// Dosya isimlerinden otomatik mapping oluşturmak için parser
function parseFileName(fileName: string): TafsirFileMapping[] {
  const mappings: TafsirFileMapping[] = [];
  const parts = fileName.replace('.json', '').split('+');

  parts.forEach(part => {
    // 0-55, 1-55 gibi formatları destekle
    const match = part.match(/^(\d+)_(\d+)-(\d+)$/);
    if (match) {
      const [, surahId, startVerse, endVerse] = match;
      mappings.push({
        surahId: parseInt(surahId, 10),
        fileName,
        startVerse: parseInt(startVerse, 10),
        endVerse: parseInt(endVerse, 10)
      });
    }
  });

  return mappings;
}

// Pattern-based mapping sistemi - dosya isimlerinden otomatik mapping oluştur
async function generateMappingsForBook(_bookId: string): Promise<TafsirFileMapping[]> {
  try {
    // Önce hardcoded dosya listesini kontrol et
    const hardcodedMappings = getHardcodedMappings(_bookId);
    if (hardcodedMappings.length > 0) {
      return hardcodedMappings;
    }

    // Dynamic dosya listesi alma (fallback)
    const response = await fetch(`/data/tafsirs/${_bookId}/`);
    if (!response.ok) {
      throw new Error(`Kitap klasörü bulunamadı: ${_bookId}`);
    }

    // HTML response'tan dosya isimlerini çıkar
    const html = await response.text();
    const filePattern = /href="([^"]*\.json)"/g;
    const fileNames: string[] = [];
    let match;

    while ((match = filePattern.exec(html)) !== null) {
      fileNames.push(match[1]);
    }

    // Her dosya için mapping oluştur
    const allMappings: TafsirFileMapping[] = [];
    fileNames.forEach(fileName => {
      const mappings = parseFileName(fileName);
      allMappings.push(...mappings);
    });
    return allMappings.sort((a, b) => a.surahId - b.surahId);
  } catch (error) {
    console.error(`[generateMappingsForBook] Error for book ${_bookId}:`, error);
    return [];
  }
}

// Hardcoded mapping sistemi - bilinen kitaplar için önceden tanımlanmış mapping'ler
// EVRENSEL TAFSIR MAPPING SİSTEMİ
// Tüm tafsir kitapları aynı dosya yapısını kullandığından tek mapping yeterli
// Auto-generated from actual file names (0-based indexing)
const UNIVERSAL_TAFSIR_MAPPINGS: TafsirFileMapping[] = [
  { surahId: 1, fileName: '1_0-7+2_0-48.json', startVerse: 0, endVerse: 7 },
  { surahId: 2, fileName: '1_0-7+2_0-48.json', startVerse: 1, endVerse: 48 },
  { surahId: 2, fileName: '2_49-103.json', startVerse: 49, endVerse: 103 },
  { surahId: 2, fileName: '2_104-158.json', startVerse: 104, endVerse: 158 },
  { surahId: 2, fileName: '2_159-213.json', startVerse: 159, endVerse: 213 },
  { surahId: 2, fileName: '2_214-268.json', startVerse: 214, endVerse: 268 },
  { surahId: 2, fileName: '2_269-286+3_0-37.json', startVerse: 269, endVerse: 286 },
  { surahId: 3, fileName: '2_269-286+3_0-37.json', startVerse: 0, endVerse: 37 },
  { surahId: 3, fileName: '3_38-92.json', startVerse: 38, endVerse: 92 },
  { surahId: 3, fileName: '3_93-147.json', startVerse: 93, endVerse: 147 },
  { surahId: 3, fileName: '3_148-200.json', startVerse: 148, endVerse: 200 },
  { surahId: 4, fileName: '4_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 4, fileName: '4_56-110.json', startVerse: 56, endVerse: 110 },
  { surahId: 4, fileName: '4_111-165.json', startVerse: 111, endVerse: 165 },
  { surahId: 4, fileName: '4_166-176+5_0-44.json', startVerse: 166, endVerse: 176 },
  { surahId: 5, fileName: '4_166-176+5_0-44.json', startVerse: 0, endVerse: 44 },
  { surahId: 5, fileName: '5_45-99.json', startVerse: 45, endVerse: 99 },
  { surahId: 5, fileName: '5_100-120+6_0-34.json', startVerse: 100, endVerse: 120 },
  { surahId: 6, fileName: '5_100-120+6_0-34.json', startVerse: 0, endVerse: 34 },
  { surahId: 6, fileName: '6_35-89.json', startVerse: 35, endVerse: 89 },
  { surahId: 6, fileName: '6_90-144.json', startVerse: 90, endVerse: 144 },
  { surahId: 6, fileName: '6_145-165+7_0-34.json', startVerse: 145, endVerse: 165 },
  { surahId: 7, fileName: '6_145-165+7_0-34.json', startVerse: 0, endVerse: 34 },
  { surahId: 7, fileName: '7_35-89.json', startVerse: 35, endVerse: 89 },
  { surahId: 7, fileName: '7_90-144.json', startVerse: 90, endVerse: 144 },
  { surahId: 7, fileName: '7_145-199.json', startVerse: 145, endVerse: 199 },
  { surahId: 7, fileName: '7_200-206+8_0-48.json', startVerse: 200, endVerse: 206 },
  { surahId: 8, fileName: '7_200-206+8_0-48.json', startVerse: 0, endVerse: 48 },
  { surahId: 8, fileName: '8_49-75+9_0-28.json', startVerse: 49, endVerse: 75 },
  { surahId: 9, fileName: '8_49-75+9_0-28.json', startVerse: 0, endVerse: 28 },
  { surahId: 9, fileName: '9_29-83.json', startVerse: 29, endVerse: 83 },
  { surahId: 9, fileName: '9_84-129.json', startVerse: 84, endVerse: 129 },
  { surahId: 10, fileName: '10_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 10, fileName: '10_56-109.json', startVerse: 56, endVerse: 109 },
  { surahId: 11, fileName: '11_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 11, fileName: '11_56-110.json', startVerse: 56, endVerse: 110 },
  { surahId: 11, fileName: '11_111-123+12_0-42.json', startVerse: 111, endVerse: 123 },
  { surahId: 12, fileName: '11_111-123+12_0-42.json', startVerse: 0, endVerse: 42 },
  { surahId: 12, fileName: '12_43-97.json', startVerse: 43, endVerse: 97 },
  { surahId: 12, fileName: '12_98-111+13_0-41.json', startVerse: 98, endVerse: 111 },
  { surahId: 13, fileName: '12_98-111+13_0-41.json', startVerse: 0, endVerse: 41 },
  { surahId: 13, fileName: '13_42-43+14_0-52.json', startVerse: 42, endVerse: 43 },
  { surahId: 14, fileName: '13_42-43+14_0-52.json', startVerse: 0, endVerse: 52 },
  { surahId: 15, fileName: '15_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 15, fileName: '15_56-99.json', startVerse: 56, endVerse: 99 },
  { surahId: 16, fileName: '16_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 16, fileName: '16_56-110.json', startVerse: 56, endVerse: 110 },
  { surahId: 16, fileName: '16_111-128+17_0-37.json', startVerse: 111, endVerse: 128 },
  { surahId: 17, fileName: '16_111-128+17_0-37.json', startVerse: 0, endVerse: 37 },
  { surahId: 17, fileName: '17_38-92.json', startVerse: 38, endVerse: 92 },
  { surahId: 17, fileName: '17_93-111+18_0-36.json', startVerse: 93, endVerse: 111 },
  { surahId: 18, fileName: '17_93-111+18_0-36.json', startVerse: 0, endVerse: 36 },
  { surahId: 18, fileName: '18_37-91.json', startVerse: 37, endVerse: 91 },
  { surahId: 18, fileName: '18_92-110+19_0-36.json', startVerse: 92, endVerse: 110 },
  { surahId: 19, fileName: '18_92-110+19_0-36.json', startVerse: 0, endVerse: 36 },
  { surahId: 19, fileName: '19_37-91.json', startVerse: 37, endVerse: 91 },
  { surahId: 19, fileName: '19_92-98+20_0-48.json', startVerse: 92, endVerse: 98 },
  { surahId: 20, fileName: '19_92-98+20_0-48.json', startVerse: 0, endVerse: 48 },
  { surahId: 20, fileName: '20_49-103.json', startVerse: 49, endVerse: 103 },
  { surahId: 20, fileName: '20_104-135+21_0-23.json', startVerse: 104, endVerse: 135 },
  { surahId: 21, fileName: '20_104-135+21_0-23.json', startVerse: 0, endVerse: 23 },
  { surahId: 21, fileName: '21_24-78.json', startVerse: 24, endVerse: 78 },
  { surahId: 21, fileName: '21_79-112+22_0-21.json', startVerse: 79, endVerse: 112 },
  { surahId: 22, fileName: '21_79-112+22_0-21.json', startVerse: 0, endVerse: 21 },
  { surahId: 22, fileName: '22_22-76.json', startVerse: 22, endVerse: 76 },
  { surahId: 22, fileName: '22_77-78+23_0-53.json', startVerse: 77, endVerse: 78 },
  { surahId: 23, fileName: '22_77-78+23_0-53.json', startVerse: 0, endVerse: 53 },
  { surahId: 23, fileName: '23_54-108.json', startVerse: 54, endVerse: 108 },
  { surahId: 23, fileName: '23_109-118+24_0-45.json', startVerse: 109, endVerse: 118 },
  { surahId: 24, fileName: '23_109-118+24_0-45.json', startVerse: 0, endVerse: 45 },
  { surahId: 24, fileName: '24_46-64+25_0-36.json', startVerse: 46, endVerse: 64 },
  { surahId: 25, fileName: '24_46-64+25_0-36.json', startVerse: 0, endVerse: 36 },
  { surahId: 25, fileName: '25_37-77.json', startVerse: 37, endVerse: 77 },
  { surahId: 26, fileName: '26_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 26, fileName: '26_56-110.json', startVerse: 56, endVerse: 110 },
  { surahId: 26, fileName: '26_111-165.json', startVerse: 111, endVerse: 165 },
  { surahId: 26, fileName: '26_166-220.json', startVerse: 166, endVerse: 220 },
  { surahId: 26, fileName: '26_221-227+27_0-48.json', startVerse: 221, endVerse: 227 },
  { surahId: 27, fileName: '26_221-227+27_0-48.json', startVerse: 0, endVerse: 48 },
  { surahId: 27, fileName: '27_49-93.json', startVerse: 49, endVerse: 93 },
  { surahId: 28, fileName: '28_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 28, fileName: '28_56-88+29_0-22.json', startVerse: 56, endVerse: 88 },
  { surahId: 29, fileName: '28_56-88+29_0-22.json', startVerse: 0, endVerse: 22 },
  { surahId: 29, fileName: '29_23-69.json', startVerse: 23, endVerse: 69 },
  { surahId: 30, fileName: '30_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 30, fileName: '30_56-60+31_0-34+32_1-16.json', startVerse: 56, endVerse: 60 },
  { surahId: 31, fileName: '30_56-60+31_0-34+32_1-16.json', startVerse: 0, endVerse: 34 },
  { surahId: 32, fileName: '30_56-60+31_0-34+32_1-16.json', startVerse: 1, endVerse: 16 },
  { surahId: 32, fileName: '32_17-30+33_0-41.json', startVerse: 17, endVerse: 30 },
  { surahId: 33, fileName: '32_17-30+33_0-41.json', startVerse: 0, endVerse: 41 },
  { surahId: 33, fileName: '33_42-73+34_0-23.json', startVerse: 42, endVerse: 73 },
  { surahId: 34, fileName: '33_42-73+34_0-23.json', startVerse: 0, endVerse: 23 },
  { surahId: 34, fileName: '34_24-54+35_0-24.json', startVerse: 24, endVerse: 54 },
  { surahId: 35, fileName: '34_24-54+35_0-24.json', startVerse: 0, endVerse: 24 },
  { surahId: 35, fileName: '35_25-45+36_0-34.json', startVerse: 25, endVerse: 45 },
  { surahId: 36, fileName: '35_25-45+36_0-34.json', startVerse: 0, endVerse: 34 },
  { surahId: 36, fileName: '36_35-83.json', startVerse: 35, endVerse: 83 },
  { surahId: 37, fileName: '37_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 37, fileName: '37_56-110.json', startVerse: 56, endVerse: 110 },
  { surahId: 37, fileName: '37_111-165.json', startVerse: 111, endVerse: 165 },
  { surahId: 37, fileName: '37_166-182+38_0-38.json', startVerse: 166, endVerse: 182 },
  { surahId: 38, fileName: '37_166-182+38_0-38.json', startVerse: 0, endVerse: 38 },
  { surahId: 38, fileName: '38_39-88.json', startVerse: 39, endVerse: 88 },
  { surahId: 39, fileName: '39_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 39, fileName: '39_56-75+40_0-35.json', startVerse: 56, endVerse: 75 },
  { surahId: 40, fileName: '39_56-75+40_0-35.json', startVerse: 0, endVerse: 35 },
  { surahId: 40, fileName: '40_36-85.json', startVerse: 36, endVerse: 85 },
  { surahId: 41, fileName: '41_0-54.json', startVerse: 0, endVerse: 54 },
  { surahId: 42, fileName: '42_0-53.json', startVerse: 0, endVerse: 53 },
  { surahId: 43, fileName: '43_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 43, fileName: '43_56-89+44_0-21.json', startVerse: 56, endVerse: 89 },
  { surahId: 44, fileName: '43_56-89+44_0-21.json', startVerse: 0, endVerse: 21 },
  { surahId: 44, fileName: '44_22-59+45_0-17.json', startVerse: 22, endVerse: 59 },
  { surahId: 45, fileName: '44_22-59+45_0-17.json', startVerse: 0, endVerse: 17 },
  { surahId: 45, fileName: '45_18-37+46_0-35.json', startVerse: 18, endVerse: 37 },
  { surahId: 46, fileName: '45_18-37+46_0-35.json', startVerse: 0, endVerse: 35 },
  { surahId: 47, fileName: '47_0-38+48_0-17.json', startVerse: 0, endVerse: 38 },
  { surahId: 48, fileName: '47_0-38+48_0-17.json', startVerse: 0, endVerse: 17 },
  { surahId: 48, fileName: '48_18-29+49_0-18+50_0-25.json', startVerse: 18, endVerse: 29 },
  { surahId: 49, fileName: '48_18-29+49_0-18+50_0-25.json', startVerse: 0, endVerse: 18 },
  { surahId: 50, fileName: '48_18-29+49_0-18+50_0-25.json', startVerse: 0, endVerse: 25 },
  { surahId: 50, fileName: '50_26-45+51_0-35.json', startVerse: 26, endVerse: 45 },
  { surahId: 51, fileName: '50_26-45+51_0-35.json', startVerse: 0, endVerse: 35 },
  { surahId: 51, fileName: '51_36-60+52_0-30.json', startVerse: 36, endVerse: 60 },
  { surahId: 52, fileName: '51_36-60+52_0-30.json', startVerse: 0, endVerse: 30 },
  { surahId: 52, fileName: '52_31-49+53_0-36.json', startVerse: 31, endVerse: 49 },
  { surahId: 53, fileName: '52_31-49+53_0-36.json', startVerse: 0, endVerse: 36 },
  { surahId: 53, fileName: '53_37-62+54_0-29.json', startVerse: 37, endVerse: 62 },
  { surahId: 54, fileName: '53_37-62+54_0-29.json', startVerse: 0, endVerse: 29 },
  { surahId: 54, fileName: '54_30-55+55_0-29.json', startVerse: 30, endVerse: 55 },
  { surahId: 55, fileName: '54_30-55+55_0-29.json', startVerse: 0, endVerse: 29 },
  { surahId: 55, fileName: '55_30-78.json', startVerse: 30, endVerse: 78 },
  { surahId: 56, fileName: '56_0-55.json', startVerse: 0, endVerse: 55 },
  { surahId: 56, fileName: '56_56-96.json', startVerse: 56, endVerse: 96 },
  { surahId: 57, fileName: '57_0-29+58_0-22.json', startVerse: 0, endVerse: 29 },
  { surahId: 58, fileName: '57_0-29+58_0-22.json', startVerse: 0, endVerse: 22 },
  { surahId: 59, fileName: '59_0-24+60_0-13+61_0-14.json', startVerse: 0, endVerse: 24 },
  { surahId: 60, fileName: '59_0-24+60_0-13+61_0-14.json', startVerse: 0, endVerse: 13 },
  { surahId: 61, fileName: '59_0-24+60_0-13+61_0-14.json', startVerse: 0, endVerse: 14 },
  { surahId: 62, fileName: '62_0-11+63_0-11+64_0-18.json', startVerse: 0, endVerse: 11 },
  { surahId: 63, fileName: '62_0-11+63_0-11+64_0-18.json', startVerse: 0, endVerse: 11 },
  { surahId: 64, fileName: '62_0-11+63_0-11+64_0-18.json', startVerse: 0, endVerse: 18 },
  { surahId: 65, fileName: '65_0-12+66_0-12+67_0-30.json', startVerse: 0, endVerse: 12 },
  { surahId: 66, fileName: '65_0-12+66_0-12+67_0-30.json', startVerse: 0, endVerse: 12 },
  { surahId: 67, fileName: '65_0-12+66_0-12+67_0-30.json', startVerse: 0, endVerse: 30 },
  { surahId: 68, fileName: '68_0-52.json', startVerse: 0, endVerse: 52 },
  { surahId: 69, fileName: '69_0-52.json', startVerse: 0, endVerse: 52 },
  { surahId: 70, fileName: '70_0-44.json', startVerse: 0, endVerse: 44 },
  { surahId: 71, fileName: '71_0-28+72_0-27.json', startVerse: 0, endVerse: 28 },
  { surahId: 72, fileName: '71_0-28+72_0-27.json', startVerse: 0, endVerse: 27 },
  { surahId: 72, fileName: '72_28-28+73_0-20+74_0-34.json', startVerse: 28, endVerse: 28 },
  { surahId: 73, fileName: '72_28-28+73_0-20+74_0-34.json', startVerse: 0, endVerse: 20 },
  { surahId: 74, fileName: '72_28-28+73_0-20+74_0-34.json', startVerse: 0, endVerse: 34 },
  { surahId: 74, fileName: '74_35-56+75_0-33.json', startVerse: 35, endVerse: 56 },
  { surahId: 75, fileName: '74_35-56+75_0-33.json', startVerse: 0, endVerse: 33 },
  { surahId: 75, fileName: '75_34-40+76_0-31+77_0-17.json', startVerse: 34, endVerse: 40 },
  { surahId: 76, fileName: '75_34-40+76_0-31+77_0-17.json', startVerse: 0, endVerse: 31 },
  { surahId: 77, fileName: '75_34-40+76_0-31+77_0-17.json', startVerse: 0, endVerse: 17 },
  { surahId: 77, fileName: '77_18-50+78_0-22.json', startVerse: 18, endVerse: 50 },
  { surahId: 78, fileName: '77_18-50+78_0-22.json', startVerse: 0, endVerse: 22 },
  { surahId: 78, fileName: '78_23-40+79_0-37.json', startVerse: 23, endVerse: 40 },
  { surahId: 79, fileName: '78_23-40+79_0-37.json', startVerse: 0, endVerse: 37 },
  { surahId: 79, fileName: '79_38-46+80_0-42.json', startVerse: 38, endVerse: 46 },
  { surahId: 80, fileName: '79_38-46+80_0-42.json', startVerse: 0, endVerse: 42 },
  { surahId: 81, fileName: '81_0-29+82_0-19.json', startVerse: 0, endVerse: 29 },
  { surahId: 82, fileName: '81_0-29+82_0-19.json', startVerse: 0, endVerse: 19 },
  { surahId: 83, fileName: '83_0-36+84_0-19.json', startVerse: 0, endVerse: 36 },
  { surahId: 84, fileName: '83_0-36+84_0-19.json', startVerse: 0, endVerse: 19 },
  { surahId: 84, fileName: '84_20-25+85_0-22+86_0-17.json', startVerse: 20, endVerse: 25 },
  { surahId: 85, fileName: '84_20-25+85_0-22+86_0-17.json', startVerse: 0, endVerse: 22 },
  { surahId: 86, fileName: '84_20-25+85_0-22+86_0-17.json', startVerse: 0, endVerse: 17 },
  { surahId: 87, fileName: '87_0-19+88_0-26.json', startVerse: 0, endVerse: 19 },
  { surahId: 88, fileName: '87_0-19+88_0-26.json', startVerse: 0, endVerse: 26 },
  { surahId: 89, fileName: '89_0-30+90_0-20.json', startVerse: 0, endVerse: 30 },
  { surahId: 90, fileName: '89_0-30+90_0-20.json', startVerse: 0, endVerse: 20 },
  { surahId: 91, fileName: '91_0-15+92_0-21+93_1-11.json', startVerse: 0, endVerse: 15 },
  { surahId: 92, fileName: '91_0-15+92_0-21+93_1-11.json', startVerse: 0, endVerse: 21 },
  { surahId: 93, fileName: '91_0-15+92_0-21+93_1-11.json', startVerse: 1, endVerse: 11 },
  { surahId: 94, fileName: '94_0-8+95_0-8+96_0-19+97_0-5.json', startVerse: 0, endVerse: 8 },
  { surahId: 95, fileName: '94_0-8+95_0-8+96_0-19+97_0-5.json', startVerse: 0, endVerse: 8 },
  { surahId: 96, fileName: '94_0-8+95_0-8+96_0-19+97_0-5.json', startVerse: 0, endVerse: 19 },
  { surahId: 97, fileName: '94_0-8+95_0-8+96_0-19+97_0-5.json', startVerse: 0, endVerse: 5 },
  { surahId: 98, fileName: '98_0-8+99_0-8+100_0-11+101_0-11+102_0-8.json', startVerse: 0, endVerse: 8 },
  { surahId: 99, fileName: '98_0-8+99_0-8+100_0-11+101_0-11+102_0-8.json', startVerse: 0, endVerse: 8 },
  { surahId: 100, fileName: '98_0-8+99_0-8+100_0-11+101_0-11+102_0-8.json', startVerse: 0, endVerse: 11 },
  { surahId: 101, fileName: '98_0-8+99_0-8+100_0-11+101_0-11+102_0-8.json', startVerse: 0, endVerse: 11 },
  { surahId: 102, fileName: '98_0-8+99_0-8+100_0-11+101_0-11+102_0-8.json', startVerse: 0, endVerse: 8 },
  { surahId: 103, fileName: '103_0-3+104_0-9+105_0-5+106_0-4+107_0-7+108_0-3+109_0-6+110_0-3.json', startVerse: 0, endVerse: 3 },
  { surahId: 104, fileName: '103_0-3+104_0-9+105_0-5+106_0-4+107_0-7+108_0-3+109_0-6+110_0-3.json', startVerse: 0, endVerse: 9 },
  { surahId: 105, fileName: '103_0-3+104_0-9+105_0-5+106_0-4+107_0-7+108_0-3+109_0-6+110_0-3.json', startVerse: 0, endVerse: 5 },
  { surahId: 106, fileName: '103_0-3+104_0-9+105_0-5+106_0-4+107_0-7+108_0-3+109_0-6+110_0-3.json', startVerse: 0, endVerse: 4 },
  { surahId: 107, fileName: '103_0-3+104_0-9+105_0-5+106_0-4+107_0-7+108_0-3+109_0-6+110_0-3.json', startVerse: 0, endVerse: 7 },
  { surahId: 108, fileName: '103_0-3+104_0-9+105_0-5+106_0-4+107_0-7+108_0-3+109_0-6+110_0-3.json', startVerse: 0, endVerse: 3 },
  { surahId: 109, fileName: '103_0-3+104_0-9+105_0-5+106_0-4+107_0-7+108_0-3+109_0-6+110_0-3.json', startVerse: 0, endVerse: 6 },
  { surahId: 110, fileName: '103_0-3+104_0-9+105_0-5+106_0-4+107_0-7+108_0-3+109_0-6+110_0-3.json', startVerse: 0, endVerse: 3 },
  { surahId: 111, fileName: '111_0-5+112_0-4+113_0-5+114_0-6.json', startVerse: 0, endVerse: 5 },
  { surahId: 112, fileName: '111_0-5+112_0-4+113_0-5+114_0-6.json', startVerse: 0, endVerse: 4 },
  { surahId: 113, fileName: '111_0-5+112_0-4+113_0-5+114_0-6.json', startVerse: 0, endVerse: 5 },
  { surahId: 114, fileName: '111_0-5+112_0-4+113_0-5+114_0-6.json', startVerse: 0, endVerse: 6 },
];

// Evrensel mapping sistemi - bookId'ye bakmadan aynı mapping'i döndür
function getHardcodedMappings(_bookId: string): TafsirFileMapping[] {
  // Tüm kitaplar için aynı evrensel mapping'i kullan
  // Bu çok daha verimli ve mantıklı
  return UNIVERSAL_TAFSIR_MAPPINGS;
}



// Cache for book mappings
const bookMappingsCache = new Map<string, TafsirFileMapping[]>();

// Ana fonksiyon - kitap için mapping'leri getir
export async function getTafsirMappingsForBook(bookId: string): Promise<TafsirFileMapping[]> {
  const cacheKey = `tafsir_mappings_${bookId}`;

  // Cache'de varsa direkt döndür (performans optimizasyonu)
  if (bookMappingsCache.has(cacheKey)) {
    return bookMappingsCache.get(cacheKey)!;
  }

  const mappings = await generateMappingsForBook(bookId);

  // Cache'e kaydet ama maksimum cache size'ına dikkat et
  if (bookMappingsCache.size < 10) { // Max 10 kitap cache'de tut
    bookMappingsCache.set(cacheKey, mappings);
  } else {
    // Cache doluysa en eski entry'yi sil
    const firstKey = bookMappingsCache.keys().next().value;
    if (firstKey) bookMappingsCache.delete(firstKey);
    bookMappingsCache.set(cacheKey, mappings);
  }

  return mappings;
}

// Cache temizleme fonksiyonu - tüm cache'leri temizler
export function clearAllCaches(): void {
  console.log('[clearAllCaches] Clearing all tafsir caches...');

  // Book mappings cache'ini temizle
  bookMappingsCache.clear();

  // Local storage'dan eski cache'leri temizle
  try {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('tafsir_')) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.warn('[clearAllCaches] Error clearing localStorage:', error);
  }

  console.log('[clearAllCaches] All caches cleared successfully');
}

// Eski fonksiyonu korumak için - tek bir sure için mapping'leri getir
export function getTafsirFilesForSurah(_surahId: number): TafsirFileMapping[] {
  // Bu fonksiyon artık kullanılmıyor, ama geriye dönük uyumluluk için var
  // Asıl kullanım getTafsirMappingsForBook ile birlikte olacak
  console.warn('getTafsirFilesForSurah is deprecated. Use getTafsirMappingsForBook instead.');
  return [];
}



// Sistem test fonksiyonu - tüm cache'leri temizler ve sistemi resetler
export async function resetTafsirSystem(): Promise<void> {
  console.log('[resetTafsirSystem] Starting tafsir system reset...');

  try {
    // 1. Tüm cache'leri temizle
    clearAllCaches();

    // 2. Hook cache'lerini temizle (eğer import edilebilirse)
    // Bu kısım runtime'da çalışacak
    if (typeof window !== 'undefined') {
      // Browser environment
      window.dispatchEvent(new CustomEvent('tafsirCacheReset'));
    }

    console.log('[resetTafsirSystem] Tafsir system reset completed successfully');

  } catch (error) {
    console.error('[resetTafsirSystem] Error during reset:', error);
    throw error;
  }
}

// Test fonksiyonu - mapping sisteminin çalıştığını kontrol eder
export async function testMappingSystem(bookId: string = '30'): Promise<boolean> {
  console.log(`[testMappingSystem] Testing mapping system for book ${bookId}...`);

  try {
    const mappings = await getTafsirMappingsForBook(bookId);

    if (!mappings || mappings.length === 0) {
      console.error(`[testMappingSystem] No mappings found for book ${bookId}`);
      return false;
    }

    // İlk surenin mapping'ini kontrol et
    const firstSurahMapping = mappings.find(m => m.surahId === 1);
    if (!firstSurahMapping) {
      console.error(`[testMappingSystem] No mapping found for Surah 1 in book ${bookId}`);
      return false;
    }

    console.log(`[testMappingSystem] ✅ Mapping system test passed for book ${bookId}`);
    console.log(`[testMappingSystem] Found ${mappings.length} mappings`);
    console.log(`[testMappingSystem] First surah mapping:`, firstSurahMapping);

    return true;

  } catch (error) {
    console.error(`[testMappingSystem] Test failed for book ${bookId}:`, error);
    return false;
  }
}

