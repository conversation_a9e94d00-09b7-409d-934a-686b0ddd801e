/**
 * Normalizes a string for searching by converting to lowercase,
 * handling special Turkish characters, and removing diacritics and some punctuation.
 * This makes search "forgiving" for users who don't type special characters.
 * e.g., "Ali imran" will match "Âl-i İmrân".
 * @param str The string to normalize.
 * @returns The normalized string.
 */
export const normalizeSearchTerm = (str: string): string => {
  if (!str) return '';

  // Use Turkish locale for correct handling of 'İ' -> 'i' and 'I' -> 'ı'.
  let normalized = str.toLocaleLowerCase('tr-TR');
  
  // Decompose combined characters (like 'â' -> 'a' + 'ˆ') and remove the diacritics.
  normalized = normalized.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  
  // Remove common punctuation that might interfere with search.
  normalized = normalized.replace(/[-'’.]/g, '');

  return normalized;
};

/**
 * A generic filter function that uses string normalization for robust searching.
 * It can search across multiple properties of an object.
 * @template T The type of items in the array.
 * @param {T[]} items The array of items to filter.
 * @param {string} query The search query.
 * @param {(item: T) => string[]} getKeys A function that takes an item and returns an array of strings to be searched against.
 * @returns {T[]} A filtered array of items.
 */
export const filterByNormalizedQuery = <T>(
  items: T[],
  query: string,
  getKeys: (item: T) => string[]
): T[] => {
  if (!query.trim()) {
    return items;
  }

  const normalizedQuery = normalizeSearchTerm(query);

  return items.filter(item => {
    const searchableValues = getKeys(item);
    // Check if any of the item's searchable values include the query
    return searchableValues.some(value => 
      normalizeSearchTerm(String(value)).includes(normalizedQuery)
    );
  });
};
