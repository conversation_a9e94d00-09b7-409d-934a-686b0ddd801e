import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Export URL and key for other services to use
export { supabaseUrl, supabaseAnonKey };

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase URL veya Anon Key bulunamadı!');
}

// Supabase client options
const options = {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    storageKey: 'ikra-kitabe-auth', // <PERSON>zel bir key kullanalım
  },
  global: {
    headers: {
      'X-Client-Info': 'ikra-kitabe-web-app'
    }
  }
};

// Supabase istemcisini oluştur
export const supabase = createClient(supabaseUrl, supabaseAnonKey, options);

// Başlangıçta aktif oturumu kontrol et
(async () => {
  const { data, error } = await supabase.auth.getSession();
  
  if (error) {
    console.error('[SupabaseClient] Error checking initial session:', error);
  } else {
    console.log('[SupabaseClient] Initial session check:', {
      hasSession: !!data.session,
      userId: data.session?.user?.id,
      expiresAt: data.session?.expires_at ? new Date(data.session.expires_at * 1000).toISOString() : 'undefined'
    });
  }
})(); 