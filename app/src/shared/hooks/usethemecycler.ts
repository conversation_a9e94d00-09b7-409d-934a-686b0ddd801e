import { useState } from 'react';
import { <PERSON>, <PERSON>, Coffee } from 'lucide-react';
import type { LucideIcon } from 'lucide-react';

export type Theme = 'dark' | 'light' | 'krem';

export interface ThemeColors {
  text: string;
  bg: string;
}

interface UseThemeCyclerReturn {
  theme: Theme;
  cycleTheme: () => void;
  themeIcon: LucideIcon;
}

export const useThemeCycler = (initialTheme: Theme = 'dark'): UseThemeCyclerReturn => {
  const [theme, setTheme] = useState<Theme>(initialTheme);

  // Theme colors for each theme
  const themeColors: Record<Theme, ThemeColors> = {
    dark: { text: '#ffffff', bg: '#121212' },
    light: { text: '#1d1d1f', bg: '#f5f5f7' },
    krem: { text: '#5B4636', bg: '#F7F2E2' }
  };

  // Next theme mapping for cycling
  const nextThemeMap: Record<Theme, Theme> = {
    dark: 'light',
    light: 'krem',
    krem: 'dark'
  };

  // Theme cycle function
  const cycleTheme = () => {
    const nextTheme = nextThemeMap[theme];
    
    setTheme(nextTheme);
    
    // Update CSS variables
    document.documentElement.style.setProperty('--text-color', themeColors[nextTheme].text);
    document.documentElement.style.setProperty('--bg-color', themeColors[nextTheme].bg);
  };

  // Get the appropriate icon based on current theme
  const getThemeIcon = (): LucideIcon => {
    switch (theme) {
      case 'dark':
        return Sun;
      case 'light':
        return Coffee;
      case 'krem':
        return Moon;
    }
  };

  return {
    theme,
    cycleTheme,
    themeIcon: getThemeIcon()
  };
}; 