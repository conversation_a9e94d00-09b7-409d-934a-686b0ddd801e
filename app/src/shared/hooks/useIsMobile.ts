import { useState, useEffect } from 'react';

const MOBILE_BREAKPOINT = 850; // Tailwind's md breakpoint

export function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkDeviceSize = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };

    // Initial check
    checkDeviceSize();

    // Listener for window resize
    window.addEventListener('resize', checkDeviceSize);

    // Cleanup listener on component unmount
    return () => {
      window.removeEventListener('resize', checkDeviceSize);
    };
  }, []); // Empty dependency array ensures this runs only on mount and unmount

  return isMobile;
}

export default useIsMobile; 