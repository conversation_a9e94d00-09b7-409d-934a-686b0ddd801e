import { useState, useCallback } from 'react';

interface IErrorToastState {
  message: string;
  id: string;
}

export const useErrorToast = () => {
  const [errors, setErrors] = useState<IErrorToastState[]>([]);

  const showError = useCallback((message: string) => {
    const id = Math.random().toString(36).substr(2, 9);
    setErrors(prev => [...prev, { message, id }]);
  }, []);

  const removeError = useCallback((id: string) => {
    setErrors(prev => prev.filter(error => error.id !== id));
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors([]);
  }, []);

  return {
    errors,
    showError,
    removeError,
    clearAllErrors
  };
};
