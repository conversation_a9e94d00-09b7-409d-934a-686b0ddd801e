import { useState, useEffect } from 'react';

const getDeviceType = (width: number): 'mobile' | 'desktop' => {
  if (width < 768) { // Tablet boyutunu (768px) sınır olarak alıyoruz
    return 'mobile';
  }
  return 'desktop';
};

export const useBreakpoint = (): 'mobile' | 'desktop' => {
  // Sayfa ilk yüklendiğinde doğru değeri ayarlamak için window.innerWidth'i kullan
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'desktop'>(() => getDeviceType(window.innerWidth));

  useEffect(() => {
    const handleResize = () => {
      setBreakpoint(getDeviceType(window.innerWidth));
    };

    // Pencere boyutu değiştiğinde breakpoint'i yeniden hesapla
    window.addEventListener('resize', handleResize);

    // Component unmount olduğunda event listener'ı temizle
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return breakpoint;
};
