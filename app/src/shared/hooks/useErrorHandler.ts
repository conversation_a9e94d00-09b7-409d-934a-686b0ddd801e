import { useState, useCallback } from 'react';

interface ErrorState {
  hasError: boolean;
  error: Error | null;
  errorType: 'network' | 'content' | 'auth' | 'permission' | 'general';
  errorContext?: string;
}

interface UseErrorHandlerReturn extends ErrorState {
  handleError: (error: unknown, context?: string, type?: ErrorState['errorType']) => void;
  clearError: () => void;
  isNetworkError: (error: unknown) => boolean;
  isAuthError: (error: unknown) => boolean;
}

/**
 * Hata yönetimi için özel hook.
 * Hataları yakalar, kategorize eder ve uygun şekilde işler.
 */
export function useErrorHandler(): UseErrorHandlerReturn {
  const [errorState, setErrorState] = useState<ErrorState>({
    hasError: false,
    error: null,
    errorType: 'general'
  });

  /**
   * <PERSON><PERSON> hatası olup olmadığını kontrol eder
   */
  const isNetworkError = useCallback((error: unknown): boolean => {
    if (error instanceof Error) {
      // Fetch API hataları
      if (
        error.message.includes('Failed to fetch') ||
        error.message.includes('Network request failed') ||
        error.message.includes('network') ||
        error.message.includes('ECONNREFUSED') ||
        error.message.includes('ETIMEDOUT') ||
        error.message.includes('ENOTFOUND')
      ) {
        return true;
      }
      
      // HTTP durum kodları
      if (
        error.message.includes('404') ||
        error.message.includes('500') ||
        error.message.includes('503')
      ) {
        return true;
      }
    }
    return false;
  }, []);

  /**
   * Kimlik doğrulama hatası olup olmadığını kontrol eder
   */
  const isAuthError = useCallback((error: unknown): boolean => {
    if (error instanceof Error) {
      // Supabase auth hataları
      if (
        error.message.includes('auth') ||
        error.message.includes('authentication') ||
        error.message.includes('token') ||
        error.message.includes('session') ||
        error.message.includes('login') ||
        error.message.includes('password') ||
        error.message.includes('unauthorized') ||
        error.message.includes('Unauthorized') ||
        error.message.includes('401')
      ) {
        return true;
      }
    }
    return false;
  }, []);

  /**
   * Hata türünü belirler
   */
  const determineErrorType = useCallback((error: unknown): ErrorState['errorType'] => {
    if (isNetworkError(error)) {
      return 'network';
    }
    
    if (isAuthError(error)) {
      return 'auth';
    }
    
    if (error instanceof Error) {
      // İzin hatası
      if (
        error.message.includes('permission') ||
        error.message.includes('access denied') ||
        error.message.includes('forbidden') ||
        error.message.includes('403')
      ) {
        return 'permission';
      }
      
      // İçerik hatası
      if (
        error.message.includes('content') ||
        error.message.includes('data') ||
        error.message.includes('load') ||
        error.message.includes('parse')
      ) {
        return 'content';
      }
    }
    
    return 'general';
  }, [isNetworkError, isAuthError]);

  /**
   * Hatayı işler ve state'i günceller
   */
  const handleError = useCallback((error: unknown, context?: string, type?: ErrorState['errorType']) => {
    console.error(`Error in ${context || 'application'}:`, error);
    
    const errorObj = error instanceof Error ? error : new Error(String(error));
    const errorType = type || determineErrorType(error);
    
    setErrorState({
      hasError: true,
      error: errorObj,
      errorType,
      errorContext: context
    });
  }, [determineErrorType]);

  /**
   * Hata durumunu temizler
   */
  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: null,
      errorType: 'general'
    });
  }, []);

  return {
    ...errorState,
    handleError,
    clearError,
    isNetworkError,
    isAuthError
  };
}

export default useErrorHandler;
