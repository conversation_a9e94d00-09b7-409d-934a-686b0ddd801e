import { useState, useEffect } from 'react';

// Responsive breakpoint hook'u (tek sefer setup, SSR-safe)
export function useResponsiveBreakpoint(breakpoint: number = 768) {
  const [isDesktop, setIsDesktop] = useState<boolean>(() => {
    // SSR-safe initial value
    if (typeof window === 'undefined') return true;
    return window.innerWidth >= breakpoint;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const mediaQuery = window.matchMedia(`(min-width: ${breakpoint}px)`);
    const handleChange = (e: MediaQueryListEvent) => setIsDesktop(e.matches);
    
    // Set initial value
    setIsDesktop(mediaQuery.matches);
    
    // Listen for changes
    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [breakpoint]);

  return isDesktop;
}
