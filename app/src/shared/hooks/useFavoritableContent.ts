import { useMemo } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import { useLibraryStore } from '@domains/library/store/librarystore';
import { IBook } from '@domains/library/models/types';

/**
 * Bu hook, mevcut URL'e ve uygulama durumuna göre favorilere eklenebilecek kitabı belirler.
 * Sad<PERSON>e bölüm/sure seçim sayfalarında çalışır, okuma sayfalarında `undefined` döner.
 * @returns {IBook | undefined} Favorilere eklenebilecek içerik nesnesi veya tanımsız.
 */
export const useFavoritableContent = (): IBook | undefined => {
  const { bookId } = useParams<{ bookId?: string }>();
  const location = useLocation();
  const books = useLibraryStore(state => state.books);

  const favoritableContent = useMemo(() => {
    const path = location.pathname;
    const pathSegments = path.split('/').filter(Boolean);

    // --- Okuma Sayfası Kontrolü ---
    // Eğer URL bir okuma sayfası formatındaysa (örn: /kuran/1, /risale/5/1),
    // favori butonu gösterilmemelidir.

    // Risale/Tefsir okuma sayfası kontrolü (örn: /risale/5/1 -> 3 segment)
    if ((path.startsWith('/risale') || path.startsWith('/tafsir')) && pathSegments.length > 2) {
      return undefined;
    }

    // Kuran okuma sayfası kontrolü (örn: /kuran/1 -> 2 segment ve ikincisi sayı)
    const isQuranPath = pathSegments[0] === 'kuran' || pathSegments[0] === 'mealli-kuran' || pathSegments[0] === 'kelime-mealli-kuran' || pathSegments[0] === 'kuran-meali';
    if (isQuranPath && pathSegments.length > 1 && !isNaN(Number(pathSegments[1]))) {
      return undefined;
    }

    // --- Seçim Sayfası Mantığı ---
    // Eğer okuma sayfası değilse, favorilenecek kitabı bul.

    // Kuran seçim sayfaları için doğru kitabı bul
    if (path.includes('/kelime-mealli-kuran')) {
      return books.find(b => b.id === 3);
    }
    if (path.includes('/mealli-kuran')) {
      return books.find(b => b.id === 2);
    }
    if (path.includes('/kuran-meali')) {
      return books.find(b => b.id === 4);
    }
    if (path.includes('/kuran')) {
      return books.find(b => b.id === 1);
    }

    // Risale/Tefsir seçim sayfaları için kitabı bul
    const currentBookId = bookId;
    if (!currentBookId) {
      return undefined;
    }

    const bookFromStore = books.find(b => b.id.toString() === currentBookId);
    if (bookFromStore) {
      return bookFromStore;
    }

    // Yedek nesne (veri yüklenirken)
    const categoryId = path.includes('tafsir') ? 4 : 3;
    const title = path.includes('tafsir') ? 'Tefsir' : 'Risale-i Nur';

    return {
      id: parseInt(currentBookId, 10),
      title: title,
      author: 'Yükleniyor...',
      description: '',
      cover_image: '',
      category_id: categoryId,
      is_available: true,
    };
  }, [bookId, books, location.pathname]);

  return favoritableContent;
};
