import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Hook that scrolls to top when route changes
 * @param dependencies - Additional dependencies to trigger scroll
 */
export const useScrollToTop = (dependencies: any[] = []) => {
  const location = useLocation();

  useEffect(() => {
    // Immediate scroll
    window.scrollTo(0, 0);

    // Delayed scroll to ensure DOM is ready
    const timeoutId = setTimeout(() => {
      window.scrollTo(0, 0);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [location.pathname, ...dependencies]);
};

/**
 * Function to manually scroll to top
 */
export const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

export default useScrollToTop;
