#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const ROOT = path.resolve(__dirname, '..');
const RISALE_CONTENT_DIR = path.join(ROOT, 'app', 'public', 'data', 'risalei_nur');
const DRY_RUN = process.argv.includes('--dry');

console.log('🧽 Removing sentence.tags from all Risale content files');
console.log(`→ Target dir: ${RISALE_CONTENT_DIR}`);
console.log(DRY_RUN ? '⏳ Dry run (no writes)\n' : '✍️  Will write changes\n');

function removeSentenceTags() {
  if (!fs.existsSync(RISALE_CONTENT_DIR)) {
    console.error('❌ Content directory not found:', RISALE_CONTENT_DIR);
    process.exit(1);
  }

  const bookDirs = fs
    .readdirSync(RISALE_CONTENT_DIR)
    .filter((dir) => fs.statSync(path.join(RISALE_CONTENT_DIR, dir)).isDirectory());

  let filesTouched = 0;
  let sentencesStripped = 0;

  for (const bookDir of bookDirs) {
    const contentDir = path.join(RISALE_CONTENT_DIR, bookDir, 'content');
    if (!fs.existsSync(contentDir)) continue;

    const contentFiles = fs.readdirSync(contentDir).filter((f) => f.endsWith('.json'));

    for (const file of contentFiles) {
      const contentPath = path.join(contentDir, file);
      try {
        const data = JSON.parse(fs.readFileSync(contentPath, 'utf8'));
        if (!Array.isArray(data.sentences)) continue;

        let modified = false;
        for (const s of data.sentences) {
          if (s && 'tags' in s) {
            delete s.tags;
            sentencesStripped++;
            modified = true;
          }
        }
        if (modified) {
          if (!DRY_RUN) {
            fs.writeFileSync(contentPath, JSON.stringify(data, null, 2));
          }
          filesTouched++;
          if (filesTouched % 50 === 0) {
            console.log(`✅ Updated ${filesTouched} files...`);
          }
        }
      } catch (e) {
        console.error(`❌ Error processing ${contentPath}:`, e.message);
      }
    }
  }

  console.log(`\n✅ Done. Files updated: ${filesTouched}, sentences stripped: ${sentencesStripped}`);
}

removeSentenceTags();
