#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Resolve paths relative to the repo root regardless of CWD
const ROOT = path.resolve(__dirname, '..');
const TAGS_DIR = path.join(ROOT, 'app', 'public', 'tags');

const DRY_RUN = process.argv.includes('--dry');

console.log('🧹 Cleaning tag files - removing book_id and section_id...');
console.log(`→ Target dir: ${TAGS_DIR}`);
console.log(DRY_RUN ? '⏳ Dry run (no writes)\n' : '✍️  Will write changes\n');

function cleanTagFiles() {
  if (!fs.existsSync(TAGS_DIR)) {
    console.error('❌ Tags directory not found:', TAGS_DIR);
    process.exit(1);
  }

  const files = fs
    .readdirSync(TAGS_DIR)
    .filter(
      (file) => file.endsWith('.json') && file !== 'index.json' && file !== 'reverse-index.json'
    )
    .sort((a, b) => parseInt(a) - parseInt(b));

  let cleanedFiles = 0;

  for (const file of files) {
    const filePath = path.join(TAGS_DIR, file);

    try {
      const raw = fs.readFileSync(filePath, 'utf8');
      const tagData = JSON.parse(raw);

      if (tagData.sentences && Array.isArray(tagData.sentences)) {
        // Remove book_id and section_id from each sentence
        tagData.sentences = tagData.sentences.map((sentence) => {
          const { book_id, section_id, ...rest } = sentence;
          return rest;
        });

        if (!DRY_RUN) {
          fs.writeFileSync(filePath, JSON.stringify(tagData, null, 2));
        }
        cleanedFiles++;

        if (cleanedFiles % 100 === 0) {
          console.log(`✅ Cleaned ${cleanedFiles} files...`);
        }
      }
    } catch (error) {
      console.error(`❌ Error cleaning ${file}:`, error.message);
    }
  }

  console.log(`✅ Cleaned ${cleanedFiles} tag files`);
}

// Main execution
cleanTagFiles();
console.log('\n🎉 Tag file cleaning completed!');
