#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Resolve paths relative to the repo root regardless of CWD
const ROOT = path.resolve(__dirname, '..');
const RISALE_TAG_DIR = path.join(ROOT, 'risale_tag_listesi');
const RISALE_CONTENT_DIR = path.join(ROOT, 'app', 'public', 'data', 'risalei_nur');
const TAGS_OUTPUT_DIR = path.join(ROOT, 'app', 'public', 'tags');
const V2_FILE = path.join(ROOT, 'v2.2.1.txt');

// Ensure output directory exists
if (!fs.existsSync(TAGS_OUTPUT_DIR)) {
  fs.mkdirSync(TAGS_OUTPUT_DIR, { recursive: true });
}

console.log('🚀 Risale Tag Processing Started (v2.0)...\n');

// Step 1: Parse v2.1.txt to get all unique tags with counts (filter out 1 adet)
function parseTagListFile() {
  console.log(`📖 Parsing ${path.basename(V2_FILE)} for tag list (filtering out 1 adet)...`);

  if (!fs.existsSync(V2_FILE)) {
    throw new Error(`v2.1.txt not found at ${V2_FILE}`);
  }

  const content = fs.readFileSync(V2_FILE, 'utf8');
  const lines = content.split(/\r?\n/);

  const tagMap = new Map();
  let tagId = 1;

  for (const rawLine of lines) {
    const line = rawLine.trim();
    if (!line) continue;
    // Skip comments or separators
    if (line.startsWith('#') || /^[-=]{3,}$/.test(line)) continue;

    // Parse lines like: "1. Temsil (1133 adet)"
    const match = line.match(/^\d+\.\s+(.+?)\s+\((\d+)\s+adet\)/);
    if (!match) continue;

    const tagName = match[1].trim();
    const count = parseInt(match[2], 10);

    if (count === 1) {
      // Skip tags with only 1 occurrence
      continue;
    }

    if (!tagMap.has(tagName)) {
      tagMap.set(tagName, { id: tagId++, name: tagName, count, sentences: [] });
    }
  }

  console.log(`✅ Found ${tagMap.size} tags (filtered out 1 adet tags)`);
  return tagMap;
}

// Step 2: Load all content files to get sentence data
function loadAllSentenceData() {
  console.log('📚 Loading all sentence data from content files...');

  if (!fs.existsSync(RISALE_CONTENT_DIR)) {
    throw new Error(`Content directory not found at ${RISALE_CONTENT_DIR}`);
  }

  const sentenceData = new Map(); // sentence_id -> full sentence object

  const bookDirs = fs
    .readdirSync(RISALE_CONTENT_DIR)
    .filter((dir) => fs.statSync(path.join(RISALE_CONTENT_DIR, dir)).isDirectory());

  let totalSentences = 0;

  for (const bookDir of bookDirs) {
    const contentDir = path.join(RISALE_CONTENT_DIR, bookDir, 'content');

    if (!fs.existsSync(contentDir)) continue;

    const contentFiles = fs.readdirSync(contentDir).filter((file) => file.endsWith('.json'));

    for (const contentFile of contentFiles) {
      const contentPath = path.join(contentDir, contentFile);

      try {
        const content = JSON.parse(fs.readFileSync(contentPath, 'utf8'));

        if (content.sentences) {
          for (const sentence of content.sentences) {
            if (sentence.id) {
              sentenceData.set(sentence.id, {
                ...sentence,
                book_id: bookDir,
                section_id: contentFile.replace('.json', ''),
              });
              totalSentences++;
            }
          }
        }
      } catch (error) {
        console.error(`❌ Error loading ${contentPath}:`, error.message);
      }
    }
  }

  console.log(`✅ Loaded ${totalSentences} sentences from content files`);
  return sentenceData;
}

// Step 3: Process tag files and collect sentence mappings with full data
function processTagFiles(tagMap, sentenceData) {
  console.log('📁 Processing risale_tag_listesi files...');

  if (!fs.existsSync(RISALE_TAG_DIR)) {
    throw new Error(`risale_tag_listesi not found at ${RISALE_TAG_DIR}`);
  }

  const files = fs
    .readdirSync(RISALE_TAG_DIR)
    .filter((file) => file.endsWith('.json') && !file.startsWith('.'))
  .sort((a, b) => parseInt(a, 10) - parseInt(b, 10));

  let totalProcessed = 0;

  for (const file of files) {
    const filePath = path.join(RISALE_TAG_DIR, file);

    try {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));

      if (!Array.isArray(data)) {
        console.warn(`⚠️  Skipping ${file} - not an array`);
        continue;
      }

      for (const item of data) {
        const sentenceId = item.id;
        const tags = item.tags || [];

        for (const tagName of tags) {
          if (tagMap.has(tagName)) {
            const fullSentence = sentenceData.get(sentenceId);
            if (fullSentence) {
              tagMap.get(tagName).sentences.push(fullSentence);
            } else {
              console.warn(`⚠️  Sentence not found: ${sentenceId}`);
            }
          }
        }

        totalProcessed++;
      }

      console.log(`✅ Processed ${file} (${data.length} sentences)`);
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }

  console.log(`✅ Total sentences processed: ${totalProcessed}\n`);
  return tagMap;
}

// Step 4: Create tag index file
function createTagIndex(tagMap) {
  console.log('📝 Creating tag index file...');

  const index = {};
  const reverseIndex = {};

  for (const [tagName, tagData] of tagMap) {
    index[tagData.id] = tagName;
    reverseIndex[tagName] = tagData.id;
  }

  // Save main index
  fs.writeFileSync(path.join(TAGS_OUTPUT_DIR, 'index.json'), JSON.stringify(index, null, 2));

  // Save reverse index for easy lookup
  fs.writeFileSync(
    path.join(TAGS_OUTPUT_DIR, 'reverse-index.json'),
    JSON.stringify(reverseIndex, null, 2)
  );

  console.log('✅ Tag index files created');
  return reverseIndex;
}

// Step 5: Create individual tag files with full sentence data
function createTagFiles(tagMap) {
  console.log('📁 Creating individual tag files with full sentence data...');

  let createdFiles = 0;

  for (const [tagName, tagData] of tagMap) {
    const tagFile = {
      id: tagData.id,
      name: tagName,
      count: tagData.sentences.length,
      sentences: tagData.sentences.map((sentence) => ({
        id: sentence.id,
        text: sentence.text,
        type: sentence.type,
        alignment: sentence.alignment,
        paragraph_start: sentence.paragraph_start,
        verse_break_after: sentence.verse_break_after,
        line_break: sentence.line_break,
        divider_after: sentence.divider_after,
        role: sentence.role,
        book_id: sentence.book_id,
        section_id: sentence.section_id,
      })),
    };

    const filename = `${tagData.id}.json`;
    fs.writeFileSync(path.join(TAGS_OUTPUT_DIR, filename), JSON.stringify(tagFile, null, 2));

    createdFiles++;

    if (createdFiles % 100 === 0) {
      console.log(`✅ Created ${createdFiles} tag files...`);
    }
  }

  console.log(`✅ Created ${createdFiles} tag files`);
}

// Step 6: Update sentence data with tag IDs (only for tags with >1 occurrence)
function updateSentenceData(reverseIndex) {
  console.log('🔄 Updating sentence data with tag IDs...');

  const files = fs
    .readdirSync(RISALE_TAG_DIR)
    .filter((file) => file.endsWith('.json') && !file.startsWith('.'))
  .sort((a, b) => parseInt(a, 10) - parseInt(b, 10));

  // Create sentence ID to tag IDs mapping
  const sentenceTagMap = new Map();

  for (const file of files) {
    const filePath = path.join(RISALE_TAG_DIR, file);

    try {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));

      if (!Array.isArray(data)) continue;

      for (const item of data) {
        const sentenceId = item.id;
        const tags = item.tags || [];

        const tagIds = tags
          .map((tagName) => reverseIndex[tagName])
          .filter((id) => id !== undefined); // Only include tags that weren't filtered out

        if (tagIds.length > 0) {
          sentenceTagMap.set(sentenceId, tagIds);
        }
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }

  console.log(`✅ Mapped ${sentenceTagMap.size} sentences to tag IDs`);

  // Now update the actual content files
  updateContentFiles(sentenceTagMap);
}

// Helper function to update content files
function updateContentFiles(sentenceTagMap) {
  console.log('📝 Updating content files...');

  const bookDirs = fs
    .readdirSync(RISALE_CONTENT_DIR)
    .filter((dir) => fs.statSync(path.join(RISALE_CONTENT_DIR, dir)).isDirectory());

  let updatedFiles = 0;

  for (const bookDir of bookDirs) {
    const contentDir = path.join(RISALE_CONTENT_DIR, bookDir, 'content');

    if (!fs.existsSync(contentDir)) continue;

    const contentFiles = fs.readdirSync(contentDir).filter((file) => file.endsWith('.json'));

    for (const contentFile of contentFiles) {
      const contentPath = path.join(contentDir, contentFile);

      try {
        const content = JSON.parse(fs.readFileSync(contentPath, 'utf8'));

        if (content.sentences) {
          let sentenceUpdated = false;

          for (const sentence of content.sentences) {
            if (sentence.id) {
              const tagIds = sentenceTagMap.get(sentence.id);
              if (tagIds && tagIds.length > 0) {
                sentence.tags = tagIds;
                sentenceUpdated = true;
              }
            }
          }

          if (sentenceUpdated) {
            fs.writeFileSync(contentPath, JSON.stringify(content, null, 2));
            updatedFiles++;
          }
        }
      } catch (error) {
        console.error(`❌ Error updating ${contentPath}:`, error.message);
      }
    }
  }

  console.log(`✅ Updated ${updatedFiles} content files`);
}

// Main execution
async function main() {
  try {
    // Step 1: Parse tag list (filter out 1 adet)
    const tagMap = parseTagListFile();

    // Step 2: Load all sentence data
    const sentenceData = loadAllSentenceData();

    // Step 3: Process tag files with full sentence data
    const processedTagMap = processTagFiles(tagMap, sentenceData);

    // Step 4: Create index
    const reverseIndex = createTagIndex(processedTagMap);

    // Step 5: Create tag files with full data
    createTagFiles(processedTagMap);

    // Step 6: Update sentence data
    updateSentenceData(reverseIndex);

    console.log('\n🎉 Tag processing completed successfully!');
    console.log(`📊 Statistics:`);
    console.log(`   - Total tags (>1 adet): ${tagMap.size}`);
    console.log(`   - Tag files created: ${tagMap.size}`);
    console.log(`   - Index files: 2`);
  } catch (error) {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  }
}

// Run the script
main();
