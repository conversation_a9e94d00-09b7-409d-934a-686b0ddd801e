export interface Env {
  ASSETS: Fetcher; // static assets binding
  R2_BUCKET: R2Bucket;
  // API_KEY removed from public R2 reads
  CORS_ALLOWED_ORIGINS?: string; // comma-separated
  REQUIRE_ORIGIN?: string; // 'true' to block requests without Origin
}

const CACHE_CONTROL = "public, max-age=300, s-maxage=86400"; // 5m browser, 1d edge
const RATE_LIMIT_WINDOW_MS = 60_000; // 1 minute
const RATE_LIMIT_MAX = 120; // 120 requests/IP/min (soft)
const rateMap = new Map<string, { count: number; resetAt: number }>();

const ALLOWED_PREFIXES = [
  "library/",
  "quran/",
];

function parseAllowedOrigins(env: Env): Set<string> {
  const list = (env.CORS_ALLOWED_ORIGINS || "").split(",").map(s => s.trim()).filter(Boolean);
  return new Set(list);
}

function corsHeaders(req: Request, env: Env): Headers {
  const origin = req.headers.get("Origin");
  const allowed = parseAllowedOrigins(env);
  const requestedHeaders = req.headers.get("Access-Control-Request-Headers");

  const headers = new Headers({
    "Access-Control-Allow-Methods": "GET,HEAD,OPTIONS",
    "Access-Control-Max-Age": "86400",
    "Vary": "Origin",
  });

  // Echo requested headers for preflight to avoid case-sensitivity issues
  if (requestedHeaders) {
    headers.set("Access-Control-Allow-Headers", requestedHeaders);
  } else {
    headers.set("Access-Control-Allow-Headers", "X-Api-Key,Content-Type,Accept");
  }

  if (allowed.has("*")) {
    headers.set("Access-Control-Allow-Origin", "*");
  } else if (origin && allowed.has(origin)) {
    headers.set("Access-Control-Allow-Origin", origin);
  }

  return headers;
}

function clientIp(req: Request): string {
  return (
    req.headers.get("CF-Connecting-IP") ||
    req.headers.get("X-Forwarded-For") ||
    "unknown"
  );
}

function rateLimitAllow(req: Request): boolean {
  const ip = clientIp(req);
  const now = Date.now();
  const rec = rateMap.get(ip);
  if (!rec || now >= rec.resetAt) {
    rateMap.set(ip, { count: 1, resetAt: now + RATE_LIMIT_WINDOW_MS });
    return true;
  }
  if (rec.count < RATE_LIMIT_MAX) {
    rec.count += 1;
    return true;
  }
  return false;
}

function isAllowedKey(key: string): boolean {
  const safe = key.replace(/^\/+/, "");
  return ALLOWED_PREFIXES.some((p) => safe.startsWith(p));
}

function requireOrigin(env: Env): boolean {
  const val = (env.REQUIRE_ORIGIN || "").toLowerCase();
  return val === "true" || val === "1" || val === "yes";
}

function isOriginAllowed(req: Request, env: Env): boolean {
  const allowed = parseAllowedOrigins(env);
  const origin = req.headers.get("Origin");

  if (origin) {
    if (allowed.has("*")) return true;
    return allowed.has(origin);
  }

  // No Origin header. If strict mode: allow only same-origin browser navigations/fetches
  if (requireOrigin(env)) {
    const sfs = (req.headers.get("Sec-Fetch-Site") || "").toLowerCase();
    if (sfs === "same-origin") return true; // browser request from our own pages
    return false; // block server-side/curl or cross-site
  }

  // Not strict: allow
  return true;
}

function inferContentType(key: string): string | undefined {
  const lower = key.toLowerCase();
  if (lower.endsWith(".json")) return "application/json; charset=utf-8";
  if (lower.endsWith(".txt")) return "text/plain; charset=utf-8";
  if (lower.endsWith(".html")) return "text/html; charset=utf-8";
  if (lower.endsWith(".css")) return "text/css; charset=utf-8";
  if (lower.endsWith(".js")) return "application/javascript; charset=utf-8";
  if (lower.endsWith(".png")) return "image/png";
  if (lower.endsWith(".jpg") || lower.endsWith(".jpeg")) return "image/jpeg";
  if (lower.endsWith(".svg")) return "image/svg+xml";
  return undefined;
}

async function handleOptions(req: Request, env: Env): Promise<Response> {
  return new Response(null, { status: 204, headers: corsHeaders(req, env) });
}

async function handleHead(key: string, req: Request, env: Env): Promise<Response> {
  const head = await env.R2_BUCKET.head(key);
  if (!head) return new Response("Not Found", { status: 404, headers: corsHeaders(req, env) });

  const headers = new Headers(corsHeaders(req, env));
  headers.set("Accept-Ranges", "bytes");
  headers.set("Cache-Control", CACHE_CONTROL);
  if (head.httpMetadata?.contentType) headers.set("Content-Type", head.httpMetadata.contentType);
  else {
    const inferred = inferContentType(key);
    if (inferred) headers.set("Content-Type", inferred);
  }
  if (head.etag) headers.set("ETag", head.etag);
  if (head.uploaded) headers.set("Last-Modified", new Date(head.uploaded).toUTCString());
  headers.set("Content-Length", String(head.size ?? 0));
  return new Response(null, { status: 200, headers });
}

async function handleGet(key: string, req: Request, env: Env): Promise<Response> {
  // Conditional request
  const ifNoneMatch = req.headers.get("If-None-Match");
  const obj = await env.R2_BUCKET.get(key, { onlyIf: ifNoneMatch ? { etagMatches: ifNoneMatch } : undefined });
  if (!obj) {
    // Could be 304 (Not Modified) or 404; `get` returns null when condition fails
    if (ifNoneMatch) return new Response(null, { status: 304, headers: corsHeaders(req, env) });
    return new Response("Not Found", { status: 404, headers: corsHeaders(req, env) });
  }

  const headers = new Headers(corsHeaders(req, env));
  headers.set("Accept-Ranges", "bytes");
  headers.set("Cache-Control", CACHE_CONTROL);
  if (obj.httpMetadata?.contentType) headers.set("Content-Type", obj.httpMetadata.contentType);
  else {
    const inferred = inferContentType(key);
    if (inferred) headers.set("Content-Type", inferred);
  }
  if (obj.etag) headers.set("ETag", obj.etag);
  if (obj.uploaded) headers.set("Last-Modified", new Date(obj.uploaded).toUTCString());

  return new Response(obj.body, { status: 200, headers });
}

const API_PREFIX = "/api/";
const R2_PREFIX = "/api/r2/";

export default {
  async fetch(req, env): Promise<Response> {
    const url = new URL(req.url);

    // CORS preflight early return for any origin (safe)
    if (req.method === "OPTIONS") return handleOptions(req, env);

    // Health check
    if (url.pathname === "/api/health") {
      return new Response(JSON.stringify({ ok: true, time: new Date().toISOString() }), {
        status: 200,
        headers: { "Content-Type": "application/json; charset=utf-8" },
      });
    }

    // Route: R2 API under /api/r2/*
    if (url.pathname.startsWith(R2_PREFIX)) {
      if (!rateLimitAllow(req)) {
        const h = corsHeaders(req, env);
        h.set("Retry-After", String(Math.ceil(RATE_LIMIT_WINDOW_MS / 1000)));
        return new Response("Too Many Requests", { status: 429, headers: h });
      }

      const keyRaw = url.pathname.slice(R2_PREFIX.length);
      const key = keyRaw.replace(/^\/+/, "");
      if (!key) return new Response("Bad Request: key required", { status: 400, headers: corsHeaders(req, env) });

      // Block browser requests from disallowed origins
      if (!isOriginAllowed(req, env)) {
        return new Response("Forbidden", { status: 403, headers: corsHeaders(req, env) });
      }

      switch (req.method) {
        case "HEAD":
          return handleHead(key, req, env);
        case "GET":
          return handleGet(key, req, env);
        default:
          return new Response("Method Not Allowed", { status: 405, headers: corsHeaders(req, env) });
      }
    }

    // Otherwise: serve static assets (SPA)
    return env.ASSETS.fetch(req);
  },
} satisfies ExportedHandler<Env>;

