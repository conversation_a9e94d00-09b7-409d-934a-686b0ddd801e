## İkra Kitabe – Cloudflare Workers + R2 Mi<PERSON><PERSON>

### TL;DR
- <PERSON><PERSON> bir Cloudflare Worker hem SPA (static assets) hem de API’yi servis eder.
- R2 bucket private; Worker proxy üzerinden public GET eri<PERSON><PERSON><PERSON> sağlanır.
- Frontend, aynı origin’den sadece relative path ile çağırır: `/api/r2/...`.
- Güvenlik: CORS allowlist + Origin zorunluluğu (server‑side hotlink blok) + rate limit + HTTP caching.
- Geliştirme: Vite dev server + Wrangler dev; Production: `npm run build` + `wrangler deploy`.

### Bileşenler
- Cloudflare Worker (adı: `ikrakitabe`)
  - Static assets binding: `app/dist` SPA dosyaları servis edilir.
  - API Routes: `/api/*` altında Worker kodu çalışır.
- R2 Bucket: `ikra-kitabe-data`
  - Private; sadece Worker’ın R2 binding’i ile okunur.
- Frontend (Vite + React)
  - API çağrıları same‑origin relative path: `/api/r2/...`
  - <PERSON><PERSON><PERSON> bir sabit Worker URL’i veya API key’e ihtiyaç yok.

### Güvenlik
- CORS allowlist (wrangler.toml → `CORS_ALLOWED_ORIGINS`):
  - `http://localhost:5173`, `http://127.0.0.1:5173`, `https://ikrakitabe.keremkozyigit.workers.dev`
- Origin zorunluluğu (wrangler.toml → `REQUIRE_ORIGIN=true`):
  - Origin header’ı olmayan server‑side istekler 403.
  - Tarayıcı bazı same‑origin isteklerde Origin göndermeyebilir; bu durumda `Sec-Fetch-Site: same-origin` istisnası tanımlandı.
- Rate limit: 120 istek/IP/dk (yumuşak limit).
- HTTP Caching: `ETag`/`Last-Modified` + `Cache-Control` ile 304 destekli.

---

## Geliştirme ve Yayın Akışı

### Development
1) Worker’ı çalıştırın:
   - `cd worker`
   - `wrangler dev`
2) Frontend’i çalıştırın:
   - `cd app`
   - `npm run dev`
3) Vite dev server, `/api` isteklerini 8787’deki Worker’a proxy’ler.

Not: @cloudflare/vite-plugin dev’de wrangler config arar. App klasöründe wrangler.toml yoksa hata verir. Biz plugin’i dev’de devre dışı bıraktık (gerekmiyor). İsterseniz plugin’i kullanmak için `app/vite.config.ts` içinde worker wrangler yolunu açıkça geçebilirsiniz.

### Production
1) SPA’yi build edin:
   - `cd app && npm run build`
2) Worker’ı deploy edin:
   - `cd worker && wrangler deploy`
3) Production URL: `https://ikrakitabe.keremkozyigit.workers.dev`

---

## Environment Variables

### Frontend (app/.env.local)
Gerekli olanlar:
```
VITE_SUPABASE_URL=...supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOi...

# Opsiyonel
VITE_DEBUG=true
```
Kaldırılmış/değil:
- `VITE_WORKER_URL` → Gerekmiyor (relative path)
- `VITE_API_KEY` → Gerekmiyor (public GET, sunucu tarafında CORS/origin kontrolü var)

### Worker (worker/wrangler.toml)
Önemli kısımlar:
```
name = "ikrakitabe"
main = "src/index.ts"
compatibility_date = "2024-10-01"

[assets]
directory = "../app/dist"
binding = "ASSETS"
not_found_handling = "single-page-application"
run_worker_first = [ "/api/*" ]

[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "ikra-kitabe-data"

[vars]
CORS_ALLOWED_ORIGINS = "http://localhost:5173,http://127.0.0.1:5173,https://ikrakitabe.keremkozyigit.workers.dev"
REQUIRE_ORIGIN = "true"
```

---

## Router & API Yüzeyi
- SPA: `GET /` ve asset’ler → `ASSETS` binding ile servis edilir.
- Healthcheck: `GET /api/health` → `{ ok: true, time: ... }` döner.
- R2 Proxy: `/api/r2/<key>`
  - Desteklenen yöntemler: `GET`, `HEAD`, `OPTIONS`
  - `Content-Type` inference, `ETag/Last-Modified`, `Cache-Control` başlıkları eklenir.
  - Örnek: `/api/r2/library/books.json`

---

## Worker Adı Değiştirme (Rename)
- `worker/wrangler.toml` içinde `name` değerini değiştirin (örn. `ikrakitabe`).
- `CORS_ALLOWED_ORIGINS` içindeki eski hostu yeni workers.dev hostu ile değiştirin.
- `wrangler deploy` ile yayınlayın.
- Frontend kodu değişmez (relative path kullanıldığı için).

---

## Custom Domain (İsteğe Bağlı)
- Cloudflare panelinden `app.sizinalanadiniz.com` gibi bir hostu Worker’a bağlayın.
- `CORS_ALLOWED_ORIGINS`’u sadece bu domainde sabitleyin; workers.dev’i kaldırabilirsiniz.
- Frontend yine relative path kullandığı için kod değişmez.

---

## Sorun Giderme
- Vite dev çalışırken “No config file found … wrangler.(json|toml)” hatası:
  - Sebep: `@cloudflare/vite-plugin` app dizininde wrangler config arar.
  - Çözüm 1 (önerilen): Plugin’i dev’de devre dışı bırakın (mevcut ayar).
  - Çözüm 2: Plugin’e worker wrangler yolunu verin (örn. `../worker/wrangler.toml`).
- 403 Forbidden:
  - CORS allowlist’e kendi origin’inizin eklendiğinden emin olun.
  - Server‑side isteklerde Origin gelmez; `REQUIRE_ORIGIN=true` ile 403 normaldir.
  - Tarayıcı same‑origin istekleri `Sec-Fetch-Site: same-origin` ile kabul edilir.
- Bağlantı reset:
  - Deploy sonrası DNS/cache birkaç dakika gecikebilir; tarayıcıdan deneyin.

---

## Komut Hızlı Rehber
- Dev:
  - `cd worker && wrangler dev`
  - `cd app && npm run dev`
- Prod:
  - `cd app && npm run build`
  - `cd worker && wrangler deploy`

---

Güncel Worker URL: `https://ikrakitabe.keremkozyigit.workers.dev`

