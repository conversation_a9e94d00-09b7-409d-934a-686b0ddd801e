## İkra Kitabe – Cloudflare Workers + R2 Mi<PERSON><PERSON>

### TL;DR
- <PERSON><PERSON> bir Cloudflare Worker hem SPA (static assets) hem de API’yi servis eder.
- R2 bucket private; Worker proxy üzerinden public GET eri<PERSON><PERSON><PERSON> sağlanır.
- Frontend, aynı origin’den sadece relative path ile çağırır: `/api/r2/...`.
- Güvenlik: CORS allowlist + Origin zorunluluğu (server‑side hotlink blok) + rate limit + HTTP caching.
- Geliştirme: Vite dev server + Wrangler dev; Production: `npm run build` + `wrangler deploy`.

### Bileşenler
- Cloudflare Worker (adı: `ikrakitabe`)
  - Static assets binding: `app/dist` SPA dosyaları servis edilir.
  - API Routes: `/api/*` altında Worker kodu çalışır.
- R2 Bucket: `ikra-kitabe-data`
  - Private; sadece Worker’ın R2 binding’i ile okunur.
- Frontend (Vite + React)
  - API çağrıları same‑origin relative path: `/api/r2/...`
  - <PERSON><PERSON><PERSON> bir sabit Worker URL’i veya API key’e ihtiyaç yok.

### Güvenlik
- CORS allowlist (wrangler.toml → `CORS_ALLOWED_ORIGINS`):
  - `http://localhost:5173`, `http://127.0.0.1:5173`, `https://ikrakitabe.keremkozyigit.workers.dev`
- Origin zorunluluğu (wrangler.toml → `REQUIRE_ORIGIN=true`):
  - Origin header’ı olmayan server‑side istekler 403.
  - Tarayıcı bazı same‑origin isteklerde Origin göndermeyebilir; bu durumda `Sec-Fetch-Site: same-origin` istisnası tanımlandı.
- Rate limit: 120 istek/IP/dk (yumuşak limit).
- HTTP Caching: `ETag`/`Last-Modified` + `Cache-Control` ile 304 destekli.

---

## Geliştirme ve Yayın Akışı

### Development
1) Worker’ı çalıştırın:
   - `cd worker`
   - `wrangler dev`
2) Frontend’i çalıştırın:
   - `cd app`
   - `npm run dev`
3) Vite dev server, `/api` isteklerini 8787’deki Worker’a proxy’ler.

Not: @cloudflare/vite-plugin dev’de wrangler config arar. App klasöründe wrangler.toml yoksa hata verir. Biz plugin’i dev’de devre dışı bıraktık (gerekmiyor). İsterseniz plugin’i kullanmak için `app/vite.config.ts` içinde worker wrangler yolunu açıkça geçebilirsiniz.

### Production
1) SPA’yi build edin:
   - `cd app && npm run build`
2) Worker’ı deploy edin:
   - `cd worker && wrangler deploy`
3) Production URL: `https://ikrakitabe.keremkozyigit.workers.dev`

---
---

## Remote Dev (Preview R2) – Neden ve Nasıl?
Wrangler `dev --remote` veya `--x-remote-bindings` ile gerçek Cloudflare kaynaklarına bağlanırken production R2 yerine ayrı bir "preview/dev" bucket kullanmanız istenir. Bu, geliştirme sırasında prod verisini etkilememek içindir.

Adımlar:
1) Yeni dev bucket oluşturun (örnek ad):
```
cd worker
wrangler r2 bucket create ikra-kitabe-data-dev
```
2) wrangler.toml zaten aşağıdaki gibi ayarlı:
```
[[r2_buckets]]
Binding = "R2_BUCKET"
bucket_name = "ikra-kitabe-data"
preview_bucket_name = "ikra-kitabe-data-dev"
```
3) Prod → Dev seed (en azından gerekli JSON’ları taşıyın):
```
wrangler r2 object get ikra-kitabe-data library/books.json --file /tmp/books.json
wrangler r2 object get ikra-kitabe-data library/categories.json --file /tmp/categories.json
wrangler r2 object put ikra-kitabe-data-dev library/books.json --file /tmp/books.json
wrangler r2 object put ikra-kitabe-data-dev library/categories.json --file /tmp/categories.json
```
4) Remote dev’i çalıştırın:
```
# Yeni beta akış (önerilen):
wrangler dev --x-remote-bindings
# veya klasik:
wrangler dev --remote
```

Alternatif (tamamen lokal): `wrangler dev --persist-to .mf` ile lokal R2 emülasyonu kullanıp gerekli dosyaları bir defa içeri yükleyebilirsiniz.

---

## R2 “Location Hint” (Performans)
Bucket oluştururken hedef kullanıcı kitlenize uygun bir location hint seçmek, R2 erişimini hızlandırır (ör. `weur` Batı Avrupa, `enam` Kuzey Amerika doğu). Mevcut bucket için sonradan değiştirilemez; yeni bucket açarken seçilir. Dev bucket’ı oluştururken uygun hint’i seçebilirsiniz.

Öneri: Kullanıcılarınız çoğunlukla TR/EU ise `weur`.

---

## Cloudflare Vite Plugin – Doğru Yapılandırma
- Varsayılan: Dev’de kapalı. Çünkü app/ içinde wrangler config aradığı için gereksiz hataya yol açıyordu.
- İhtiyaç halinde: Env bayrağıyla açılabilir ve wrangler dosya yolu verilir.

app/vite.config.ts içinde yapılandırma:
```
import { cloudflare } from '@cloudflare/vite-plugin'
...
plugins: [
  react(),
  // Opt-in: CF_PLUGIN=1 npm run dev
  ...(process.env.CF_PLUGIN === '1'
    ? [cloudflare({ wrangler: { configPath: '../worker/wrangler.toml' } })]
    : []),
]
```

Gerekçe:
- Dev’de Vite proxy ile Worker’a bağlandığımız için plugin zorunlu değil.
- İstendiğinde tek komutla açılabilsin diye opt‑in hale getirildi.

---

## Cache ve Performans Notları
- Worker yanıtlarında `ETag/Last-Modified` + `Cache-Control` zaten mevcut.
- İleri seviye: Cloudflare panelinden Tiered Cache / Cache Reserve gibi özellikler tercihe göre açılabilir.


## Environment Variables

### Frontend (app/.env.local)
Gerekli olanlar:
```
VITE_SUPABASE_URL=...supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOi...

# Opsiyonel
VITE_DEBUG=true
```
Kaldırılmış/değil:
- `VITE_WORKER_URL` → Gerekmiyor (relative path)
- `VITE_API_KEY` → Gerekmiyor (public GET, sunucu tarafında CORS/origin kontrolü var)

### Worker (worker/wrangler.toml)
Önemli kısımlar:
```
name = "ikrakitabe"
main = "src/index.ts"
compatibility_date = "2024-10-01"

[assets]
directory = "../app/dist"
binding = "ASSETS"
not_found_handling = "single-page-application"
run_worker_first = [ "/api/*" ]

[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "ikra-kitabe-data"
preview_bucket_name = "ikra-kitabe-data-dev" # dev/preview bucket for remote dev

[vars]
CORS_ALLOWED_ORIGINS = "http://localhost:5173,http://127.0.0.1:5173,https://ikrakitabe.keremkozyigit.workers.dev"
REQUIRE_ORIGIN = "true"
```

---

## Router & API Yüzeyi
- SPA: `GET /` ve asset’ler → `ASSETS` binding ile servis edilir.
- Healthcheck: `GET /api/health` → `{ ok: true, time: ... }` döner.
- R2 Proxy: `/api/r2/<key>`
  - Desteklenen yöntemler: `GET`, `HEAD`, `OPTIONS`
  - `Content-Type` inference, `ETag/Last-Modified`, `Cache-Control` başlıkları eklenir.
  - Örnek: `/api/r2/library/books.json`

---

## Worker Adı Değiştirme (Rename)
- `worker/wrangler.toml` içinde `name` değerini değiştirin (örn. `ikrakitabe`).
- `CORS_ALLOWED_ORIGINS` içindeki eski hostu yeni workers.dev hostu ile değiştirin.
- `wrangler deploy` ile yayınlayın.
- Frontend kodu değişmez (relative path kullanıldığı için).

---

## Custom Domain (İsteğe Bağlı)
- Cloudflare panelinden `app.sizinalanadiniz.com` gibi bir hostu Worker’a bağlayın.
- `CORS_ALLOWED_ORIGINS`’u sadece bu domainde sabitleyin; workers.dev’i kaldırabilirsiniz.
- Frontend yine relative path kullandığı için kod değişmez.

---

## Sorun Giderme
- Vite dev çalışırken “No config file found … wrangler.(json|toml)” hatası:
  - Sebep: `@cloudflare/vite-plugin` app dizininde wrangler config arar.
  - Çözüm 1 (önerilen): Plugin’i dev’de devre dışı bırakın (mevcut ayar).
  - Çözüm 2: Plugin’e worker wrangler yolunu verin (örn. `../worker/wrangler.toml`).
- 403 Forbidden:
  - CORS allowlist’e kendi origin’inizin eklendiğinden emin olun.
  - Server‑side isteklerde Origin gelmez; `REQUIRE_ORIGIN=true` ile 403 normaldir.
  - Tarayıcı same‑origin istekleri `Sec-Fetch-Site: same-origin` ile kabul edilir.
- Bağlantı reset:
  - Deploy sonrası DNS/cache birkaç dakika gecikebilir; tarayıcıdan deneyin.

---

## Komut Hızlı Rehber
- Dev:
  - `cd worker && wrangler dev`
  - `cd app && npm run dev`
- Prod:
  - `cd app && npm run build`
  - `cd worker && wrangler deploy`

---

Güncel Worker URL: `https://ikrakitabe.keremkozyigit.workers.dev`

