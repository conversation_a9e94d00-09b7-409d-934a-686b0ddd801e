name = "ikrakitabe"
main = "src/index.ts"
compatibility_date = "2024-10-01"
account_id = "3da7d0ef941fbce03ce69823da1d9cd6"

# Static assets (serve app/dist via Worker)
[assets]
directory = "../app/dist"
binding = "ASSETS"
not_found_handling = "single-page-application"
run_worker_first = [ "/api/*" ]

# Bind the private R2 bucket
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "ikra-kitabe-data"
# jurisdiction = "default" # uncomment and set if you use a non-default jurisdiction like "eu"

[vars]
# Comma-separated origins allowed to call the worker (used for CORS)
CORS_ALLOWED_ORIGINS = "http://localhost:5173,http://127.0.0.1:5173,https://ikrakitabe.keremkozyigit.workers.dev"
# If true, block requests without an Origin header (prevents server-side hotlinking)
REQUIRE_ORIGIN = "true"
# Do NOT put API_KEY here. Use `wrangler secret put API_KEY` instead.

