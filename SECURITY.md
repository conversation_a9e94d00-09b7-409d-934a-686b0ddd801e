# 🔒 İkra Kitabe Güvenlik Rehberi

## 🚨 Kritik Güvenlik Uyarıları

### ❌ ASLA YAPMAYIN:
1. **Environment dosyalarını git'e commit etmeyin**
2. **API key'leri kod içinde hardcode etmeyin**  
3. **Production secret'larını client-side'da kullanmayın**
4. **CORS ayarlarını production'da `*` olarak bırakmayın**

### ✅ MUTLAKA YAPIN:
1. **Environment dosyalarını .gitignore'a ekleyin**
2. **Secret'ları environment variables'da tutun**
3. **RLS (Row Level Security) politikalarını aktif edin**
4. **API key'leri dü<PERSON> o<PERSON> rotate edin**

## 🛡️ Güvenlik Katmanları

### 1. Environment Variables Güvenliği

#### ✅ Güvenli Yapılandırma:
```bash
# .env.local (GIT'E COMMIT ETMEYİN!)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here

# Worker için ekstra VITE_ değişkenine gerek yok (same-origin /api/r2)

# Backend only (VITE_ prefix kullanmayın!)
R2_ACCESS_KEY_ID=your_secret_key
R2_SECRET_ACCESS_KEY=your_secret_key
```

#### ❌ Güvensiz Yapılandırma:
```bash
# ASLA böyle yapmayın!
VITE_R2_SECRET_KEY=secret_key  # Client'da görünür!
const API_KEY = "hardcoded_key"; // Kod içinde hardcode
```

### 2. Supabase Güvenliği

#### RLS (Row Level Security) Politikaları:
```sql
-- Kullanıcılar sadece kendi annotation'larını görebilir
CREATE POLICY "Users can view own annotations" 
ON text_annotations FOR SELECT 
USING (auth.uid() = user_id);

-- Kullanıcılar sadece kendi annotation'larını oluşturabilir  
CREATE POLICY "Users can create own annotations"
ON text_annotations FOR INSERT
WITH CHECK (auth.uid() = user_id);
```

#### Authentication Kontrolleri:
```typescript
// Her annotation işleminde auth kontrolü
const { data: { user }, error } = await supabase.auth.getUser();
if (!user) {
  throw new Error('Authentication required');
}
```

### 3. Cloudflare Worker Güvenliği

#### Origin Zorunluluf9 ve CORS:
```typescript
function isOriginAllowed(req: Request, env: Env): boolean {
  const allowed = new Set((env.CORS_ALLOWED_ORIGINS || '').split(',').map(s => s.trim()).filter(Boolean));
  const origin = req.headers.get('Origin');

  if (origin) {
    if (allowed.has('*')) return true;
    return allowed.has(origin);
  }

  // Origin yoksa sadece taray1c1 same-origin isteklerine izin ver
  const sfs = (req.headers.get('Sec-Fetch-Site') || '').toLowerCase();
  return sfs === 'same-origin';
}
```

#### CORS Konfigürasyonu:
```javascript
// Production için:
const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://yourdomain.com', // Sadece kendi domaininiz
  'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Accept',
};
```

### 4. Client-Side Güvenliği

#### Input Validation:
```typescript
// Tüm user input'larını validate edin
const validateAnnotationInput = (input: CreateAnnotationInput) => {
  if (!input.selected_text || input.selected_text.length > 10000) {
    throw new Error('Invalid text selection');
  }
  // XSS prevention
  const sanitized = DOMPurify.sanitize(input.annotation_content);
  return { ...input, annotation_content: sanitized };
};
```

#### Rate Limiting:
```typescript
// Client-side rate limiting
const rateLimiter = new Map();
const checkRateLimit = (userId: string) => {
  const now = Date.now();
  const userRequests = rateLimiter.get(userId) || [];
  const recentRequests = userRequests.filter(time => now - time < 60000);
  
  if (recentRequests.length >= 10) {
    throw new Error('Rate limit exceeded');
  }
  
  rateLimiter.set(userId, [...recentRequests, now]);
};
```

## 🔧 Güvenlik Kontrol Listesi

### Deployment Öncesi:
- [ ] Tüm environment dosyaları .gitignore'da
- [ ] Production secret'ları environment variables'da
- [ ] CORS ayarları production domain'e kısıtlı
- [ ] Origin zorunluluğu ve CORS allowlist aktif
- [ ] RLS politikaları test edildi
- [ ] Input validation tüm endpoint'lerde
- [ ] Rate limiting aktif
- [ ] Error handling güvenli (bilgi sızdırmıyor)

### Düzenli Kontroller:
- [ ] API key'leri rotate edildi (3 ayda bir)
- [ ] Supabase audit log'ları kontrol edildi
- [ ] Cloudflare security events incelendi
- [ ] Dependency'ler güvenlik açıkları için tarandı
- [ ] Access log'ları anormal aktivite için kontrol edildi

## 🚨 Güvenlik İhlali Durumunda

### Acil Müdahale:
1. **API key'leri derhal rotate edin**
2. **Supabase'de şüpheli session'ları sonlandırın**
3. **Cloudflare Worker'ı geçici olarak devre dışı bırakın**
4. **Audit log'larını inceleyin**
5. **Etkilenen kullanıcıları bilgilendirin**

### İletişim:
- Güvenlik sorunları için: <EMAIL>
- Acil durumlar için: +90 XXX XXX XXXX

## 📚 Ek Kaynaklar

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Supabase Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Cloudflare Security](https://developers.cloudflare.com/workers/platform/security/)
- [Vite Environment Variables](https://vitejs.dev/guide/env-and-mode.html)

---

**Son Güncelleme:** 2024-01-XX  
**Versiyon:** 1.0  
**Sorumlu:** Development Team
